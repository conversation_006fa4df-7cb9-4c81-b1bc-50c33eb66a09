import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

export default function PageContainer({
  children,
  scrollable = true,
  className
}: {
  children: React.ReactNode;
  scrollable?: boolean;
  className?: string;
}) {
    return (
    <>
      {scrollable ? (
        <ScrollArea className={cn('h-[calc(100vh-4rem)]', className)}>
          <div className="px-4 pb-4 pt-0 md:px-6">{children}</div>
        </ScrollArea>
      ) : (
        <div className={cn('h-full px-4 pb-4 pt-0 md:px-6', className)}>
          {children}
        </div>
      )}
    </>
  );
}
