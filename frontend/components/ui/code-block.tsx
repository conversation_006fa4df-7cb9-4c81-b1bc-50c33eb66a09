import React, { useEffect, useState } from 'react';
import { getHighlighter, type Highlighter } from 'shiki';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Copy, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CodeBlockProps {
  code: string;
  language?: string;
  className?: string;
  showCopyButton?: boolean;
  title?: string;
}

export function CodeBlock({
  code,
  language = 'text',
  className,
  showCopyButton = true,
  title
}: CodeBlockProps) {
  const { theme } = useTheme();
  const [highlighter, setHighlighter] = useState<Highlighter | null>(null);
  const [highlightedCode, setHighlightedCode] = useState<string>('');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const initHighlighter = async () => {
      try {
        const hl = await getHighlighter({
          themes: ['github-light', 'github-dark'],
          langs: [
            'javascript',
            'typescript',
            'python',
            'bash',
            'json',
            'yaml',
            'sql',
            'html',
            'css',
            'markdown',
            'text'
          ]
        });
        setHighlighter(hl);
      } catch (error) {
        console.error('Failed to initialize highlighter:', error);
      }
    };

    initHighlighter();
  }, []);

  useEffect(() => {
    if (highlighter && code) {
      try {
        const currentTheme = theme === 'dark' ? 'github-dark' : 'github-light';
        const highlighted = highlighter.codeToHtml(code, {
          lang: language,
          theme: currentTheme
        });
        setHighlightedCode(highlighted);
      } catch (error) {
        console.error('Failed to highlight code:', error);
        // Fallback to plain text
        setHighlightedCode(`<pre><code>${code}</code></pre>`);
      }
    }
  }, [highlighter, code, language, theme]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  if (!highlightedCode) {
    return (
      <div className={cn("relative max-w-full", className)}>
        <pre className="bg-muted/30 p-4 rounded-lg overflow-x-auto max-w-full whitespace-pre-wrap break-words">
          <code className="break-words">{code}</code>
        </pre>
      </div>
    );
  }

  return (
    <div className={cn("relative group max-w-full", className)}>
      {title && (
        <div className="bg-muted/50 px-4 py-2 text-sm font-medium border-b min-w-0">
          <span className="truncate block">{title}</span>
        </div>
      )}

      {showCopyButton && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      )}

      <div
        className="overflow-x-auto max-w-full [&>pre]:!bg-transparent [&>pre]:!p-4 [&>pre]:!m-0 [&>pre]:!max-w-full [&>pre]:!whitespace-pre-wrap [&>pre]:!break-words [&_code]:!break-words [&_code]:!whitespace-pre-wrap bg-muted/30 rounded-lg"
        dangerouslySetInnerHTML={{ __html: highlightedCode }}
      />
    </div>
  );
}

// Legacy compatibility component
export const SyntaxHighlighter = ({
  children,
  language,
  style,
  customStyle,
  PreTag = "div",
  className,
  ...props
}: any) => {
  const code = typeof children === 'string' ? children : String(children);

  return (
    <CodeBlock
      code={code}
      language={language}
      className={className}
      {...props}
    />
  );
};

// Prism compatibility export
export const Prism = SyntaxHighlighter;
