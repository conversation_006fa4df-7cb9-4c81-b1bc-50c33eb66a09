import { Button } from '@/components/ui/button';
import { Loader2, RotateCcw } from 'lucide-react';
import { ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface RefreshButtonProps extends Omit<ButtonProps, 'onClick'> {
  isRefreshing: boolean;
  onRefresh: () => void;
  refreshingText?: string;
  idleText?: string;
  className?: string;
  iconOnly?: boolean;
}

export function RefreshButton({
  isRefreshing,
  onRefresh,
  refreshingText = 'Refreshing...',
  idleText = 'Refresh',
  className,
  iconOnly = false,
  ...buttonProps
}: RefreshButtonProps) {
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onRefresh}
      disabled={isRefreshing || buttonProps.disabled}
      className={cn("gap-1", className)}
      {...buttonProps}
    >
      {isRefreshing ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RotateCcw className={`h-4 w-4 transition-transform duration-300 ${isRefreshing ? 'rotate-180' : ''}`} />
      )}
      {!iconOnly && (
        <span className={isRefreshing ? 'text-muted-foreground' : ''}>
          {isRefreshing ? refreshingText : idleText}
        </span>
      )}
    </Button>
  );
}
