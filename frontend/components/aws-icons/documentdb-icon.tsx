import { LucideProps } from 'lucide-react';

export function DocumentdbIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="documentdb-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#156AB3" />
          <stop offset="100%" stopColor="#2E73B8" />
        </linearGradient>
      </defs>
      <rect width="24" height="24" rx="4" fill="url(#documentdb-gradient)" />
      <g transform="translate(4, 4)">
        <rect x="2" y="2" width="12" height="12" rx="2" fill="white" opacity="0.2" />
        <rect x="3" y="3" width="10" height="10" rx="1" fill="white" opacity="0.9" />

        <rect x="5" y="5" width="6" height="1" rx="0.5" fill="url(#documentdb-gradient)" opacity="0.8" />
        <rect x="5" y="7" width="4" height="1" rx="0.5" fill="url(#documentdb-gradient)" opacity="0.7" />
        <rect x="5" y="9" width="5" height="1" rx="0.5" fill="url(#documentdb-gradient)" opacity="0.8" />
        <rect x="5" y="11" width="3" height="1" rx="0.5" fill="url(#documentdb-gradient)" opacity="0.6" />

        <circle cx="12" cy="6" r="1" fill="url(#documentdb-gradient)" opacity="0.7" />
        <circle cx="12" cy="9" r="1" fill="url(#documentdb-gradient)" opacity="0.8" />

        <path d="M1 8 Q1 6 3 6 Q5 6 5 8 Q5 10 3 10 Q1 10 1 8 Z" fill="url(#documentdb-gradient)" opacity="0.6" />
      </g>
    </svg>
  );
}
