import { LucideProps } from "lucide-react";

export const S3Icon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-S3_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="s3-gradient">
        <stop stopColor="#1B660F" offset="0%"></stop>
        <stop stopColor="#6CAE3E" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#s3-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M12,19.5 C7.313,19.5 4,18.19 4,17 L4,7 C4,5.81 7.313,4.5 12,4.5 C16.687,4.5 20,5.81 20,7 L20,17 C20,18.19 16.687,19.5 12,19.5 M12,3.5 C6.21,3.5 3,5.29 3,7 L3,17 C3,18.71 6.21,20.5 12,20.5 C17.79,20.5 21,18.71 21,17 L21,7 C21,5.29 17.79,3.5 12,3.5 M12,11.5 C7.313,11.5 4,10.19 4,9 L4,13 C4,14.19 7.313,15.5 12,15.5 C16.687,15.5 20,14.19 20,13 L20,9 C20,10.19 16.687,11.5 12,11.5 M12,15.5 C7.313,15.5 4,14.19 4,13 L4,17 C4,18.19 7.313,19.5 12,19.5 C16.687,19.5 20,18.19 20,17 L20,13 C20,14.19 16.687,15.5 12,15.5"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
