import { LucideProps } from "lucide-react";

export const EksIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-EKS_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="eks-gradient">
        <stop stopColor="#C8511B" offset="0%"></stop>
        <stop stopColor="#FF9900" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#eks-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M12.1,12.6 L13.8,13.6 L12.1,14.6 L10.4,13.6 L12.1,12.6 Z M14.3,13.9 L12.1,15.2 L9.9,13.9 L9.9,11.3 L12.1,10 L14.3,11.3 L14.3,13.9 Z M19.5,12 C19.5,16.142 16.142,19.5 12,19.5 C7.858,19.5 4.5,16.142 4.5,12 C4.5,7.858 7.858,4.5 12,4.5 C16.142,4.5 19.5,7.858 19.5,12 M20.5,12 C20.5,7.306 16.694,3.5 12,3.5 C7.306,3.5 3.5,7.306 3.5,12 C3.5,16.694 7.306,20.5 12,20.5 C16.694,20.5 20.5,16.694 20.5,12"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
