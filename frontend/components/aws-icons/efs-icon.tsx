import { LucideProps } from "lucide-react";

export const EfsIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-EFS_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="efs-gradient">
        <stop stopColor="#1B660F" offset="0%"></stop>
        <stop stopColor="#6CAE3E" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_Amazon-EFS_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Storage" fill="url(#efs-gradient)">
        <rect id="Rectangle" x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M19,18 L5,18 C4.448,18 4,17.552 4,17 L4,7 C4,6.448 4.448,6 5,6 L19,6 C19.552,6 20,6.448 20,7 L20,17 C20,17.552 19.552,18 19,18 M19,5 L5,5 C3.895,5 3,5.895 3,7 L3,17 C3,18.105 3.895,19 5,19 L19,19 C20.105,19 21,18.105 21,17 L21,7 C21,5.895 20.105,5 19,5 M6,8 L18,8 L18,16 L6,16 L6,8 Z M5,17 L19,17 L19,7 L5,7 L5,17 Z M8,10 L16,10 L16,14 L8,14 L8,10 Z M7,15 L17,15 L17,9 L7,9 L7,15 Z M10,11 L14,11 L14,13 L10,13 L10,11 Z M9,14 L15,14 L15,10 L9,10 L9,14 Z"
        id="Amazon-EFS_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
