import { LucideProps } from "lucide-react";

export const SnsIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-SNS_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="sns-gradient">
        <stop stopColor="#B0084D" offset="0%"></stop>
        <stop stopColor="#FF4F8B" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#sns-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M19.5,12 C19.5,16.142 16.142,19.5 12,19.5 C7.858,19.5 4.5,16.142 4.5,12 C4.5,7.858 7.858,4.5 12,4.5 C16.142,4.5 19.5,7.858 19.5,12 M20.5,12 C20.5,7.306 16.694,3.5 12,3.5 C7.306,3.5 3.5,7.306 3.5,12 C3.5,16.694 7.306,20.5 12,20.5 C16.694,20.5 20.5,16.694 20.5,12 M16,11.5 C16,8.467 13.533,6 10.5,6 C7.467,6 5,8.467 5,11.5 C5,14.533 7.467,17 10.5,17 C13.533,17 16,14.533 16,11.5 M17,11.5 C17,15.084 14.084,18 10.5,18 C6.916,18 4,15.084 4,11.5 C4,7.916 6.916,5 10.5,5 C14.084,5 17,7.916 17,11.5"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
