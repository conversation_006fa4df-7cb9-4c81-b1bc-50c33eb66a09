import { LucideProps } from "lucide-react";

export const DynamodbIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-DynamoDB_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="dynamodb-gradient">
        <stop stopColor="#2E27AD" offset="0%"></stop>
        <stop stopColor="#527FFF" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#dynamodb-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M17.5,15.5 C17.5,16.327 14.083,17.5 12,17.5 C9.917,17.5 6.5,16.327 6.5,15.5 L6.5,16.5 C6.5,17.327 9.917,18.5 12,18.5 C14.083,18.5 17.5,17.327 17.5,16.5 L17.5,15.5 Z M17.5,13 C17.5,13.827 14.083,15 12,15 C9.917,15 6.5,13.827 6.5,13 L6.5,14 C6.5,14.827 9.917,16 12,16 C14.083,16 17.5,14.827 17.5,14 L17.5,13 Z M17.5,10.5 C17.5,11.327 14.083,12.5 12,12.5 C9.917,12.5 6.5,11.327 6.5,10.5 L6.5,11.5 C6.5,12.327 9.917,13.5 12,13.5 C14.083,13.5 17.5,12.327 17.5,11.5 L17.5,10.5 Z M17.5,8 C17.5,8.827 14.083,10 12,10 C9.917,10 6.5,8.827 6.5,8 L6.5,9 C6.5,9.827 9.917,11 12,11 C14.083,11 17.5,9.827 17.5,9 L17.5,8 Z M12,9 C14.083,9 17.5,7.827 17.5,7 C17.5,6.173 14.083,5 12,5 C9.917,5 6.5,6.173 6.5,7 C6.5,7.827 9.917,9 12,9"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
