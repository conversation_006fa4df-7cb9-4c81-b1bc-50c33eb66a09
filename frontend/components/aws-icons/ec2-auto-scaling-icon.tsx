import { LucideProps } from 'lucide-react';

export function Ec2AutoScalingIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="ec2-auto-scaling-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#C8511B" />
          <stop offset="100%" stopColor="#FF9900" />
        </linearGradient>
      </defs>
      <rect width="24" height="24" rx="4" fill="url(#ec2-auto-scaling-gradient)" />
      <g transform="translate(3, 3)">
        <rect x="2" y="2" width="4" height="4" rx="1" fill="white" opacity="0.9" />
        <rect x="7" y="2" width="4" height="4" rx="1" fill="white" opacity="0.7" />
        <rect x="12" y="2" width="4" height="4" rx="1" fill="white" opacity="0.9" />

        <rect x="2" y="7" width="4" height="4" rx="1" fill="white" opacity="0.6" />
        <rect x="7" y="7" width="4" height="4" rx="1" fill="white" opacity="0.8" />
        <rect x="12" y="7" width="4" height="4" rx="1" fill="white" opacity="0.6" />

        <rect x="4.5" y="13" width="9" height="2" rx="1" fill="white" opacity="0.9" />
        <polygon points="2,14 4,13 4,15" fill="white" opacity="0.8" />
        <polygon points="16,14 14,13 14,15" fill="white" opacity="0.8" />
      </g>
    </svg>
  );
}
