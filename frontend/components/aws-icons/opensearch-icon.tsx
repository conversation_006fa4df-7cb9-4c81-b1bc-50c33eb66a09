import { LucideProps } from 'lucide-react';

export function OpensearchIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="opensearch-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#156AB3" />
          <stop offset="100%" stopColor="#2E73B8" />
        </linearGradient>
      </defs>
      <rect width="24" height="24" rx="4" fill="url(#opensearch-gradient)" />
      <g transform="translate(4, 4)">
        <circle cx="8" cy="8" r="6" fill="white" opacity="0.2" />
        <circle cx="8" cy="8" r="4" fill="white" opacity="0.9" />

        <circle cx="8" cy="8" r="2.5" fill="url(#opensearch-gradient)" opacity="0.3" />
        <circle cx="8" cy="8" r="1.5" fill="url(#opensearch-gradient)" opacity="0.8" />

        <path d="M12 12 L14 14" stroke="white" strokeWidth="2" strokeLinecap="round" opacity="0.9" />
        <circle cx="14" cy="14" r="1" fill="white" opacity="0.8" />

        <rect x="6" y="6" width="4" height="0.5" rx="0.25" fill="white" opacity="0.7" />
        <rect x="6" y="7.5" width="3" height="0.5" rx="0.25" fill="white" opacity="0.6" />
        <rect x="6" y="9" width="3.5" height="0.5" rx="0.25" fill="white" opacity="0.7" />
      </g>
    </svg>
  );
}
