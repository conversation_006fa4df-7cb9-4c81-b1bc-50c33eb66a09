import { LucideProps } from 'lucide-react';

export function BatchIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="batch-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#C8511B" />
          <stop offset="100%" stopColor="#FF9900" />
        </linearGradient>
      </defs>
      <rect width="24" height="24" rx="4" fill="url(#batch-gradient)" />
      <g transform="translate(4, 4)">
        <rect x="2" y="2" width="12" height="2" rx="1" fill="white" opacity="0.9" />
        <rect x="2" y="6" width="8" height="2" rx="1" fill="white" opacity="0.7" />
        <rect x="2" y="10" width="10" height="2" rx="1" fill="white" opacity="0.8" />
        <rect x="2" y="14" width="6" height="2" rx="1" fill="white" opacity="0.6" />
        <circle cx="13" cy="7" r="1.5" fill="white" opacity="0.9" />
        <circle cx="13" cy="11" r="1.5" fill="white" opacity="0.7" />
      </g>
    </svg>
  );
}
