import { LucideProps } from "lucide-react";

export const CloudFormationIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_AWS-CloudFormation_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="cloudformation-gradient">
        <stop stopColor="#B0084D" offset="0%"></stop>
        <stop stopColor="#FF4F8B" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#cloudformation-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M13,16 L11,16 L11,14 L13,14 L13,16 Z M13,13 L11,13 L11,11 L13,11 L13,13 Z M13,10 L11,10 L11,8 L13,8 L13,10 Z M19.5,12 C19.5,16.142 16.142,19.5 12,19.5 C7.858,19.5 4.5,16.142 4.5,12 C4.5,7.858 7.858,4.5 12,4.5 C16.142,4.5 19.5,7.858 19.5,12 M20.5,12 C20.5,7.306 16.694,3.5 12,3.5 C7.306,3.5 3.5,7.306 3.5,12 C3.5,16.694 7.306,20.5 12,20.5 C16.694,20.5 20.5,16.694 20.5,12"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
