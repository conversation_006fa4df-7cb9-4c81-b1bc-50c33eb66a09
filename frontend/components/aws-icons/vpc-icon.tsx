import { LucideProps } from "lucide-react";

export const VpcIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-VPC_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="vpc-gradient">
        <stop stopColor="#4B612C" offset="0%"></stop>
        <stop stopColor="#7AA116" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#vpc-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M19,17.5 L5,17.5 C4.724,17.5 4.5,17.276 4.5,17 L4.5,7 C4.5,6.724 4.724,6.5 5,6.5 L19,6.5 C19.276,6.5 19.5,6.724 19.5,7 L19.5,17 C19.5,17.276 19.276,17.5 19,17.5 M19,5.5 L5,5.5 C4.173,5.5 3.5,6.173 3.5,7 L3.5,17 C3.5,17.827 4.173,18.5 5,18.5 L19,18.5 C19.827,18.5 20.5,17.827 20.5,17 L20.5,7 C20.5,6.173 19.827,5.5 19,5.5 M7,15 L17,15 L17,9 L7,9 L7,15 Z M6,16 L18,16 L18,8 L6,8 L6,16 Z"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
