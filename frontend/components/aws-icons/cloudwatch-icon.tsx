import { LucideProps } from "lucide-react";

export const CloudwatchIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-CloudWatch_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="cloudwatch-gradient">
        <stop stopColor="#B0084D" offset="0%"></stop>
        <stop stopColor="#FF4F8B" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#cloudwatch-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M12,3.5 C7.313,3.5 3.5,7.313 3.5,12 C3.5,16.687 7.313,20.5 12,20.5 C16.687,20.5 20.5,16.687 20.5,12 C20.5,7.313 16.687,3.5 12,3.5 M12,21.5 C6.762,21.5 2.5,17.238 2.5,12 C2.5,6.762 6.762,2.5 12,2.5 C17.238,2.5 21.5,6.762 21.5,12 C21.5,17.238 17.238,21.5 12,21.5 M12,7 C11.724,7 11.5,7.224 11.5,7.5 L11.5,12.5 C11.5,12.776 11.724,13 12,13 L15,13 C15.276,13 15.5,12.776 15.5,12.5 C15.5,12.224 15.276,12 15,12 L12.5,12 L12.5,7.5 C12.5,7.224 12.276,7 12,7"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
