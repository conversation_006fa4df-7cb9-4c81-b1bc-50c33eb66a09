import { LucideProps } from 'lucide-react';

export function RedshiftIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="redshift-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#156AB3" />
          <stop offset="100%" stopColor="#2E73B8" />
        </linearGradient>
      </defs>
      <rect width="24" height="24" rx="4" fill="url(#redshift-gradient)" />
      <g transform="translate(4, 4)">
        <rect x="2" y="2" width="12" height="12" rx="2" fill="white" opacity="0.2" />

        <rect x="3" y="4" width="3" height="8" rx="1" fill="white" opacity="0.9" />
        <rect x="6.5" y="6" width="3" height="6" rx="1" fill="white" opacity="0.8" />
        <rect x="10" y="3" width="3" height="9" rx="1" fill="white" opacity="0.9" />

        <circle cx="4.5" cy="8" r="0.5" fill="url(#redshift-gradient)" opacity="0.8" />
        <circle cx="8" cy="9" r="0.5" fill="url(#redshift-gradient)" opacity="0.8" />
        <circle cx="11.5" cy="7.5" r="0.5" fill="url(#redshift-gradient)" opacity="0.8" />

        <path d="M4.5 6 L8 7 L11.5 5.5" stroke="url(#redshift-gradient)" strokeWidth="1" opacity="0.6" />
        <path d="M4.5 10 L8 11 L11.5 9.5" stroke="url(#redshift-gradient)" strokeWidth="1" opacity="0.6" />
      </g>
    </svg>
  );
}
