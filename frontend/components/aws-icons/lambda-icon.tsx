import { LucideProps } from "lucide-react";

export const LambdaIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_AWS-Lambda_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="lambda-gradient">
        <stop stopColor="#C13A94" offset="0%"></stop>
        <stop stopColor="#FF5193" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#lambda-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M19.5,20 L15.697,20 C15.428,20 15.186,19.86 15.056,19.633 L10.364,11.482 L7.516,17.949 C7.384,18.231 7.106,18.41 6.798,18.41 L4.5,18.41 C4.224,18.41 4,18.186 4,17.91 C4,17.634 4.224,17.41 4.5,17.41 L6.484,17.41 L9.636,10.181 C9.766,9.907 10.043,9.728 10.349,9.728 C10.651,9.728 10.927,9.904 11.058,10.175 L16.053,18.821 L19.5,19 C19.776,19 20,19.224 20,19.5 C20,19.776 19.776,20 19.5,20 M14.75,6.5 L12.5,6.5 C12.224,6.5 12,6.276 12,6 C12,5.724 12.224,5.5 12.5,5.5 L14.75,5.5 C15.026,5.5 15.25,5.724 15.25,6 C15.25,6.276 15.026,6.5 14.75,6.5 M11.5,6.5 L9.25,6.5 C8.974,6.5 8.75,6.276 8.75,6 C8.75,5.724 8.974,5.5 9.25,5.5 L11.5,5.5 C11.776,5.5 12,5.724 12,6 C12,6.276 11.776,6.5 11.5,6.5"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
