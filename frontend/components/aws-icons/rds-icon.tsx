import { LucideProps } from "lucide-react";

export const RdsIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-RDS_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="rds-gradient">
        <stop stopColor="#2E27AD" offset="0%"></stop>
        <stop stopColor="#527FFF" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_Amazon-RDS_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Database" fill="url(#rds-gradient)">
        <rect id="Rectangle" x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M5.707,5 L7.853,7.146 L7.146,7.854 L5,5.707 L5,7.5 L4,7.5 L4,4.5 C4,4.224 4.224,4 4.5,4 L7.5,4 L7.5,5 L5.707,5 Z M7.853,16.854 L5.707,19 L7.5,19 L7.5,20 L4.5,20 C4.224,20 4,19.776 4,19.5 L4,16.5 L5,16.5 L5,18.293 L7.146,16.146 L7.853,16.854 Z M5,12 C5,12.75 5.966,13.542 7.519,14.069 L7.199,15.016 C5.166,14.328 4,13.228 4,12 C4,10.772 5.166,9.672 7.199,8.984 L7.519,9.931 C5.966,10.458 5,11.25 5,12 L5,12 Z M19,16.5 L20,16.5 L20,19.5 C20,19.776 19.776,20 19.5,20 L16.5,20 L16.5,19 L18.293,19 L16.146,16.854 L16.853,16.146 L19,18.293 L19,16.5 Z M20,4.5 L20,7.5 L19,7.5 L19,5.707 L16.853,7.854 L16.146,7.146 L18.293,5 L16.5,5 L16.5,4 L19.5,4 C19.776,4 20,4.224 20,4.5 L20,4.5 Z M20,12 C20,13.228 18.834,14.328 16.801,15.016 L16.48,14.069 C18.034,13.542 19,12.75 19,12 C19,11.25 18.034,10.458 16.48,9.931 L16.801,8.984 C18.834,9.672 20,10.772 20,12 L20,12 Z M12,15.402 C10.81,15.402 10.087,15.104 10,15.032 L10,10.651 C10.564,10.886 11.294,11 12,11 C12.708,11 13.439,10.886 14.004,10.65 L14.02,14.952 C13.913,15.104 13.19,15.402 12,15.402 L12,15.402 Z M12,9 C13.174,9 13.858,9.336 13.987,9.5 C13.858,9.664 13.174,10 12,10 C10.771,10 10.08,9.632 10,9.531 L10,9.521 C10.08,9.368 10.771,9 12,9 L12,9 Z M12,8 C10.555,8 9,8.469 9,9.5 L9,15.032 C9,15.979 10.507,16.402 12,16.402 C13.493,16.402 15,15.979 15,15.032 L15,9.5 C15,8.469 13.445,8 12,8 L12,8 Z"
        id="Amazon-RDS_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
