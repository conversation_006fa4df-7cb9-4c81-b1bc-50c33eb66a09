import { LucideProps } from "lucide-react";

export const ElasticacheIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-ElastiCache_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="elasticache-gradient">
        <stop stopColor="#2E27AD" offset="0%"></stop>
        <stop stopColor="#527FFF" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_Amazon-ElastiCache_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Database" fill="url(#elasticache-gradient)">
        <rect id="Rectangle" x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M12,18 C8.686,18 6,15.314 6,12 C6,8.686 8.686,6 12,6 C15.314,6 18,8.686 18,12 C18,15.314 15.314,18 12,18 M12,5 C8.134,5 5,8.134 5,12 C5,15.866 8.134,19 12,19 C15.866,19 19,15.866 19,12 C19,8.134 15.866,5 12,5 M9,12 C9,10.343 10.343,9 12,9 C13.657,9 15,10.343 15,12 C15,13.657 13.657,15 12,15 C10.343,15 9,13.657 9,12 M8,12 C8,14.209 9.791,16 12,16 C14.209,16 16,14.209 16,12 C16,9.791 14.209,8 12,8 C9.791,8 8,9.791 8,12 M12,11 C11.448,11 11,11.448 11,12 C11,12.552 11.448,13 12,13 C12.552,13 13,12.552 13,12 C13,11.448 12.552,11 12,11 M12,10 C13.105,10 14,10.895 14,12 C14,13.105 13.105,14 12,14 C10.895,14 10,13.105 10,12 C10,10.895 10.895,10 12,10 M4,12 L3,12 C3,7.029 7.029,3 12,3 C16.971,3 21,7.029 21,12 L20,12 C20,7.582 16.418,4 12,4 C7.582,4 4,7.582 4,12"
        id="Amazon-ElastiCache_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
