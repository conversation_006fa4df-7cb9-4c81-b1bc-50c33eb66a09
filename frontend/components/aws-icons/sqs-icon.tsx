import { LucideProps } from "lucide-react";

export const SqsIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-SQS_16</title>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="sqs-gradient">
        <stop stopColor="#B0084D" offset="0%"></stop>
        <stop stopColor="#FF4F8B" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g fill="url(#sqs-gradient)">
        <rect x="0" y="0" width="24" height="24" rx="4" ry="4"></rect>
      </g>
      <path
        d="M19.5,16 L4.5,16 C4.224,16 4,15.776 4,15.5 L4,8.5 C4,8.224 4.224,8 4.5,8 L19.5,8 C19.776,8 20,8.224 20,8.5 L20,15.5 C20,15.776 19.776,16 19.5,16 M19.5,7 L4.5,7 C3.673,7 3,7.673 3,8.5 L3,15.5 C3,16.327 3.673,17 4.5,17 L19.5,17 C20.327,17 21,16.327 21,15.5 L21,8.5 C21,7.673 20.327,7 19.5,7 M6.5,14 L17.5,14 C17.776,14 18,13.776 18,13.5 L18,10.5 C18,10.224 17.776,10 17.5,10 L6.5,10 C6.224,10 6,10.224 6,10.5 L6,13.5 C6,13.776 6.224,14 6.5,14"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
