'use client'

import { useState, useRef, useEffect, useCallback } from "react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import debounce from 'lodash/debounce';
import { flushSync } from "react-dom";
import useAuth from "@/hooks/useAuth";
import { MessageInputProps } from "@/components/chat/types";
import { useBuiltinConnectors } from '@/hooks/use-builtin-connectors';

// Import components
import { ModelSelector } from "@/components/chat/components/model-selector";
import { QuotaIndicator } from "@/components/chat/components/quota-indicator";
import { ActionButton } from "@/components/chat/components/action-button";
import { ResourceMentionDropdown } from "@/components/chat/components/resource-mention";
import { AgentMentionDropdown } from "@/components/chat/components/agent-mention";
import { highlightMentions, getIncompleteAtCursor } from "@/components/chat/utils/mention-highlighting";

// Import data
import { resourceCategories } from "@/components/chat/components/data";

// Import custom hooks
import { useKnowledgeBases } from "@/components/chat/hooks/useKnowledgeBases";

// Import icons
import { Settings } from "lucide-react";

// Import AgentExampleQuestions
import { AgentExampleQuestions } from '@/app/(dashboard)/agents/_components/agent-example-questions';



// Available AI models
const AI_MODELS = [
  { id: 'standard', name: 'Standard' },
  { id: 'premium', name: 'Premium', locked: true },
];

export function MessageInput({
  onSendMessage,
  disabled,
  value: externalValue,
  onChange,
  isStreaming,
  onStop,
  showExampleQuestions = false,
  isEmptyConversation = false,
  resourceType,
  quotaInfo
}: MessageInputProps) {
  // State
  const [localValue, setLocalValue] = useState(externalValue || '');
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState('standard');

  // # Mention feature states
  const [showResourceMention, setShowResourceMention] = useState(false);
  const [resourceMentionPosition, setResourceMentionPosition] = useState({ top: 0, left: 0 });
  const [resourceMentionFilter, setResourceMentionFilter] = useState('');

  // @ Mention feature states
  const [showAgentMention, setShowAgentMention] = useState(false);
  const [agentMentionPosition, setAgentMentionPosition] = useState({ top: 0, left: 0 });
  const [agentMentionFilter, setAgentMentionFilter] = useState('');

  // New state to track mentioned KB IDs
  const [mentionedKbIds, setMentionedKbIds] = useState<Record<string, string>>({});

  // Animation states for text limit feedback
  const [isShaking, setIsShaking] = useState(false);
  const [shouldScrollOnPaste, setShouldScrollOnPaste] = useState(false);

  // Get knowledge bases
  const { kbs, isLoading: kbsLoading } = useKnowledgeBases();

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isSubmittingRef = useRef(false);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Derived state
  const hasContent = Boolean(localValue?.trim());

  // Fetch builtin tools
  const { data: builtinTools, isPending: isBuiltinToolsLoading } = useBuiltinConnectors();

  // Prepare dynamic resource categories by merging static data with dynamic KB collections and builtin tools
  const dynamicResourceCategories = [
    {
      id: 'tools',
      name: 'Tools',
      icon: <Settings className="h-5 w-5 text-purple-500" />,
      items: isBuiltinToolsLoading ?
        [{ id: 'loading', title: 'Loading tools...', description: 'Please wait' }] :
        builtinTools?.filter(tool => tool.is_active).map(tool => ({
          id: tool.id,
          title: tool.display_name || tool.name,
          description: tool.description || 'No description available'
        })) || []
    },
    ...resourceCategories.map(category => {
      if (category.isDynamic && category.source === 'kb_collections') {
        return {
          ...category,
          items: kbsLoading ?
            [{ id: 'loading', title: 'Loading collections...', description: 'Please wait' }] :
            kbs
        };
      }
      return category;
    })
  ];

  // Effects
  // Sync with external value when it changes
  useEffect(() => {
    if (externalValue !== undefined) {
      setLocalValue(externalValue);
    }
  }, [externalValue]);

  // Debounced update to parent
  const debouncedOnChange = useCallback(
    debounce((value: string) => {
      onChange?.(value);
    }, 100),
    [onChange]
  );

  // Auto-resize textarea with debounce
  const debouncedResize = useCallback(
    debounce(() => {
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const maxHeight = window.innerWidth < 640 ? 200 : 250; // Responsive max height
        const newHeight = Math.min(textareaRef.current.scrollHeight, maxHeight);
        textareaRef.current.style.height = `${newHeight}px`;
      }
    }, 50),
    []
  );

  useEffect(() => {
    debouncedResize();
    return () => {
      debouncedResize.cancel();
    };
  }, [localValue, debouncedResize]);

  // Add this function to sync scrolling
  const syncScroll = (e: React.UIEvent<HTMLTextAreaElement>) => {
    if (overlayRef.current && textareaRef.current) {
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
      overlayRef.current.scrollLeft = textareaRef.current.scrollLeft;
    }
  };

  // Event handlers
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Don't update if we're in the middle of submitting
    if (isSubmittingRef.current) return;

    const newValue = e.target.value;
    const maxLength = 10000; // Set reasonable text limit

    // Check for text length limit
    if (newValue.length > maxLength) {
      // Trigger shake animation
      setIsShaking(true);
      setTimeout(() => setIsShaking(false), 600);
      return; // Don't update if over limit
    }

    setLocalValue(newValue);
    debouncedOnChange(newValue);

    // Check for mention triggers
    checkForMentions(newValue, e.target.selectionStart);

    // Ensure textarea resizes properly after content changes
    debouncedResize();
  };

  // Handle paste events with animation
  const handlePaste = useCallback((e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const pastedText = e.clipboardData.getData('text');
    const currentLength = localValue.length;
    const newLength = currentLength + pastedText.length;
    const maxLength = 10000;

    // If pasting would exceed limit, show shake animation
    if (newLength > maxLength) {
      e.preventDefault();
      setIsShaking(true);
      setTimeout(() => setIsShaking(false), 600);
      return;
    }

    // If pasting a lot of text, trigger scroll animation
    if (pastedText.length > 100) {
      setShouldScrollOnPaste(true);
      setTimeout(() => {
        if (textareaRef.current) {
          // Smooth scroll to show new content
          textareaRef.current.scrollTo({
            top: textareaRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
        setShouldScrollOnPaste(false);
      }, 100);
    }
  }, [localValue]);

  // Check for # and @ mentions in the text
  const checkForMentions = (text: string, cursorPos: number) => {
    const mentionInfo = getIncompleteAtCursor(text, cursorPos);

    if (!mentionInfo) {
      // If no mentions, hide both dropdowns
      setShowResourceMention(false);
      setShowAgentMention(false);
      return;
    }

    if (mentionInfo.type === 'resource') {
      // Show resource dropdown
      setResourceMentionFilter(mentionInfo.filter.toLowerCase());

      if (textareaRef.current) {
        const textareaRect = textareaRef.current.getBoundingClientRect();
        const cursorCoords = getCaretCoordinates(textareaRef.current, cursorPos);

        setResourceMentionPosition({
          top: window.scrollY + textareaRect.top + cursorCoords.top - 180,
          left: Math.max(16, Math.min(window.scrollX + textareaRect.left + cursorCoords.left, window.innerWidth - 320)),
        });
        setShowResourceMention(true);
      }

      // Hide agent mention
      setShowAgentMention(false);
    } else if (mentionInfo.type === 'agent') {
      // Show agent dropdown
      setAgentMentionFilter(mentionInfo.filter.toLowerCase());

      if (textareaRef.current) {
        const textareaRect = textareaRef.current.getBoundingClientRect();
        const cursorCoords = getCaretCoordinates(textareaRef.current, cursorPos);

        setAgentMentionPosition({
          top: window.scrollY + textareaRect.top + cursorCoords.top - 180,
          left: Math.max(16, Math.min(window.scrollX + textareaRect.left + cursorCoords.left, window.innerWidth - 320)),
        });
        setShowAgentMention(true);
      }

      // Hide resource mention
      setShowResourceMention(false);
    }
  };

  // Helper function to get caret coordinates
  const getCaretCoordinates = (element: HTMLTextAreaElement, position: number) => {
    // Create a temporary div to measure text
    const div = document.createElement('div');
    const style = window.getComputedStyle(element);
    const properties = [
      'fontFamily',
      'fontSize',
      'fontWeight',
      'wordWrap',
      'whiteSpace',
      'borderLeftWidth',
      'borderTopWidth',
      'borderRightWidth',
      'borderBottomWidth',
      'paddingLeft',
      'paddingTop',
      'paddingRight',
      'paddingBottom',
      'lineHeight'
    ];

    // Copy styles from textarea to div
    properties.forEach(prop => {
      // @ts-ignore - we know these styles exist
      div.style[prop] = style[prop];
    });

    // Set div content and styles
    div.textContent = element.value.substring(0, position);
    div.style.position = 'absolute';
    div.style.visibility = 'hidden';
    div.style.whiteSpace = 'pre-wrap';
    div.style.wordWrap = 'break-word';
    div.style.overflow = 'hidden';
    div.style.width = element.offsetWidth + 'px';

    // Add a span to mark cursor position
    const span = document.createElement('span');
    span.textContent = element.value.charAt(position) || '.';
    div.appendChild(span);

    // Add to document, measure, then remove
    document.body.appendChild(div);
    const coordinates = {
      top: span.offsetTop + parseInt(style.borderTopWidth) + parseInt(style.paddingTop),
      left: span.offsetLeft + parseInt(style.borderLeftWidth) + parseInt(style.paddingLeft)
    };
    document.body.removeChild(div);

    return coordinates;
  };

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!hasContent || disabled || isSubmittingRef.current) return;

    // Set the submitting flag to prevent race conditions
    isSubmittingRef.current = true;

    // Capture message before clearing
    const messageContent = localValue.trim();

    try {
      // Use flushSync to ensure state updates are processed immediately
      flushSync(() => {
        setLocalValue('');
        // Call onChange directly instead of using the debounced version
        onChange?.('');
      });

      // Only then send the message
      onSendMessage(messageContent);

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } finally {
      // Reset the submitting flag
      isSubmittingRef.current = false;
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Let the dropdowns handle their own keyboard events
    if (showResourceMention || showAgentMention) {
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();

      if (!isStreaming) {
        handleSubmit();
      }
    }
  };

  // Handle resource mention selection
  const handleResourceSelect = (itemId: string, fullPath: string) => {
    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = localValue.substring(0, cursorPos);
      const textAfterCursor = localValue.substring(cursorPos);

      // Get mention being typed using the utility function
      const mentionInfo = getIncompleteAtCursor(localValue, cursorPos);

      if (mentionInfo && mentionInfo.type === 'resource') {
        // Construct new text with the replacement
        const cleanPath = fullPath.replace(/\//g, '/').toLowerCase();
        const newText =
          textBeforeCursor.substring(0, mentionInfo.startIndex) +
          `#${cleanPath}` +
          ' ' + // Add a space AFTER the entire mention
          textAfterCursor;

        // Store the KB ID mapped to its mention path
        setMentionedKbIds(prev => ({
          ...prev,
          [`#${cleanPath}`]: itemId
        }));

        // Update the input value
        setLocalValue(newText);
        onChange?.(newText);
        setShowResourceMention(false);

        // Position the cursor after the mention and space
        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos = mentionInfo.startIndex + `#${cleanPath} `.length;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  };

  // Handle agent mention selection
  const handleAgentSelect = (agentId: string, agentName: string) => {
    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = localValue.substring(0, cursorPos);
      const textAfterCursor = localValue.substring(cursorPos);

      // Get mention being typed using the utility function
      const mentionInfo = getIncompleteAtCursor(localValue, cursorPos);

      if (mentionInfo && mentionInfo.type === 'agent') {
        // Prepare agent name (removing any parenthetical information)
        const displayName = agentName.split(' (')[0];

        // Construct new text with the replacement
        const newText =
          textBeforeCursor.substring(0, mentionInfo.startIndex) +
          `@${displayName}` +
          ' ' + // Add a space AFTER the entire mention
          textAfterCursor;

        // Update the input value
        setLocalValue(newText);
        onChange?.(newText);
        setShowAgentMention(false);

        // Position the cursor after the mention and space
        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos = mentionInfo.startIndex + `@${displayName} `.length;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  };

  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  // Add autofocus functionality
  useEffect(() => {
    // Auto focus the textarea when component mounts
    if (textareaRef.current && !disabled) {
      // Small delay to ensure focus works properly after page load/navigation
      const timer = setTimeout(() => {
        textareaRef.current?.focus();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [disabled]);

  const { highlightedText, hasMentions } = highlightMentions(localValue);


  return (
    <div className="flex-none bg-transparent border-none pb-0">
      <div className="max-w-4xl mx-auto px-2 sm:px-4 pt-2 pb-0">
        {/* Show example questions when conversation is empty AND showExampleQuestions is true */}
        {showExampleQuestions && isEmptyConversation && !localValue && (
          <div className="flex justify-center items-center w-full mb-4">
            <div className="w-full">
              <AgentExampleQuestions
                resourceType={resourceType as any}
                onQuestionClick={(question) => {
                  setLocalValue(question);
                  if (onChange) onChange(question);
                  // Focus on the textarea after selecting a question
                  setTimeout(() => {
                    if (textareaRef.current) {
                      textareaRef.current.focus();
                      // Move cursor to the end of the text
                      const length = question.length;
                      textareaRef.current.setSelectionRange(length, length);
                    }
                  }, 100);
                }}
              />
            </div>
          </div>
        )}
        <div className="rounded-2xl sm:rounded-3xl bg-muted/30 dark:bg-muted/10 border border-muted-foreground/10 shadow-none overflow-hidden transition-all duration-200 hover:border-muted-foreground/20 hover:shadow-none">
          {/* Message Input Row */}
          <form onSubmit={handleSubmit} className="relative">
            {/* Resource Mention Dropdown */}
            <ResourceMentionDropdown
              isVisible={showResourceMention}
              position={resourceMentionPosition}
              filter={resourceMentionFilter}
              categories={dynamicResourceCategories}
              onSelect={handleResourceSelect}
              onClose={() => setShowResourceMention(false)}
            />

            {/* Agent Mention Dropdown */}
            <AgentMentionDropdown
              isVisible={showAgentMention}
              position={agentMentionPosition}
              filter={agentMentionFilter}
              onSelect={handleAgentSelect}
              onClose={() => setShowAgentMention(false)}
            />

            {/* Message Input Container */}
            <div className={cn(
              "relative overflow-hidden transition-transform duration-150",
              isShaking && "animate-shake"
            )}>
              {/* Display the textarea for input */}
              <Textarea
                ref={textareaRef}
                value={localValue}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                onPaste={handlePaste}
                onScroll={syncScroll}
                placeholder=""
                disabled={isStreaming ? false : disabled}
                autoFocus={!disabled}
                className={cn(
                  "w-full",
                  "min-h-[50px] sm:min-h-[60px] max-h-[200px] sm:max-h-[250px]",
                  "resize-none overflow-y-auto custom-scrollbar",
                  "py-3 sm:py-4 px-3 sm:px-4",
                  "border-none focus-visible:ring-0",
                  "bg-transparent",
                  "text-sm sm:text-base leading-relaxed",
                  // Only make text transparent when there are mentions, otherwise use normal text color
                  hasMentions ? "text-transparent caret-foreground selection:bg-primary/20" : "text-foreground caret-foreground",
                  // Don't hide when empty to ensure cursor is visible
                  "placeholder:opacity-0" // Use placeholder opacity instead
                )}
                rows={1}
              />

              {/* Display highlighted text overlay when there are mentions */}
              {hasMentions && (
                <div
                  ref={overlayRef}
                  className="pointer-events-none absolute inset-0 w-full h-full py-3 sm:py-4 px-3 sm:px-4 whitespace-pre-wrap break-words text-sm sm:text-base leading-relaxed text-foreground"
                  style={{
                    fontFamily: 'inherit',
                    fontSize: 'inherit',
                    letterSpacing: 'inherit',
                    wordSpacing: 'inherit',
                    lineHeight: 'inherit',
                    textIndent: 'inherit',
                    tabSize: 'inherit',
                    overflow: 'hidden'
                  }}
                >
                  {highlightedText}
                </div>
              )}

              {!localValue && (
                <div className="pointer-events-none absolute inset-0 px-3 sm:px-4 py-3 sm:py-4 text-gray-400 dark:text-gray-500">
                  <div className="text-sm sm:text-base leading-relaxed">
                    Type <span className="text-primary font-medium mx-0.5">#</span> to mention a tool, or document, or <span className="text-primary font-medium mx-0.5">@</span> to mention an agent...
                  </div>
                </div>
              )}
            </div>
          </form>

          {/* Model and Tools Row */}
          <div className="flex items-center gap-1 sm:gap-2 p-2 sm:p-3 bg-muted/30 dark:bg-muted/10">
            {/* Model Selector - moved back to the left */}
            <div className="flex gap-2 items-center">
              <ModelSelector
                models={AI_MODELS}
                selectedModelId={selectedModelId}
                onSelectModel={setSelectedModelId}
                isDropdownOpen={isModelDropdownOpen}
                setIsDropdownOpen={setIsModelDropdownOpen}
              />

              {/* Hidden search button for now */}
              {/* <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-9 px-3 flex items-center gap-2",
                  "rounded-full text-sm",
                  "text-muted-foreground hover:text-foreground",
                  "transition-all duration-300",
                  "hover:bg-muted/70 dark:hover:bg-muted/30",
                  "active:scale-95",
                  isSearchOpen && "bg-primary/10 text-primary hover:text-primary hover:bg-primary/20 dark:bg-primary/20 dark:hover:bg-primary/30"
                )}
                onClick={toggleSearch}
              >
                <Globe className={cn(
                  "h-4 w-4",
                  "transition-transform duration-300",
                  isSearchOpen && "rotate-12"
                )} />
                <span>Search</span>
              </Button> */}
            </div>

            <div className="flex-1" />

            {/* Quota Indicator - kept on the right */}
            <QuotaIndicator quotaInfo={quotaInfo} />

            {/* Hidden attachment button for now */}
            {/* <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted/70 dark:hover:bg-muted/30 transition-all duration-200 active:scale-95"
            >
              <Paperclip className="h-4 w-4" />
            </Button> */}

            {/* Send/Stop Button */}
            <ActionButton
              isStreaming={!!isStreaming}
              hasContent={hasContent}
              disabled={disabled}
              onSubmit={handleSubmit}
              onStop={onStop}
            />
          </div>
        </div>
        <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-500 text-center pt-2 pb-0 mb-0 px-2">
          CloudThinker can make mistakes. Verify important information.
        </div>
      </div>
    </div>
  );
}
