'use client';

import React, { useState, useRef, useEffect } from 'react';
import { AutonomousAgentsService } from '@/client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface ConversationHeaderProps {
  conversationId?: string;
  conversationTitle?: string;
  conversationCreatedAt?: string;
  modelProvider?: string;
}

export function ConversationHeader({
  conversationId,
  conversationTitle,
}: ConversationHeaderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(conversationTitle || '');
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const renameMutation = useMutation({
    mutationFn: ({ conversationId, name }: { conversationId: string; name: string }) =>
      AutonomousAgentsService.renameConversation({ conversationId, name }),
    onSuccess: () => {
      // Invalidate conversations queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      toast({
        title: "Success",
        description: "Conversation renamed successfully",
      });
      setIsEditing(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to rename conversation. Please try again."
      });
      setEditValue(conversationTitle || ''); // Reset to original value
      setIsEditing(false);
    }
  });

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Reset edit value when conversation title changes
  useEffect(() => {
    setEditValue(conversationTitle || '');
  }, [conversationTitle]);

  if (!conversationId || !conversationTitle) {
    return null;
  }

  const handleClick = () => {
    setIsEditing(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setEditValue(conversationTitle || '');
      setIsEditing(false);
    }
  };

  const handleBlur = () => {
    setEditValue(conversationTitle || '');
    setIsEditing(false);
  };

  const handleSave = () => {
    const trimmedValue = editValue.trim();
    if (trimmedValue && trimmedValue !== conversationTitle && conversationId) {
      renameMutation.mutate({ conversationId, name: trimmedValue });
    } else {
      setIsEditing(false);
    }
  };

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="text"
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        className="text-sm font-medium text-foreground bg-transparent border-none outline-none p-0 m-0 max-w-[300px] truncate"
        disabled={renameMutation.isPending}
      />
    );
  }

  return (
    <span
      onClick={handleClick}
      className="text-sm font-medium text-foreground cursor-pointer hover:text-primary transition-colors max-w-[300px] truncate"
      title={conversationTitle}
    >
      {conversationTitle}
    </span>
  );
}
