export interface NextStep {
  id: string;
  text: string;
  index: number;
}

/**
 * Extracts next steps from message content by finding ```next code blocks
 * and parsing each numbered line as a separate step
 */
export function extractNextSteps(content: string): NextStep[] {
  if (!content || typeof content !== 'string') {
    return [];
  }

  // Regex to find ```next code blocks
  const nextBlockRegex = /```next\s*\n([\s\S]*?)```/gi;
  const matches = [...content.matchAll(nextBlockRegex)];

  if (matches.length === 0) {
    return [];
  }

  // Extract all next steps from all ```next blocks
  const allSteps: NextStep[] = [];
  let globalIndex = 0;

  matches.forEach(match => {
    const blockContent = match[1].trim();

    // Split by lines and filter for numbered items
    const lines = blockContent.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && /^\d+\.?\s+/.test(line));

    lines.forEach(line => {
      // Remove the number prefix from the text
      const cleanText = line.replace(/^\d+\.?\s+/, '').trim();

      allSteps.push({
        id: `next-step-${globalIndex}`,
        text: cleanText,
        index: globalIndex
      });
      globalIndex++;
    });
  });

  return allSteps;
}

/**
 * Removes ```next code blocks from message content to avoid duplicate display
 */
export function removeNextBlocks(content: string): string {
  if (!content || typeof content !== 'string') {
    return content;
  }

  // Remove all ```next code blocks
  return content.replace(/```next\s*\n[\s\S]*?```/gi, '').trim();
}
