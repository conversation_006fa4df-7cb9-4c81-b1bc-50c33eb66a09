import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PlayCircle } from "lucide-react"
import { NextStep } from "../../utils/next-steps-parser"

interface NextStepsCardsProps {
  nextSteps: NextStep[];
  onStepClick: (stepText: string) => void;
}

export function NextStepsCards({ nextSteps, onStepClick }: NextStepsCardsProps) {
  if (!nextSteps || nextSteps.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 space-y-2">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {nextSteps.map((step) => (
          <Card
            key={step.id}
            className="transition-all duration-200 hover:bg-primary/5 hover:border-primary/30 cursor-pointer group border-dashed pl-2"
            onClick={() => onStepClick(step.text)}
          >
            <CardContent className="p-3">
              <p className="text-xs text-foreground/80 leading-relaxed line-clamp-2">
                {step.text}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
