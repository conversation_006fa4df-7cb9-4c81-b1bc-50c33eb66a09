'use client'

import { useState, useCallback, memo, useMemo, useEffect, useReducer } from "react";
import { AutonomousMessageList } from "./message-list";
import { MessageInput } from "../message-input";
import { Message, Session, ToolCall } from "../types";
import { MessageActionType, MessageDisplayComponentPublic, ResourceRead, RecommendationPublic } from '@/client/types.gen';
import {
    Loader2,
    MessagesSquare,
    Users,
} from "lucide-react";
import { format } from "date-fns";
import { TabsContainer } from "./tabs-container";
import { ChatPanelHeaderContent, ChatPanelHeaderActionsContent } from "./chat-panel-header";
import { InterruptConfirmation } from '@/hooks/message-stream';
import { ChartsCanvas } from "./canvas";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import useWindowSize from "@/hooks/useWindowSize";
import Image from 'next/image';
import { DateFormat } from "@/lib/date-utils";
import { formatUtcDate } from "@/lib/date-utils";

interface ChatContainerProps {
    session?: Session;
    messages: Message[];
    isStreaming: boolean;
    isLoadingHistory?: boolean;
    onSendMessage: (content: string, restore?: { messageId: string } | null, actionType?: MessageActionType) => void;
    onStopStreaming?: () => void;
    onNewChat?: () => void;
    inputSectionChildren?: React.ReactNode;
    confirmation?: InterruptConfirmation | null;
    isCreatingConversation?: boolean;
    disableCanvas?: boolean;
    resourceData?: ResourceRead;
    streamingRecommendations?: RecommendationPublic[];
    setStreamingRecommendations?: React.Dispatch<React.SetStateAction<RecommendationPublic[]>>;
    defaultCanvasTab?: 'resource' | 'charts' | 'planning';
    quotaInfo?: {
        quota_used: number;
        quota_limit: number;
        quota_remaining: number;
        usage_percentage: number;
    };
    resourceId?: string;
    isSharedView?: boolean;
}

// Default panel sizes
const DEFAULT_CHAT_PANEL_SIZE = 65;
const DEFAULT_CANVAS_PANEL_SIZE = 35;

// Local storage key for panel sizes
const PANEL_SIZES_STORAGE_KEY = 'autonomous-chat-panel-sizes';

// Define action types for our reducer
type ChatPanelAction =
  | { type: 'SET_ACTIVE_TAB', payload: string }
  | { type: 'SET_UNREAD_COUNT', payload: number }
  | { type: 'INCREMENT_UNREAD_COUNT' }
  | { type: 'RESET_UNREAD_COUNT' }
  | { type: 'TOGGLE_CANVAS', payload: boolean }
  | { type: 'SET_MANUALLY_HIDDEN', payload: boolean }
  | { type: 'SET_INPUT_VALUE', payload: string }
  | { type: 'SET_HIDDEN_ROLES', payload: string[] }
  | { type: 'TOGGLE_ROLE_VISIBILITY', payload: string }
  | { type: 'UPDATE_DISPLAY_COMPONENTS', payload: CanvasItemWithAgent[] }
  | { type: 'UPDATE_TOOL_CALLS', payload: ToolCall[] }
  | { type: 'UPDATE_TOOL_AGENTS', payload: CanvasItemWithAgent[] }
  | { type: 'UPDATE_PANEL_SIZES', payload: number[] }
  | { type: 'RESET_STATE', payload?: { sessionId?: string } }
  | { type: 'UPDATE_RESOURCE_ID', payload: string };

// Define state interface
interface ChatPanelState {
  activeTab: string;
  unreadCount: number;
  isCanvasVisible: boolean;
  manuallyHidden: boolean;
  inputValue: string;
  hiddenRoles: string[];
  activeToolCalls: ToolCall[];
  displayComponents: MessageDisplayComponentPublic[];
  canvasItemsWithAgent: CanvasItemWithAgent[];
  panelSizes: number[];
  resourceId?: string;
}

// Initial state
const initialState: ChatPanelState = {
  activeTab: 'chat',
  unreadCount: 0,
  isCanvasVisible: true,
  manuallyHidden: false,
  inputValue: "",
  hiddenRoles: ['assistant', 'system'],
  activeToolCalls: [],
  displayComponents: [],
  canvasItemsWithAgent: [],
  panelSizes: [DEFAULT_CHAT_PANEL_SIZE, DEFAULT_CANVAS_PANEL_SIZE]
};

// Reducer function
function chatPanelReducer(state: ChatPanelState, action: ChatPanelAction): ChatPanelState {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      // Set active tab and reset unread count when switching to the chat tab
      return {
        ...state,
        activeTab: action.payload,
        unreadCount: action.payload === 'chat' ? 0 : state.unreadCount
      };

    case 'SET_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };

    case 'INCREMENT_UNREAD_COUNT':
      return { ...state, unreadCount: state.unreadCount + 1 };

    case 'RESET_UNREAD_COUNT':
      return { ...state, unreadCount: 0 };

    case 'TOGGLE_CANVAS':
      return {
        ...state,
        isCanvasVisible: action.payload,
        // Update manually hidden state when toggling canvas
        manuallyHidden: action.payload ? false : true
      };

    case 'SET_MANUALLY_HIDDEN':
      return { ...state, manuallyHidden: action.payload };

    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload };

    case 'SET_HIDDEN_ROLES':
      return { ...state, hiddenRoles: action.payload };

    case 'TOGGLE_ROLE_VISIBILITY':
      return {
        ...state,
        hiddenRoles: state.hiddenRoles.includes(action.payload)
          ? state.hiddenRoles.filter(r => r !== action.payload)
          : [...state.hiddenRoles, action.payload]
      };

    case 'UPDATE_DISPLAY_COMPONENTS':
      return {
        ...state,
        canvasItemsWithAgent: action.payload,
        displayComponents: action.payload.map(item => item.content as MessageDisplayComponentPublic),
        // Remove auto-show canvas logic
        isCanvasVisible: state.isCanvasVisible
      };

    case 'UPDATE_TOOL_CALLS':
      return {
        ...state,
        activeToolCalls: action.payload,
        // Auto-show canvas logic is in the useEffect, not here
      };

    case 'UPDATE_TOOL_AGENTS':
      // Merge the new tool agent items with existing display component items
      // First filter out any existing tool calls to avoid duplicates
      const existingDisplayComponents = state.canvasItemsWithAgent.filter(
        item => item.type === 'displayComponent'
      );

      // Create new array with both display components and tool calls
      const mergedItems = [...existingDisplayComponents, ...action.payload];

      return {
        ...state,
        canvasItemsWithAgent: mergedItems,
        // Don't update displayComponents here, that should only be for display components
        // Don't auto-show canvas here, that's handled in the tool calls effect
      };

    case 'UPDATE_PANEL_SIZES':
      // Save panel sizes to localStorage for persistence
      try {
        localStorage.setItem(PANEL_SIZES_STORAGE_KEY, JSON.stringify(action.payload));
      } catch (error) {
        console.error('Failed to save panel sizes to localStorage:', error);
      }
      return {
        ...state,
        panelSizes: action.payload
      };

    case 'RESET_STATE':
      return {
        ...initialState,
        activeTab: 'chat',
        manuallyHidden: false,
        isCanvasVisible: true,
        hiddenRoles: state.hiddenRoles,
        panelSizes: state.panelSizes
      };

    case 'UPDATE_RESOURCE_ID':
      return { ...state, resourceId: action.payload };

    default:
      return state;
  }
}

// Memoized components
const MemoizedAutonomousMessageList = memo(AutonomousMessageList);

// Memoized loading state component
// const LoadingState = memo(() => (
//     <div className="flex items-center justify-center">
//         <Loader2 className="h-4 w-4 animate-spin text-muted-foreground mr-2" />
//         <span className="text-sm text-muted-foreground">Creating conversation...</span>
//     </div>
// ));

// Update the interface to include message index for ordering
interface CanvasItemWithAgent {
    id: string;
    type: 'toolCall' | 'displayComponent';
    content: ToolCall | MessageDisplayComponentPublic;
    agentName?: string;
    agentRole?: string;
    messageIndex: number; // Track original message position
}

export function TabAutonomousChatContainer({
    session,
    messages,
    isStreaming,
    isLoadingHistory,
    onSendMessage,
    onStopStreaming,
    onNewChat,
    inputSectionChildren,
    confirmation,
    isCreatingConversation = false,
    disableCanvas = false,
    resourceData,
    streamingRecommendations = [],
    setStreamingRecommendations = () => {},
    defaultCanvasTab = 'charts',
    quotaInfo,
    resourceId,
    isSharedView = false,
}: ChatContainerProps) {
    // Initialize state with saved panel sizes if available
    const initialStateWithSavedSizes = useMemo(() => {
        let sizes = [DEFAULT_CHAT_PANEL_SIZE, DEFAULT_CANVAS_PANEL_SIZE];
        if (typeof window !== 'undefined') {
            try {
                const savedSizes = localStorage.getItem(PANEL_SIZES_STORAGE_KEY);
                if (savedSizes) {
                    const parsedSizes = JSON.parse(savedSizes);
                    if (Array.isArray(parsedSizes) && parsedSizes.length === 2) {
                        sizes = parsedSizes;
                    }
                }
            } catch (error) {
                console.error('Failed to load panel sizes from localStorage:', error);
            }
        }

        // Always set canvas to visible by default
        return {
            ...initialState,
            isCanvasVisible: true,
            manuallyHidden: false,
            panelSizes: sizes
        };
    }, [disableCanvas]);

    // Use the memoized initial state
    const [state, dispatch] = useReducer(chatPanelReducer, initialStateWithSavedSizes);

    // Get window size for responsive behavior
    const { width, isMobile, isTablet } = useWindowSize();

    // Responsive breakpoint for hiding canvas
    const SMALL_SCREEN_BREAKPOINT = 768;

    // Update the width effect to handle canvas visibility
    useEffect(() => {
        const isVerySmallScreen = width < 1100;
        dispatch({ type: 'TOGGLE_CANVAS', payload: !isVerySmallScreen });
        dispatch({ type: 'SET_MANUALLY_HIDDEN', payload: false });
    }, [width]);

    // Reset state when session changes
    useEffect(() => {
      if (session?.id) {
        // Only reset non-essential parts of state to preserve context
        dispatch({
          type: 'RESET_STATE',
          payload: {
            sessionId: session.id,
          }
        });
      }
    }, [session?.id]);

    // Update context when resourceId changes
    useEffect(() => {
      if (resourceId) {
        dispatch({
          type: 'UPDATE_RESOURCE_ID',
          payload: resourceId
        });
      }
    }, [resourceId]);

    // Consolidated effect to extract display components from messages
    useEffect(() => {
        const extractedCanvasItems: CanvasItemWithAgent[] = [];

        // Process messages to extract display components
        messages.forEach((message, messageIndex) => {
            // Extract agent information from the message
            const agentName = message.role !== 'user' ? message.role : undefined;
            const agentRole = message.role;

            // Extract display components with agent information
            if (message.displayComponents && message.displayComponents.length > 0) {
                message.displayComponents.forEach(component => {
                    extractedCanvasItems.push({
                        id: component.id,
                        type: 'displayComponent',
                        content: component,
                        agentName,
                        agentRole,
                        messageIndex
                    });
                });
            }
        });

        // Update state with extracted components
        dispatch({ type: 'UPDATE_DISPLAY_COMPONENTS', payload: extractedCanvasItems });
    }, [messages, session?.id, isStreaming]);

    // Filter and sort messages with optimized performance
    const groupChatMessages = useMemo(() => {
        // Create a Map for faster duplicate checking
        const messageMap = new Map<string, boolean>();

        // Process messages in a single pass
        const processedMessages = messages.reduce<Message[]>((acc, message) => {
            // Skip if we've already processed this message
            if (messageMap.has(message.id)) return acc;

            // For user messages, keep as is
            if (message.role === 'user') {
                acc.push({
                    ...message,
                    timestamp: new Date(message.timestamp)
                });
                messageMap.set(message.id, true);
                return acc;
            }

            // For non-user messages, use toolCalls[].thought for group chat
            if (message.toolCalls && message.toolCalls.length > 0) {
                // Concatenate all group_chat blocks from toolCalls.thought, including those with name 'thought'
                const groupChatContents: string[] = [];
                message.toolCalls.forEach(tc => {
                    if (tc.thought && (tc.name?.toLowerCase() === 'thought' || tc.thought)) {
                        // Extract all <group_chat>...</group_chat> blocks from the thought
                        const matches = tc.thought.match(/<group_chat>([\s\S]*?)<\/group_chat>/g);
                        if (matches) {
                            matches.forEach(match => {
                                const content = match.replace(/<group_chat>([\s\S]*?)<\/group_chat>/, '$1').trim();
                                if (content) groupChatContents.push(content);
                            });
                        }
                    }
                });
                if (groupChatContents.length > 0) {
                    acc.push({
                        ...message,
                        content: groupChatContents.join('\n\n'),
                        timestamp: new Date(message.timestamp)
                    });
                    messageMap.set(message.id, true);
                }
                return acc;
            }
            // Otherwise, skip
            return acc;
        }, []);

        // Sort purely by timestamp (oldest first)
        return processedMessages.sort((a, b) => {
            // Use message creation timestamp for chronological ordering
            return (a.timestamp?.getTime() || 0) - (b.timestamp?.getTime() || 0) ||
                   messages.findIndex(m => m.id === a.id) - messages.findIndex(m => m.id === b.id);
        });
    }, [messages]);

    // Remove tool calls effect that opens canvas
    // Modify the existing effect to remove auto-opening
    useEffect(() => {
        // Collect all tool calls from messages with agent information
        const allToolCalls: ToolCall[] = [];
        const toolsWithAgentInfo: CanvasItemWithAgent[] = [];

        messages.forEach((message, messageIndex) => {
            if (message.toolCalls && message.toolCalls.length > 0) {
                // Extract agent information from the message
                const agentName = message.role !== 'user' ? message.role : undefined;
                const agentRole = message.role;

                message.toolCalls.forEach(toolCall => {
                    // Create a new tool call with agent info
                    const toolCallWithAgent = {
                        ...toolCall,
                        agentName: agentName || 'assistant',
                        agentRole: agentRole || 'assistant'
                    };

                    // Add to the plain tool calls array with agent info attached
                    allToolCalls.push(toolCallWithAgent);

                    // Add to the tool calls with agent info
                    toolsWithAgentInfo.push({
                        id: toolCall.id,
                        type: 'toolCall',
                        content: toolCallWithAgent, // Use the enhanced tool call with agent info
                        agentName,
                        agentRole,
                        messageIndex
                    });
                });
            }
        });

        // Update state with all extracted tool calls
        if (allToolCalls.length > 0) {
            // Update state.activeToolCalls
            dispatch({ type: 'UPDATE_TOOL_CALLS', payload: allToolCalls });

            // Add tool calls to the canvas items (alongside display components)
            // This helps the canvas know which agent called which tool
            if (toolsWithAgentInfo.length > 0) {
                dispatch({ type: 'UPDATE_TOOL_AGENTS', payload: toolsWithAgentInfo });
            }
        }
    }, [messages]);

    // Handle functions - memoized with useCallback
    const handleTabChange = useCallback((tabId: string) => {
        dispatch({ type: 'SET_ACTIVE_TAB', payload: tabId });
    }, []);

    const handleNewMessage = useCallback((content: string, restore?: { messageId: string } | null, actionType?: MessageActionType) => {
        onSendMessage(content, restore, actionType);

        // Clear the input value when sending a message
        dispatch({ type: 'SET_INPUT_VALUE', payload: "" });

        // Increment badge counter if not on group-chat tab
        if (state.activeTab !== 'group-chat') {
            dispatch({ type: 'INCREMENT_UNREAD_COUNT' });
        }
    }, [state.activeTab, onSendMessage]);

    const handleInputChange = useCallback((value: string) => {
        dispatch({ type: 'SET_INPUT_VALUE', payload: value });
    }, []);

    const handleNextStepClick = useCallback((content: string) => {
        // Instead of sending immediately, populate the input field
        dispatch({ type: 'SET_INPUT_VALUE', payload: content });
    }, []);

    const handleRestore = useCallback((messageId: string, content: string) => {
        handleNewMessage(content, { messageId });
    }, [handleNewMessage]);

    // Handle panel resize
    const handlePanelResize = useCallback((sizes: number[]) => {
        dispatch({ type: 'UPDATE_PANEL_SIZES', payload: sizes });
    }, []);

    const handleNewChatCb = useCallback(() => {
        // Reset canvas state
        dispatch({ type: 'RESET_STATE' });
        // Call the original onNewChat handler
        onNewChat?.();
    }, [onNewChat]);

    const handleExport = useCallback(() => {
        // Implementation for exporting chat
        const chatData = {
            title: session?.title,
            category: session?.category,
            timestamp: session?.timestamp,
            messages: messages
        };

        const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${session?.title || 'chat'}-${formatUtcDate(new Date(), DateFormat.ISO_DATE)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, [session, messages]);

    // Simplified check for input disabled state
    const isInputDisabled = isStreaming;

    // If shared view, disable canvas and message input
    if (isSharedView) {
        return (
            <div className="flex flex-col h-full w-full items-center bg-background dark:bg-background">
                <div className="flex flex-col items-center pt-8 pb-4 w-full">
                    <div className=" px-6 py-4 flex flex-col items-center
                        backdrop-blur-md bg-background/80 dark:bg-popover/80 border-border rounded-xl">
                        <a href={process.env.NEXT_PUBLIC_APP_URL || '/'} target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/logo.svg"
                                alt="CloudThinker Logo"
                                width={64}
                                height={32}
                                className="object-contain mb-1 sm:w-20 w-16"
                                priority
                                aria-label="CloudThinker Logo"
                            />
                        </a>
                        <span className="text-xs sm:text-sm text-muted-foreground font-semibold tracking-wide">
                            Powered by <span className="text-primary font-bold">CloudThinker</span>
                        </span>
                    </div>
                </div>
                <div className="flex-1 w-full max-w-5xl mx-auto overflow-y-auto custom-scrollbar px-2 sm:px-0">
                    <MemoizedAutonomousMessageList
                        messages={messages}
                        isLoading={isStreaming}
                        onNextStepClick={() => {}}
                        onRestore={() => {}}
                        confirmation={null}
                        isSharedView={isSharedView}
                    />
                </div>
            </div>
        );
    }

    const chatAreaContent = (
        <div className="flex flex-col h-full w-full">
            {/* Chat tab content */}
            <div
                className={`flex flex-col h-full w-full ${state.activeTab === 'chat' ? 'block' : 'hidden'}`}
            >
                <div className="flex-1 overflow-y-auto custom-scrollbar">
                    <MemoizedAutonomousMessageList
                        messages={messages}
                        isLoading={isStreaming}
                        onNextStepClick={handleNextStepClick}
                        onRestore={handleRestore}
                        confirmation={state.activeTab === 'chat' ? confirmation : null}
                        isSharedView={isSharedView}
                    />
                </div>
                {!isSharedView && (
                    <div className="flex-none w-full">
                        <MessageInput
                            value={state.inputValue}
                            onChange={handleInputChange}
                            onSendMessage={handleNewMessage}
                            disabled={isInputDisabled}
                            isStreaming={isStreaming}
                            onStop={onStopStreaming}
                            showExampleQuestions={true}
                            isEmptyConversation={messages.length === 0}
                            resourceType={resourceData?.type}
                            quotaInfo={quotaInfo}
                        />
                        {inputSectionChildren}
                    </div>
                )}
            </div>

            {/* Group chat tab content */}
            <div
                className={`flex flex-col h-full w-full ${state.activeTab === 'group-chat' ? 'block' : 'hidden'}`}
            >
                <div className="flex-1 overflow-y-auto custom-scrollbar">
                    <MemoizedAutonomousMessageList
                        messages={messages}
                        isLoading={isStreaming}
                        onNextStepClick={handleNextStepClick}
                        onRestore={handleRestore}
                        confirmation={state.activeTab === 'group-chat' ? confirmation : null}
                        groupChatOnly={true}
                        isSharedView={isSharedView}
                    />
                </div>
                {!isSharedView && (
                    <div className="flex-none w-full">
                        <MessageInput
                            value={state.inputValue}
                            onChange={handleInputChange}
                            onSendMessage={handleNewMessage}
                            disabled={isInputDisabled}
                            isStreaming={isStreaming}
                            onStop={onStopStreaming}
                            showExampleQuestions={true}
                            isEmptyConversation={groupChatMessages.length === 0}
                            resourceType={resourceData?.type}
                            quotaInfo={quotaInfo}
                        />
                        {inputSectionChildren}
                    </div>
                )}
            </div>
        </div>
    );

    const tabsConfig = [
        { id: 'chat', label: 'Chat', icon: <MessagesSquare className="h-4 w-4" /> },
        { id: 'group-chat', label: 'Group Chat', icon: <Users className="h-4 w-4" />, badge: groupChatMessages.length > 0 ? groupChatMessages.length : undefined }
    ];

    return (
        <div className="flex flex-col h-full w-full" style={{
            contain: 'layout size style',
            boxSizing: 'border-box',
            position: 'relative',
            maxHeight: '100vh',
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden',
            overscrollBehavior: 'none'
        }}>
            {/* Main container with proper layout for tool canvas */}
            <div className="relative h-full w-full flex flex-col" style={{
                contain: 'layout size style',
                boxSizing: 'border-box',
                position: 'relative'
            }}>
                {/* Main content */}
                <div className="flex-1 flex flex-col h-full w-full overflow-hidden" style={{
                    position: 'relative',
                    contain: 'layout size style'
                }}>
                    {state.isCanvasVisible && !disableCanvas ? (
                        <ResizablePanelGroup
                            direction="horizontal"
                            onLayout={handlePanelResize}
                            className="h-full"
                            style={{
                                height: '100%',
                                position: 'relative',
                                contain: 'layout size style',
                                boxSizing: 'border-box',
                                maxHeight: '100vh',
                                overscrollBehavior: 'none',
                                backfaceVisibility: 'hidden',
                                WebkitBackfaceVisibility: 'hidden',
                                perspective: 1000,
                                WebkitPerspective: 1000,
                                willChange: 'contents',
                                transform: 'translateZ(0)'
                            }}
                        >
                            {/* Main chat area panel */}
                            <ResizablePanel
                                defaultSize={state.panelSizes[0]}
                                minSize={30}
                                style={{
                                    transform: 'translateZ(0)',
                                    backfaceVisibility: 'hidden',
                                    WebkitBackfaceVisibility: 'hidden',
                                    boxSizing: 'border-box',
                                    contain: 'layout style',
                                    position: 'relative',
                                    zIndex: 1,
                                    height: '100%'
                                }}
                            >
                                <TabsContainer
                                    tabs={tabsConfig}
                                    activeTab={state.activeTab}
                                    onTabChange={handleTabChange}
                                    contextContent={<ChatPanelHeaderContent
                                        isCreatingConversation={isCreatingConversation}
                                        conversationId={session?.id}
                                        conversationTitle={session?.title}
                                        conversationCreatedAt={session?.timestamp?.toISOString()}
                                        modelProvider="bedrock"
                                        resourceId={state.resourceId || resourceId}
                                    />}
                                    actionsContent={<ChatPanelHeaderActionsContent
                                        onNewChat={handleNewChatCb}
                                        isCreatingConversation={isCreatingConversation}
                                        conversationId={session?.id}
                                        isSharedView={isSharedView}
                                    />}
                                >
                                    {chatAreaContent}
                                </TabsContainer>
                            </ResizablePanel>

                            {/* Resize handle */}
                            <ResizableHandle
                                withHandle
                                style={{
                                    position: 'relative',
                                    zIndex: 2,  // Ensure proper stacking context for handle
                                    transform: 'translateZ(0)',
                                    backfaceVisibility: 'hidden',
                                    WebkitBackfaceVisibility: 'hidden'
                                }}
                            />

                            {/* Charts & Data Canvas panel */}
                            <ResizablePanel
                                defaultSize={state.panelSizes[1]}
                                minSize={20}
                                style={{
                                    overflow: 'hidden',
                                    position: 'relative',
                                    isolation: 'isolate',
                                    contain: 'layout size style',
                                    boxSizing: 'border-box',
                                    zIndex: 1, // Ensure proper stacking context
                                    backfaceVisibility: 'hidden', // Prevent layout shifts
                                    WebkitBackfaceVisibility: 'hidden',
                                    transform: 'translateZ(0)',
                                    height: '100%'
                                }}
                            >
                                <ChartsCanvas
                                    isVisible={state.isCanvasVisible}
                                    toolCalls={state.activeToolCalls}
                                    displayComponents={state.displayComponents}
                                    canvasItemsWithAgent={state.canvasItemsWithAgent}
                                    resourceData={resourceData}
                                    streamingRecommendations={streamingRecommendations}
                                    setStreamingRecommendations={setStreamingRecommendations}
                                    isStreaming={isStreaming}
                                    defaultTab={defaultCanvasTab as 'resource' | 'charts' | 'planning'}
                                    onTabChange={(tab) => console.log('Tab changed:', tab)}
                                    lastMessage={messages.length > 0 ? messages[messages.length - 1] : undefined}
                                />
                            </ResizablePanel>
                        </ResizablePanelGroup>
                    ) : (
                        <TabsContainer
                            tabs={tabsConfig}
                            activeTab={state.activeTab}
                            onTabChange={handleTabChange}
                            contextContent={<ChatPanelHeaderContent
                                isCreatingConversation={isCreatingConversation}
                                conversationId={session?.id}
                                conversationTitle={session?.title}
                                conversationCreatedAt={session?.timestamp?.toISOString()}
                                modelProvider="bedrock"
                                resourceId={state.resourceId || resourceId}
                            />}
                            actionsContent={<ChatPanelHeaderActionsContent
                                onNewChat={handleNewChatCb}
                                isCreatingConversation={isCreatingConversation}
                                conversationId={session?.id}
                                isSharedView={isSharedView}
                            />}
                        >
                            {chatAreaContent}
                        </TabsContainer>
                    )}
                </div>
            </div>
        </div>
    );
}
