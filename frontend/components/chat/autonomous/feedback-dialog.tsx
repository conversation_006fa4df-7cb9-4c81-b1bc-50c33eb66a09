'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { FeedbackType } from "@/client/types.gen"

interface FeedbackDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    feedbackType: FeedbackType
    onSubmit: (reason: string) => void
    isSubmitting?: boolean
}

export function FeedbackDialog({
    open,
    onOpenChange,
    feedbackType,
    onSubmit,
    isSubmitting = false
}: FeedbackDialogProps) {
    const [reason, setReason] = useState("")

    const handleSubmit = () => {
        onSubmit(reason)
        // Reset form
        setReason("")
    }

    const handleClose = () => {
        onOpenChange(false)
        // Reset form when closing
        setReason("")
    }

    const isValidForm = feedbackType === 'good' || (feedbackType === 'bad' && reason.trim())

    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                    <DialogTitle>Help us improve</DialogTitle>
                    <DialogDescription>
                        We're sorry the response wasn't helpful. Please let us know what went wrong so we can improve.
                    </DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                        <Label htmlFor="reason" className="text-sm font-medium">
                            What went wrong? <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                            id="reason"
                            placeholder="Please describe what was wrong with the response..."
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            className="min-h-[100px]"
                            required
                        />
                    </div>
                </div>

                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={handleClose}
                        disabled={isSubmitting}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        disabled={!isValidForm || isSubmitting}
                    >
                        {isSubmitting ? "Submitting..." : "Submit Feedback"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
