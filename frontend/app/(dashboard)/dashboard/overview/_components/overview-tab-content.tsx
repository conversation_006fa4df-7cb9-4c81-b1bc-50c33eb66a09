'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatUSD } from '@/lib/currency';
import { TopServicesPieChart } from './top-services-pie-chart';
import { RecentSales } from './recent-sales';
import { useSavingsSummary } from '../../hooks/use-savings-summary';

export function OverviewTabContent({
  previousStartDate,
  previousEndDate,
  currentStartDate,
  currentEndDate,
  periodLabel
}: {
  previousStartDate: string;
  previousEndDate: string;
  currentStartDate: string;
  currentEndDate: string;
  periodLabel: string;
}) {
  const { data: savingsSummary, isLoading } = useSavingsSummary({
    startDate: currentStartDate,
    endDate: currentEndDate,
    previousStartDate,
    previousEndDate
  });

  return (
    <div className="space-y-6 p-1">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="rounded-3xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Monthly Savings ($)
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : savingsSummary ? (
              <>
                <div className="text-2xl font-bold">
                  {formatUSD(savingsSummary.potential_savings || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {savingsSummary.potential_savings_percentage_change >= 0
                    ? '+'
                    : ''}
                  {savingsSummary.potential_savings_percentage_change.toFixed(
                    1
                  )}
                  % from {periodLabel}
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
        <Card className="rounded-3xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Saving Opportunities
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : savingsSummary ? (
              <>
                <div className="text-2xl font-bold">
                  {Math.round(savingsSummary.save_opportunities)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {savingsSummary.save_opportunities_percentage_change >= 0
                    ? '+'
                    : ''}
                  {savingsSummary.save_opportunities_percentage_change.toFixed(
                    1
                  )}
                  % from {periodLabel}
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
        <Card className="rounded-3xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">Total Saved</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <path d="M2 10h20" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : savingsSummary ? (
              <>
                <div className="text-2xl font-bold">
                  {formatUSD(savingsSummary.total_saved)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {savingsSummary.total_saved_percentage_change >= 0 ? '+' : ''}
                  {savingsSummary.total_saved_percentage_change.toFixed(1)}%
                  from {periodLabel}
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
        <Card className="rounded-3xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">Active Saving</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : savingsSummary ? (
              <>
                <div className="text-2xl font-bold">
                  {Math.round(savingsSummary.active_saving)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {savingsSummary.active_saving_percentage_change >= 0
                    ? '+'
                    : ''}
                  {savingsSummary.active_saving_percentage_change.toFixed(1)}%
                  from {periodLabel}
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-7">
        <div className="col-span-4">
          <TopServicesPieChart
            startDate={currentStartDate}
            endDate={currentEndDate}
          />
        </div>
        <div className="col-span-4 md:col-span-3">
          <RecentSales startDate={currentStartDate} endDate={currentEndDate} />
        </div>
        {/* <div className="col-span-4">
          <AreaGraph />
        </div>
        <div className="col-span-4 md:col-span-3">
          <PieGraph />
        </div> */}
      </div>
    </div>
  );
}
