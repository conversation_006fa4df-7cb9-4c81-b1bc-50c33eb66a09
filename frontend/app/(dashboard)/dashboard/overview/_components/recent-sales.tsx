'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { formatUSD } from '@/lib/currency';
import { Skeleton } from '@/components/ui/skeleton';
import { AwsIcon } from '@/components/aws-icons';
import { Badge } from '@/components/ui/badge';

import { useTopSavings } from '../../hooks/use-top-savings';
import { PagePath } from '@/constants/route';

// Helper function to map resource type to icon key
const getIconKey = (resourceType: string): string => {
  const typeMap: { [key: string]: string } = {
    'ec2': 'ec2',
    'rds': 'rds',
    // Add more mappings as needed
  };
  return typeMap[resourceType.toLowerCase()] || 'ec2'; // Default to EC2 if no match
};

// Helper function to get effort badge variant
const getEffortVariant = (effort: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (effort.toLowerCase()) {
    case 'low': return 'default';
    case 'medium': return 'secondary';
    case 'high': return 'destructive';
    default: return 'outline';
  }
};

// Helper function to get risk badge variant
const getRiskVariant = (risk: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (risk.toLowerCase()) {
    case 'low': return 'default';
    case 'medium': return 'secondary';
    case 'high': return 'destructive';
    default: return 'outline';
  }
};

interface RecentSalesProps {
  startDate?: string;
  endDate?: string;
}

export function RecentSales({ startDate, endDate }: RecentSalesProps) {
  const { data: topSavings, isLoading } = useTopSavings(startDate, endDate);

  const content = isLoading ? (
    Array(5)
      .fill(0)
      .map((_, i) => (
        <div key={i} className="mb-4 flex items-center space-x-4 p-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-[200px]" />
            <div className="flex space-x-2">
              <Skeleton className="h-5 w-12 rounded-full" />
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
          </div>
          <Skeleton className="h-4 w-[100px]" />
        </div>
      ))
  ) : (
    <div className="space-y-3">
      {topSavings?.data?.length ? (
        topSavings?.data.map((recommendation) => (
          <a
            key={recommendation.title}
            href={`${PagePath.Recommendations}/${recommendation.id}`}
            className="flex items-center space-x-4 rounded-xl p-3 transition-colors hover:bg-muted/50 border border-transparent hover:border-border"
          >
            <div className="h-12 w-12 flex items-center justify-center rounded-xl overflow-hidden flex-shrink-0">
              {AwsIcon[getIconKey(recommendation.resource.type)]({
                size: 28,
                className: "text-foreground"
              })}
            </div>

            <div className="flex-1 space-y-2">
              <p className="text-sm font-medium leading-none">
                {recommendation.title}
              </p>
              <div className="flex items-center space-x-2">
                <Badge variant={getEffortVariant(recommendation.effort)} className="text-xs px-2 py-0.5">
                  Effort: {recommendation.effort}
                </Badge>
                <Badge variant={getRiskVariant(recommendation.risk)} className="text-xs px-2 py-0.5">
                  Risk: {recommendation.risk}
                </Badge>
              </div>
            </div>

            <div className="text-right">
              <div className="text-sm font-medium text-green-600">
                {formatUSD(recommendation.potential_savings)}
              </div>
              <div className="text-xs text-muted-foreground">
                / month
              </div>
            </div>
          </a>
        ))
      ) : (
        <div className="text-center py-8">
          <p className="text-sm text-muted-foreground">No data found</p>
        </div>
      )}
    </div>
  );

  return (
    <Card className="h-full rounded-3xl">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-lg font-semibold">Top 5 Potential Monthly Savings</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">Recommendations with effort and risk assessment</p>
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          className="h-5 w-5 text-muted-foreground"
        >
          <path d="M16 6h6M16 10h6M16 14h6M16 18h6M4 6h6M4 10h6M4 14h6M4 18h6" />
          <circle cx="8" cy="6" r="2" />
          <circle cx="8" cy="10" r="2" />
          <circle cx="8" cy="14" r="2" />
          <circle cx="8" cy="18" r="2" />
        </svg>
      </CardHeader>
      <CardContent className="pt-0">{content}</CardContent>
    </Card>
  );
}
