'use client';
import { useQueryState } from 'nuqs';
import { DateRange } from 'react-day-picker';
import {
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  subMonths,
  differenceInDays,
  subDays,
  parseISO
} from 'date-fns';
import { useMemo } from 'react';

import { AreaGraph } from './area-graph';
import { AwsCostSaving } from './aws-cost-saving';
import { PieGraph } from './pie-graph';
import { CalendarDateRangePicker } from '@/components/date-range-picker';
import PageContainer from '@/components/layout/page-container';
import { RecentSales } from './recent-sales';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSavingsSummary } from '@/app/(dashboard)/dashboard/hooks/use-savings-summary';
import { formatUSD } from '@/lib/currency';
import { Skeleton } from '@/components/ui/skeleton';

// @depercated
export default function OverViewPage() {
  const [dateRange, setDateRange] = useQueryState('dateRange', {
    parse: (value: string) => {
      const [from, to] = value.split(',');
      return {
        from: from ? parseISO(from) : undefined,
        to: to ? parseISO(to) : undefined
      };
    },
    serialize: (value: DateRange | undefined) => {
      if (!value?.from && !value?.to) return '';
      return `${value.from?.toISOString() || ''},${
        value.to?.toISOString() || ''
      }`;
    },
    defaultValue: {
      from: undefined,
      to: undefined
    }
  });

  const { defaultEnd, defaultStart, now } = useMemo(() => {
    const now = new Date();
    return {
      defaultEnd: endOfMonth(now),
      defaultStart: startOfMonth(now),
      now
    };
  }, []);

  const {
    currentStartDate,
    currentEndDate,
    previousStartDate,
    previousEndDate,
    periodLabel
  } = useMemo(() => {
    if (!dateRange?.from && !dateRange?.to) {
      const previousStart = startOfMonth(subMonths(now, 1));
      const previousEnd = endOfMonth(previousStart);

      return {
        currentStartDate: defaultStart.toISOString(),
        currentEndDate: defaultEnd.toISOString(),
        previousStartDate: previousStart.toISOString(),
        previousEndDate: previousEnd.toISOString(),
        periodLabel: 'last month'
      };
    }

    if (!dateRange?.from || !dateRange?.to) {
      const currentDate = (dateRange?.from || dateRange?.to) as Date;
      const previousDate = subDays(currentDate, 1);

      return {
        currentStartDate: startOfDay(currentDate).toISOString(),
        currentEndDate: endOfDay(currentDate).toISOString(),
        previousStartDate: startOfDay(previousDate).toISOString(),
        previousEndDate: endOfDay(previousDate).toISOString(),
        periodLabel: 'yesterday'
      };
    }

    // Calculate previous period with same duration
    const duration = differenceInDays(dateRange.to, dateRange.from);
    const previousEnd = startOfDay(dateRange.from);
    const previousStart = subMonths(previousEnd, duration);

    return {
      currentStartDate: startOfDay(dateRange.from).toISOString(),
      currentEndDate: endOfDay(dateRange.to).toISOString(),
      previousStartDate: previousStart.toISOString(),
      previousEndDate: previousEnd.toISOString(),
      periodLabel: 'last period'
    };
  }, [dateRange.from, dateRange.to, defaultEnd, defaultStart, now]);

  const { data: savingsSummary, isLoading } = useSavingsSummary({
    startDate: currentStartDate,
    endDate: currentEndDate,
    previousStartDate,
    previousEndDate
  });

  return (
    <PageContainer scrollable>
      <div className="space-y-2">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-2xl font-bold tracking-tight">
            Hi, Welcome back 👋
          </h2>
          <div className="hidden items-center space-x-2 md:flex">
            <CalendarDateRangePicker
              value={dateRange}
              defaultValue={
                !dateRange.from && !dateRange.to
                  ? {
                      from: defaultStart,
                      to: defaultEnd
                    }
                  : dateRange
              }
              onChange={(date) =>
                setDateRange({
                  from: date?.from,
                  to: date?.to
                })
              }
            />
            <Button>Download</Button>
          </div>
        </div>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics" disabled>
              Analytics
            </TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-0">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 rounded-3xl">
                  <CardTitle className="text-sm font-medium">
                    Monthly Savings ($)
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <>
                      <Skeleton className="mb-2 h-8 w-[100px]" />
                      <Skeleton className="h-4 w-[140px]" />
                    </>
                  ) : savingsSummary ? (
                    <>
                      <div className="text-2xl font-bold">
                        {formatUSD(savingsSummary.potential_savings || 0)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {savingsSummary.potential_savings_percentage_change >= 0
                          ? '+'
                          : ''}
                        {savingsSummary.potential_savings_percentage_change.toFixed(
                          1
                        )}
                        % from {periodLabel}
                      </p>
                    </>
                  ) : null}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 rounded-3xl">
                  <CardTitle className="text-sm font-medium">
                    Saving Opportunities
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <>
                      <Skeleton className="mb-2 h-8 w-[100px]" />
                      <Skeleton className="h-4 w-[140px]" />
                    </>
                  ) : savingsSummary ? (
                    <>
                      <div className="text-2xl font-bold">
                        {Math.round(savingsSummary.save_opportunities)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {savingsSummary.save_opportunities_percentage_change >=
                        0
                          ? '+'
                          : ''}
                        {savingsSummary.save_opportunities_percentage_change.toFixed(
                          1
                        )}
                        % from {periodLabel}
                      </p>
                    </>
                  ) : null}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 rounded-3xl">
                  <CardTitle className="text-sm font-medium">
                    Total Saved
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <path d="M2 10h20" />
                  </svg>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <>
                      <Skeleton className="mb-2 h-8 w-[100px]" />
                      <Skeleton className="h-4 w-[140px]" />
                    </>
                  ) : savingsSummary ? (
                    <>
                      <div className="text-2xl font-bold">
                        {formatUSD(savingsSummary.total_saved)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {savingsSummary.total_saved_percentage_change >= 0
                          ? '+'
                          : ''}
                        {savingsSummary.total_saved_percentage_change.toFixed(
                          1
                        )}
                        % from {periodLabel}
                      </p>
                    </>
                  ) : null}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 rounded-3xl">
                  <CardTitle className="text-sm font-medium">
                    Active Saving
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                  </svg>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <>
                      <Skeleton className="mb-2 h-8 w-[100px]" />
                      <Skeleton className="h-4 w-[140px]" />
                    </>
                  ) : savingsSummary ? (
                    <>
                      <div className="text-2xl font-bold">
                        {Math.round(savingsSummary.active_saving)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {savingsSummary.active_saving_percentage_change >= 0
                          ? '+'
                          : ''}
                        {savingsSummary.active_saving_percentage_change.toFixed(
                          1
                        )}
                        % from {periodLabel}
                      </p>
                    </>
                  ) : null}
                </CardContent>
              </Card>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7">
              <div className="col-span-4">
                <AwsCostSaving
                  startDate={currentStartDate}
                  endDate={currentEndDate}
                />
              </div>
              <div className="col-span-4 md:col-span-3">
                <RecentSales
                  startDate={currentStartDate}
                  endDate={currentEndDate}
                />
              </div>
              {/* <div className="col-span-4">
                <AreaGraph />
              </div>
              <div className="col-span-4 md:col-span-3">
                <PieGraph />
              </div> */}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  );
}
