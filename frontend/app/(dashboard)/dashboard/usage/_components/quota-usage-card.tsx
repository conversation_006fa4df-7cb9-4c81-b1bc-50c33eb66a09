import {
    <PERSON>,
    CardContent,
    CardHeader,
    CardTitle
} from '@/components/ui/card';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRightIcon, CreditCard } from 'lucide-react';
import Link from 'next/link';

interface QuotaUsageCardProps {
    quotaInfo: {
        quota_used: number;
        quota_limit: number;
        quota_remaining: number;
        usage_percentage: number;
    } | undefined;
}

export function QuotaUsageCard({ quotaInfo }: QuotaUsageCardProps) {
    const [isBlinking, setIsBlinking] = useState(false);

    // Determine quota status
    const getQuotaStatus = () => {
        const percentage = quotaInfo?.usage_percentage || 0;
        if (percentage >= 100) {
            return percentage === 100 ? "OUT OF QUOTA" : "OVER QUOTA";
        }
        return null;
    };

    const quotaStatus = getQuotaStatus();
    const percentage = quotaInfo?.usage_percentage || 0;
    const isOverQuota = percentage > 100;
    const isAtQuota = percentage === 100;
    const isNearQuota = percentage >= 80 && percentage < 100;

    // Set up blinking effect for over quota
    useEffect(() => {
        if (isOverQuota) {
            const interval = setInterval(() => {
                setIsBlinking(prev => !prev);
            }, 2000);
            return () => clearInterval(interval);
        }
    }, [isOverQuota]);

    return (
        <Card
            className={`rounded-3xl transition-all duration-300 ${
                isOverQuota
                    ? `border-red-300 ${isBlinking ? 'shadow-md shadow-red-200' : 'shadow-sm'}`
                    : isAtQuota
                        ? 'border-amber-300 shadow-amber-100'
                        : ''
            }`}
        >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium flex items-center">
                    Quota Usage
                    {(isOverQuota || isAtQuota) && (
                        <span className={`ml-2 inline-block w-2 h-2 rounded-full ${
                            isOverQuota ? 'bg-red-500' : 'bg-amber-500'
                        } ${isOverQuota ? 'animate-ping' : ''}`}></span>
                    )}
                </CardTitle>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke={isOverQuota ? "rgb(220, 38, 38)" : isAtQuota ? "rgb(217, 119, 6)" : "currentColor"}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className={`h-4 w-4 ${isOverQuota ? 'text-red-600' : isAtQuota ? 'text-amber-600' : 'text-muted-foreground'}`}
                >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                </svg>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                    <div className={`text-2xl font-bold ${
                        isOverQuota ? 'text-red-600' : isAtQuota ? 'text-amber-600' : ''
                    }`}>
                        {quotaInfo?.usage_percentage?.toFixed(2) || '0.00'}%
                    </div>
                    {quotaStatus && (
                        <div className={`px-3 py-1 text-xs font-semibold rounded-full ${
                            isOverQuota
                                ? "bg-red-100 text-red-800 border border-red-300 animate-pulse"
                                : "bg-amber-100 text-amber-800 border border-amber-300"
                        }`}>
                            {quotaStatus}
                        </div>
                    )}
                </div>
                <div className="w-full h-3 mt-3 rounded-full overflow-hidden bg-slate-200 relative">
                    <div
                        className={`h-full ${
                            isOverQuota
                                ? 'bg-red-500'
                                : isAtQuota || isNearQuota
                                    ? 'bg-amber-500'
                                    : 'bg-blue-500'
                        } ${isOverQuota ? 'animate-pulse' : ''}`}
                        style={{
                            width: `${Math.min(percentage, 100)}%`,
                            transition: 'width 0.5s ease-in-out'
                        }}
                    />
                    {isNearQuota && !isAtQuota && !isOverQuota && (
                        <div className="absolute top-0 right-0 h-full w-1 bg-amber-300 animate-pulse"
                             style={{ right: '20%' }}></div>
                    )}
                </div>
                <div className="mt-3 text-xs text-muted-foreground flex justify-between">
                    <span>
                        <span className="font-medium">{quotaInfo?.quota_used?.toLocaleString() || '0'}</span> / {quotaInfo?.quota_limit?.toLocaleString() || '0'} messages used
                    </span>
                    <span className={`font-medium ${isOverQuota || isAtQuota ? 'text-red-500' : ''}`}>
                        {quotaInfo?.quota_remaining?.toLocaleString() || '0'} remaining
                    </span>
                </div>
                {quotaStatus && (
                    <div className={`mt-3 p-2 text-xs rounded-xl ${
                        isOverQuota
                            ? "bg-red-50 text-red-700 border border-red-100"
                            : "bg-amber-50 text-amber-700 border border-amber-100"
                    }`}>
                        <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <span>
                                {isOverQuota
                                    ? "You've exceeded your quota limit. Consider upgrading your plan for uninterrupted service."
                                    : "You've reached your quota limit. Consider upgrading your plan for more usage."}
                            </span>
                        </div>
                    </div>
                )}

                {/* Purchase Button */}
                <div className="mt-4 flex justify-center">
                    <Link href="/purchase" passHref>
                        <Button
                            size="sm"
                            className={`group transition-all font-medium px-4 py-2 ${
                                isOverQuota || isAtQuota
                                    ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md hover:shadow-lg'
                                    : isNearQuota
                                        ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white shadow-md hover:shadow-lg'
                                        : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-sm hover:shadow-md'
                            }`}
                        >
                            <div className="flex items-center gap-2">
                                <CreditCard className="h-3 w-3" />
                                <span className="text-sm">Upgrade Plan</span>
                                <ArrowRightIcon className="h-3 w-3 transition-transform group-hover:translate-x-1" />
                            </div>
                        </Button>
                    </Link>
                </div>
            </CardContent>
        </Card>
    );
}
