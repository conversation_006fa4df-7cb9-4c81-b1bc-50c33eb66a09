// constants.ts
export const CHART_COLORS = {
    primary: {
        main: 'hsl(var(--primary))',
        muted: 'hsl(var(--primary) / 0.5)',
        subtle: 'hsl(var(--primary) / 0.2)'
    },
    secondary: {
        main: 'hsl(var(--secondary))',
        muted: 'hsl(var(--secondary) / 0.5)',
        subtle: 'hsl(var(--secondary) / 0.2)'
    },
    // Color palette for multiple data points
    palette: [
        'hsl(var(--primary))',
        'hsl(var(--secondary))',
        'hsl(var(--accent))',
        'hsl(var(--info))',
        'hsl(var(--success))',
        'hsl(var(--warning))'
    ]
};

export const CHART_STYLES = {
    tooltip: {
        className: 'rounded-lg border bg-popover text-popover-foreground p-2 shadow-lg backdrop-blur-sm',
        contentStyle: {
            background: 'hsl(var(--popover))',
            border: '1px solid hsl(var(--border))',
            borderRadius: '8px',
            color: 'hsl(var(--popover-foreground))',
            padding: '8px'
        },
        wrapperStyle: { outline: 'none' }
    },
    grid: {
        stroke: 'hsl(var(--border))',
        strokeDasharray: '3 3'
    },
    text: {
        fontSize: 12,
        fill: 'hsl(var(--muted-foreground))'
    }
};
