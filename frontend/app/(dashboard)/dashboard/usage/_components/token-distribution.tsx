'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';
import { CHART_COLORS, CHART_STYLES } from './constants';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface TokenDistributionProps {
  data: Array<{
    category: string;
    percentage: number;
  }>;
}

export function TokenDistribution({ data }: TokenDistributionProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  // Custom tooltip content
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-popover text-popover-foreground p-3 shadow-lg backdrop-blur-sm">
          <p className="font-medium text-foreground">{payload[0].payload.category}</p>
          <p className="text-sm text-muted-foreground">
            {payload[0].value}% of messages
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <ResponsiveContainer width="100%" height={200}>
        <BarChart
          data={data}
          onMouseMove={(e) => {
            if (e.activePayload) {
              setHoveredCategory(e.activePayload[0].payload.category);
            }
          }}
          onMouseLeave={() => setHoveredCategory(null)}
        >
          <defs>
            <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="0%"
                stopColor={CHART_COLORS.primary.main}
                stopOpacity={0.8}
              />
              <stop
                offset="100%"
                stopColor={CHART_COLORS.primary.muted}
                stopOpacity={0.5}
              />
            </linearGradient>
          </defs>
          <CartesianGrid {...CHART_STYLES.grid} />
          <XAxis
            dataKey="category"
            {...CHART_STYLES.text}
            tickMargin={8}
          />
          <YAxis
            {...CHART_STYLES.text}
            tickMargin={8}
            tickFormatter={(value) => `${value}%`}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: 'hsl(var(--muted) / 0.1)' }}
            wrapperStyle={{ outline: 'none' }}
          />
          <Bar
            dataKey="percentage"
            fill="url(#barGradient)"
            radius={[4, 4, 0, 0]}
            barSize={40}
            cursor="pointer"
          />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-4">
        {data.map((item) => (
          <div
            key={item.category}
            className={cn(
              "rounded-lg border p-3 transition-colors duration-200 hover:bg-accent",
              hoveredCategory === item.category && "bg-accent"
            )}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="h-2 w-2 rounded-full bg-primary" />
              <span className="font-medium text-lg">
                {item.percentage}%
              </span>
            </div>
            <div className="text-sm text-muted-foreground">
              {item.category}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
