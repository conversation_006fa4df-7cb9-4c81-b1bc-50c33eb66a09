'use client';
import { useQueryState } from 'nuqs';
import { DateRange } from 'react-day-picker';
import {
  startOfMonth,
  endOfMonth,
  differenceInDays,
  subDays,
  parseISO
} from 'date-fns';
import { useMemo } from 'react';

import { OverviewTabContent } from './overview/_components/overview-tab-content';
import { CalendarDateRangePicker } from '@/components/date-range-picker';
import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { UrlTabs, TabItem } from '@/components/url-tabs';
import UsageTabContent from './usage/_components/usage-tab-content';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { UserInfo } from '@/types/common.enum';
import clientCookie from 'js-cookie';
import useAuth from '@/hooks/useAuth';
import { useState, useEffect } from 'react';
import { Workspace } from '@/client';
import { Building } from 'lucide-react';
import { PageHeader } from '@/components/layout/page-header';

// @depercated
export default function OverViewPage() {
  const { user } = useAuth();
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState<string>('');
  const [dateRange, setDateRange] = useQueryState('dateRange', {
    parse: (value: string) => {
      const [from, to] = value.split(',');
      return {
        from: from ? parseISO(from) : undefined,
        to: to ? parseISO(to) : undefined
      };
    },
    serialize: (value: DateRange | undefined) => {
      if (!value?.from && !value?.to) return '';
      return `${value.from?.toISOString() || ''},${
        value.to?.toISOString() || ''
      }`;
    },
    defaultValue: {
      from: undefined,
      to: undefined
    }
  });

  const { defaultEnd, defaultStart, now } = useMemo(() => {
    const now = new Date();
    return {
      defaultEnd: endOfMonth(now),
      defaultStart: startOfMonth(now),
      now
    };
  }, []);

  // Calculate date ranges
  const currentStartDate = dateRange?.from
    ? dateRange.from.toISOString()
    : defaultStart.toISOString();
  const currentEndDate = dateRange?.to
    ? dateRange.to.toISOString()
    : defaultEnd.toISOString();

  // Calculate previous period
  const diffDays = differenceInDays(
    new Date(currentEndDate),
    new Date(currentStartDate)
  );
  const previousStartDate = subDays(
    new Date(currentStartDate),
    diffDays + 1
  ).toISOString();
  const previousEndDate = subDays(
    new Date(currentStartDate),
    1
  ).toISOString();

  // Get period label based on the date range
  const periodLabel = diffDays > 27 ? 'month' : 'day';

  // Get all available workspaces from user data
  const workspaces = useMemo(() => {
    if (!user) return [];

    const ownWorkspaces = user.own_workspaces || [];
    const userWorkspaces = user.workspaces || [];

    // Combine and deduplicate workspaces
    const allWorkspaces = [...ownWorkspaces, ...userWorkspaces];
    const uniqueWorkspaces = allWorkspaces.reduce((acc, workspace) => {
      if (!acc.find((w) => w.id === workspace.id)) {
        acc.push(workspace);
      }
      return acc;
    }, [] as Workspace[]);

    return uniqueWorkspaces;
  }, [user]);

  // Initialize selected workspace from cookie or first available workspace
  useEffect(() => {
    if (workspaces.length > 0) {
      const cookieWorkspaceId = clientCookie.get(UserInfo.WorkspacesID);

      if (
        cookieWorkspaceId &&
        workspaces.some((w) => w.id === cookieWorkspaceId)
      ) {
        setSelectedWorkspaceId(cookieWorkspaceId);
      } else if (workspaces[0]?.id) {
        // If no workspace in cookie or invalid, use first workspace
        setSelectedWorkspaceId(workspaces[0].id);
        clientCookie.set(UserInfo.WorkspacesID, workspaces[0].id);
      }
    }
  }, [workspaces]);

  // Handle workspace change
  const handleWorkspaceChange = (workspaceId: string) => {
    if (workspaceId) {
      setSelectedWorkspaceId(workspaceId);
      clientCookie.set(UserInfo.WorkspacesID, workspaceId);
    }
  };

  const tabs: TabItem[] = [
    {
      value: 'overview',
      label: 'Overview',
      content: (
        <OverviewTabContent
          previousEndDate={previousEndDate}
          previousStartDate={previousStartDate}
          currentStartDate={currentStartDate}
          currentEndDate={currentEndDate}
          periodLabel={periodLabel}
        />
      )
    },
    {
      value: 'usage',
      label: 'Usage Analytics',
      content: (
        <UsageTabContent
          currentStartDate={currentStartDate}
          currentEndDate={currentEndDate}
        />
      )
    }
  ];

  return (
    <PageContainer scrollable>
      <div className="space-y-2">
        <PageHeader
          title="Hi, Welcome back 👋"
          description="View your cloud resource usage and analytics"
          actions={
            <div className="hidden items-center space-x-2 md:flex">
              <CalendarDateRangePicker
                value={dateRange}
                defaultValue={
                  !dateRange.from && !dateRange.to
                    ? {
                        from: defaultStart,
                        to: defaultEnd
                      }
                    : dateRange
                }
                onChange={(date) =>
                  setDateRange({
                    from: date?.from,
                    to: date?.to
                  })
                }
              />
              <Button>Download</Button>
            </div>
          }
        />
        <UrlTabs
          tabs={tabs}
          defaultTab="overview"
          className="space-y-0"
        />
      </div>
    </PageContainer>
  );
}
