'use client';

import { ChevronDownIcon, ExternalLinkIcon } from '@radix-ui/react-icons';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  CreditCard,
  DollarSign,
  Clock,
  Loader2,
  Server,
  MapPin,
  Tag,
  Settings,
  AlertTriangle,
  CheckCircle2,
  CircleX,
  Circle,
  ArrowLeft
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import {
  ApiError,
  RecommendationPublic,
  RecommendationsService,
  RecommendationStatus,
} from '@/client';
import { handleError } from '@/components/utils/handle-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { PageHeader } from '@/components/layout/page-header';
import { PagePath } from '@/constants/route';
import { formatUSD } from '@/lib/currency';
import { openUrl } from '@/lib/utils';
import { getSeverityColor, getStatusColor } from './status-color';
import { CacheKey } from '@/components/utils/cache-key';
import { useState } from 'react';
import { AnalyzeViewer } from './analyzeViewer';
import { AwsIcon } from '@/components/aws-icons';

type ResourceFormProps = {
  data: RecommendationPublic;
  pageTitle: string;
};

const getRecommendationTypeLabel = (type: string) => {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
      return <Circle className="h-4 w-4" />;
    case 'in_progress':
      return <Clock className="h-4 w-4" />;
    case 'implemented':
      return <CheckCircle2 className="h-4 w-4" />;
    case 'ignored':
      return <CircleX className="h-4 w-4" />;
    default:
      return <Circle className="h-4 w-4" />;
  }
};

const getRiskIcon = (risk: string) => {
  switch (risk?.toLowerCase()) {
    case 'high':
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'medium':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case 'low':
      return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    default:
      return <Circle className="h-4 w-4 text-gray-400" />;
  }
};

export default function RecommendationDetail({ data }: ResourceFormProps) {
  const queryClient = useQueryClient();
  const router = useRouter();
  const statusColors = getStatusColor(data.status ?? '');
  const effortColor = getSeverityColor(data.effort ?? '');
  const riskColor = getSeverityColor(data.risk ?? '');
  const [analyzeResult, setAnalyzeResult] = useState<string>();

  const { isPending: isStatusChanging, mutateAsync: changeStatus } = useMutation({
    mutationFn: (status: RecommendationStatus) =>
      RecommendationsService.updateRecommendationStatus({
        id: data.id,
        status
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [data.id] });
      queryClient.invalidateQueries({ queryKey: [CacheKey.Recommendations] });
    },
    onError: (err: ApiError) => {
      handleError(err);
    }
  });

  const handleStatusChange = (status: RecommendationStatus) => {
    changeStatus(status);
  };

  const getStatusLabel = (status: RecommendationStatus) => {
    switch (status) {
      case 'pending':
        return 'Mark as Pending';
      case 'in_progress':
        return 'Mark as In Progress';
      case 'implemented':
        return 'Mark as Implemented';
      case 'ignored':
        return 'Mark as Ignored';
      default:
        return status;
    }
  };

  const ResourceIcon = AwsIcon[data.resource?.type?.toLowerCase()] || Server;

  // Create the actions for the header
  const headerActions = (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => router.push('/recommendations')}
        className="flex items-center gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        Back
      </Button>

      {/* Status Change Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={isStatusChanging}>
          <Button variant="outline" size="sm" disabled={isStatusChanging}>
            {isStatusChanging && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Change Status <ChevronDownIcon className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {(['pending', 'in_progress', 'implemented', 'ignored'] as const).map((status) => (
            <DropdownMenuItem
              key={status}
              onClick={() => handleStatusChange(status)}
              disabled={data.status === status}
              className={data.status === status ? 'opacity-50' : ''}
            >
              {getStatusIcon(status)}
              <span className="ml-2">{getStatusLabel(status)}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header with consistent pattern */}
      <PageHeader
        title={data.title}
        description={getRecommendationTypeLabel(data.type)}
        actions={headerActions}
        additionalContent={
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={`${statusColors.background} ${statusColors.text}`}>
              {getStatusIcon(data.status ?? '')}
              <span className="ml-1">{data.status?.toUpperCase() || 'UNKNOWN'}</span>
            </Badge>
            <Badge variant="secondary">
              {getRecommendationTypeLabel(data.type)}
            </Badge>
          </div>
        }
      />

      {/* Key Metrics Cards - Improved responsive grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Savings</CardTitle>
            <DollarSign className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatUSD(data.potential_savings)}
            </div>
            <p className="text-xs text-muted-foreground">
              Estimated monthly reduction
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Implementation Effort</CardTitle>
            <Clock className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${effortColor.text}`}>
              {data.effort?.toUpperCase()}
            </div>
            <p className="text-xs text-muted-foreground">
              Time and resources required
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
            {getRiskIcon(data.risk ?? '')}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${riskColor.text}`}>
              {data.risk?.toUpperCase()}
            </div>
            <p className="text-xs text-muted-foreground">
              Implementation risk assessment
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Annual Impact</CardTitle>
            <CreditCard className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatUSD(data.potential_savings * 12)}
            </div>
            <p className="text-xs text-muted-foreground">
              Projected yearly savings
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Two-column layout for main content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content - Analysis (2/3 width) */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>Recommendation Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <AnalyzeViewer
                content={analyzeResult || data.description || 'Analyzing recommendation...'}
              />
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Resource Context (1/3 width) */}
        <div className="space-y-6">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <div className="rounded-md overflow-hidden flex-shrink-0">
                  <ResourceIcon className="h-5 w-5" />
                </div>
                <span>Affected Resource</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Resource Name</p>
                  <p className="font-medium text-sm break-words">{data.resource?.name}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Type</p>
                  <div className="flex items-center space-x-2">
                    <div className="rounded-md overflow-hidden flex-shrink-0">
                      <ResourceIcon className="h-4 w-4" />
                    </div>
                    <span className="font-medium text-sm">{data.resource?.type}</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Region</p>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4" />
                    <span className="font-medium text-sm">{data.resource?.region}</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <Badge variant={data.resource?.status === 'running' ? 'default' : 'secondary'} className="text-xs">
                    {data.resource?.status}
                  </Badge>
                </div>
              </div>

              {/* Resource Tags */}
              {data.resource?.tags && Object.keys(data.resource.tags).length > 0 && (
                <div className="space-y-2 pt-2 border-t">
                  <p className="text-sm font-medium text-muted-foreground flex items-center space-x-1">
                    <Tag className="h-4 w-4" />
                    <span>Tags</span>
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(data.resource.tags).slice(0, 3).map(([key, value]) => (
                      <Badge key={key} variant="outline" className="text-xs">
                        {key}: {String(value)}
                      </Badge>
                    ))}
                    {Object.keys(data.resource.tags).length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{Object.keys(data.resource.tags).length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Quick Resource Stats */}
              <div className="pt-3 border-t space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Total Recommendations:</span>
                  <span className="font-medium text-foreground">{data.resource?.total_recommendation}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Total Potential Savings:</span>
                  <span className="font-medium text-green-600">{formatUSD(data.resource?.total_potential_saving || 0)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
