import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Share2, Link, X, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ShareChatService } from '@/client';
import { createPortal } from 'react-dom';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip';

interface ShareButtonProps {
  conversationId: string;
}

// Helper to fetch static share page HTML
async function fetchCurrentPageHtml() {
  const url = window.location.href;
  const response = await fetch(url);
  if (!response.ok) throw new Error('Failed to fetch current page');
  const html = await response.text();
  return html;
}

export function ShareButton({ conversationId }: ShareButtonProps) {
  const { toast } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [shareId, setShareId] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Fetch share link status when modal opens
  const handleOpenModal = async () => {
    setIsModalOpen(true);
    setIsLoading(true);
    try {
      const response = await ShareChatService.getShareLink({ conversationId });
      setShareId(response.share_id);
    } catch {
      setShareId(undefined);
    } finally {
      setIsLoading(false);
    }
  };

  // Create or update share link
  const handleCreateOrUpdate = async () => {
    setIsLoading(true);
    try {
      const response = await ShareChatService.createShareLink({ conversationId });
      setShareId(response.share_id);
      toast({
        title: 'Share link created',
        description: 'The conversation is now shared',
      });
      // Fetch and print the current page HTML
      try {
        const html = await fetchCurrentPageHtml();
        console.log('Current page HTML:', html);
      } catch (err) {
        console.error('Failed to fetch current page:', err);
      }
    } catch {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to create or update share link',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Revoke share link
  const handleRevoke = async () => {
    setIsLoading(true);
    try {
      await ShareChatService.revokeShareLink({ conversationId });
      setShareId(undefined);
      toast({
        title: 'Share link revoked',
        description: 'The conversation is no longer shared',
      });
    } catch {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to revoke share link',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = async () => {
    if (!shareId) return;
    const shareUrl = `${window.location.origin}/share/${shareId}`;
    await navigator.clipboard.writeText(shareUrl);
    toast({
      title: 'Link copied',
      description: 'Share link copied to clipboard',
    });
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              'relative h-8 w-8 border-none px-0',
              'hover:bg-primary/10 dark:hover:bg-primary/20',
              'text-muted-foreground hover:text-primary',
              'flex items-center justify-center',
              shareId ? 'text-primary' : ''
            )}
            onClick={handleOpenModal}
            aria-label="Share"
          >
            <Share2 className="h-5 w-5" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Share</TooltipContent>
      </Tooltip>
      {isModalOpen && (
        <ShareModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          shareId={shareId}
          isLoading={isLoading}
          onCopy={handleCopyLink}
          onCreateOrUpdate={handleCreateOrUpdate}
          onRevoke={handleRevoke}
        />
      )}
    </>
  );
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  shareId?: string;
  isLoading: boolean;
  onCopy: () => void;
  onCreateOrUpdate: () => void;
  onRevoke: () => void;
}

export function ShareModal({ isOpen, onClose, shareId, isLoading, onCopy, onCreateOrUpdate, onRevoke }: ShareModalProps) {
  if (!isOpen) return null;
  if (typeof window === 'undefined') return null;
  const shareUrl = shareId ? `${window.location.origin}/share/${shareId}` : '';

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-background dark:bg-popover text-foreground dark:text-popover-foreground rounded-lg shadow-lg p-6 w-full max-w-md relative border border-border">
        <button
          className="absolute top-3 right-3 text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>
        <h2 className="text-lg font-semibold mb-2">Update public share link for this chat</h2>
        <p className="text-sm text-muted-foreground mb-4">
          Name, custom instructions, and any messages you add after sharing will remain private.
        </p>
        {shareId ? (
          <>
            <div className="flex items-center mb-4 gap-2">
              <div className="relative flex-1">
                <input
                  type="text"
                  className="w-full border border-border rounded-3xl px-3 py-2 pr-10 text-foreground bg-input focus:ring-2 focus:ring-primary h-10 dark:bg-input dark:text-foreground"
                  value={shareUrl}
                  readOnly
                  aria-label="Shareable link"
                />
                <button
                  type="button"
                  onClick={onCopy}
                  disabled={isLoading}
                  className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-3xl hover:bg-muted focus:outline-none"
                  aria-label="Copy share link"
                  title="Copy share link"
                >
                  <Link className="h-4 w-4" />
                </button>
              </div>
              <Button
                variant="secondary"
                className="h-8 px-3 text-sm font-medium"
                onClick={onCreateOrUpdate}
                disabled={isLoading}
                aria-label="Update share link"
                title="Update share link"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : null} Update link
              </Button>
            </div>
            <Button
              variant="destructive"
              className="w-full mb-2 h-10 text-base font-semibold"
              onClick={onRevoke}
              disabled={isLoading}
              aria-label="Revoke share link"
            >
              {isLoading ? <span className="animate-spin mr-2"><Loader2 className="h-4 w-4" /></span> : <X className="h-4 w-4 mr-1" />} Revoke link
            </Button>
          </>
        ) : (
          <Button
            variant="default"
            className="w-full mb-2 h-10 text-base font-semibold"
            onClick={onCreateOrUpdate}
            disabled={isLoading}
            aria-label="Create share link"
          >
            {isLoading ? <span className="animate-spin mr-2"><Loader2 className="h-4 w-4" /></span> : <Share2 className="h-4 w-4 mr-1" />} Create share link
          </Button>
        )}
      </div>
    </div>
  );
  return createPortal(modalContent, document.body);
}
