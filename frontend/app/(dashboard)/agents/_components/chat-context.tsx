import { createContext, useContext, ReactNode } from 'react';

export type ChatType = 'resource' | 'agent';

interface ChatContextType {
  chatType: ChatType;
}

const ChatContext = createContext<ChatContextType | undefined>({
  chatType: 'agent',
});

interface ChatProviderProps {
  children: ReactNode;
  chatType?: ChatType;
}

export function ChatProvider({ children, chatType = 'agent' }: ChatProviderProps) {
  return (
    <ChatContext.Provider value={{ chatType }}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
} 