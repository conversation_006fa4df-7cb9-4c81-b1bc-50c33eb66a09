'use client';

import { memo, useCallback } from 'react';
import { TabAutonomousChatContainer } from '@/components/chat/autonomous/chat-panel';
import { Message, Session } from '@/types/chat';
import { MessageActionType, ResourceRead, RecommendationPublic } from '@/client/types.gen';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { ChatProvider, ChatType } from './chat-context';

interface ChatPanelProps {
  messages: Message[];
  onSendMessage: (message: string, restore?: { messageId: string } | null, actionType?: MessageActionType) => void;
  isStreaming: boolean;
  isLoadingHistory: boolean;
  currentSession?: Session;
  onNewChat?: () => void;
  onStopStreaming?: () => void;
  role?: string;
  confirmation?: InterruptConfirmation | null;
  conversations?: Session[];
  onSelectConversation?: (id: string) => void;
  isCreatingConversation?: boolean;
  onRename?: (name: string) => void;
  onDelete?: () => void;
  onRenameHistoryItem?: (conversationId: string, name: string) => void;
  onDeleteHistoryItem?: (conversationId: string) => void;
  disableCanvas?: boolean;
  resourceData?: ResourceRead;
  streamingRecommendations?: RecommendationPublic[];
  setStreamingRecommendations?: React.Dispatch<React.SetStateAction<RecommendationPublic[]>>;
  defaultCanvasTab?: 'resource' | 'charts';
  quotaInfo?: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
  resourceId?: string;
  chatType?: ChatType;
  isSharedView?: boolean;
}

function AutonomousChatPanelComponent({
  messages,
  onSendMessage,
  isStreaming,
  isLoadingHistory,
  currentSession,
  onNewChat,
  onStopStreaming,
  confirmation,
  conversations = [],
  onSelectConversation,
  isCreatingConversation = false,
  onRename,
  onDelete,
  onRenameHistoryItem,
  onDeleteHistoryItem,
  disableCanvas = false,
  resourceData,
  streamingRecommendations = [],
  setStreamingRecommendations,
  defaultCanvasTab = 'charts',
  quotaInfo,
  resourceId,
  chatType,
  isSharedView = false,
}: ChatPanelProps) {
  // Memoized callbacks to prevent unnecessary re-renders
  const handleSendMessage = useCallback((message: string, restore?: { messageId: string } | null, actionType?: MessageActionType) => {
    onSendMessage(message, restore, actionType);
  }, [onSendMessage]);

  const handleNewChat = useCallback(() => {
    onNewChat?.();
  }, [onNewChat]);

  const handleStopStreaming = useCallback(() => {
    onStopStreaming?.();
  }, [onStopStreaming]);

  return (
    <ChatProvider chatType={chatType}>
      <div className="h-full w-full">
        <TabAutonomousChatContainer
          messages={messages}
          onSendMessage={handleSendMessage}
          isStreaming={isStreaming}
          isLoadingHistory={isLoadingHistory}
          session={currentSession}
          onNewChat={handleNewChat}
          onStopStreaming={handleStopStreaming}
          confirmation={confirmation}
          isCreatingConversation={isCreatingConversation}
          disableCanvas={disableCanvas}
          resourceData={resourceData}
          streamingRecommendations={streamingRecommendations}
          setStreamingRecommendations={setStreamingRecommendations}
          defaultCanvasTab={defaultCanvasTab}
          quotaInfo={quotaInfo}
          resourceId={resourceId}
          isSharedView={isSharedView}
        />
      </div>
    </ChatProvider>
  );
}

// Export memoized component to prevent unnecessary re-renders
export const AutonomousChatPanel = memo(AutonomousChatPanelComponent);
