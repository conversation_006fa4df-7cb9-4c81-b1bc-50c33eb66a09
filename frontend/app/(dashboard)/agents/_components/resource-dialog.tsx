'use client';

import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RadioGroup } from '@/components/ui/radio-group';
import { ResourcesService } from '@/client';
import { AgentType } from '@/client/types.gen';
import { Badge } from '@/components/ui/badge';
import { getStatusColor } from '../../resources/_components/status-color';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronUp, Loader2, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface ResourceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  agentType?: AgentType | null;
}

export function ResourceDialog({ isOpen, onClose, agentId, agentType }: ResourceDialogProps) {
  const router = useRouter();
  const [selectedResource, setSelectedResource] = useState<string | null>(null);
  const [expandedResources, setExpandedResources] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch resources query
  const { data: resources, isLoading: isLoadingResources } = useQuery({
    queryKey: ['resources'],
    queryFn: async () => {
      const response = await ResourcesService.readResources();
      return response.data || [];
    }
  });

  const handleStartChat = () => {
    const searchParams = new URLSearchParams();
    // Set autonomous mode based on agent type
    searchParams.set('autonomous', agentType === 'autonomous_agent' ? 'true' : 'false');

    // Add resource_id if a resource is selected
    if (selectedResource) {
      searchParams.set('resource_id', selectedResource);
    }

    router.push(`/agents/${agentId}?${searchParams.toString()}`);
    onClose();
  };

  const toggleResourceExpansion = (resourceId: string) => {
    setExpandedResources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(resourceId)) {
        newSet.delete(resourceId);
      } else {
        newSet.add(resourceId);
      }
      return newSet;
    });
  };

  const filteredResources = useCallback(() => {
    if (!resources) return [];
    return resources.filter(resource =>
      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [resources, searchTerm]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="p-0 w-[95vw] max-w-[800px] h-[90vh] max-h-[800px] flex flex-col">
        <DialogHeader className="px-4 sm:px-6 py-3 sm:py-4 border-b">
          <DialogTitle className="flex items-center justify-between text-base sm:text-lg">
            <span>Select a Resource</span>
          </DialogTitle>
          <div className="relative mt-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 bg-muted/5 border-input"
            />
          </div>
        </DialogHeader>

        <div className="flex-1 px-4 sm:px-6 py-4 min-h-0 flex flex-col">
          <ScrollArea className="flex-1 rounded-xl border bg-muted/5">
            {isLoadingResources ? (
              <div className="flex flex-col items-center justify-center h-full space-y-4 p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">Loading resources...</p>
              </div>
            ) : filteredResources().length > 0 ? (
              <RadioGroup
                value={selectedResource || undefined}
                onValueChange={setSelectedResource}
                className="p-2 space-y-2"
              >
                {filteredResources().map((resource) => {
                  const isExpanded = expandedResources.has(resource.id);

                  return (
                    <div
                      key={resource.id}
                      className={cn(
                        "group relative flex flex-col p-2 sm:p-4 rounded-lg transition-all",
                        selectedResource === resource.id
                          ? "bg-primary/5 border-primary shadow-sm"
                          : "hover:bg-muted/50 border-transparent",
                        "border"
                      )}
                      onClick={() => setSelectedResource(resource.id)}
                    >
                      <div className="flex items-start space-x-2 sm:space-x-3">
                        <div className={cn(
                          "mt-1 h-4 w-4 rounded-full border transition-colors flex-shrink-0",
                          selectedResource === resource.id
                            ? "border-primary bg-primary"
                            : "border-muted-foreground"
                        )}>
                          {selectedResource === resource.id && (
                            <div className="h-full w-full rounded-full bg-background flex items-center justify-center">
                              <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                            </div>
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-1 sm:gap-2">
                            <div className="flex flex-col">
                              <span className="font-medium leading-none mb-1 text-sm sm:text-base">
                                {resource.name}
                              </span>
                              {resource.description && (
                                <span className="text-xs sm:text-sm text-muted-foreground line-clamp-2">
                                  {resource.description}
                                </span>
                              )}
                            </div>

                            <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                              <Badge
                                variant="outline"
                                className={cn(
                                  "px-1 sm:px-2 py-0.5 text-xs",
                                  getStatusColor(resource.status).background,
                                  getStatusColor(resource.status).text
                                )}
                              >
                                {resource.status?.toUpperCase() ?? 'UNKNOWN'}
                              </Badge>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleResourceExpansion(resource.id);
                                }}
                              >
                                {isExpanded ? (
                                  <ChevronUp className="h-4 w-4" />
                                ) : (
                                  <ChevronDown className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>

                          {isExpanded && (
                            <div className="mt-3 pt-3 border-t space-y-1 sm:space-y-2 text-xs sm:text-sm">
                              <div className="grid grid-cols-2 gap-1 sm:gap-2">
                                <span className="text-muted-foreground">Resource ID</span>
                                <span className="font-mono text-xs truncate">
                                  {resource.id}
                                </span>
                              </div>
                              <div className="grid grid-cols-2 gap-1 sm:gap-2">
                                <span className="text-muted-foreground">Type</span>
                                <span className="capitalize">{resource.type}</span>
                              </div>
                              <div className="grid grid-cols-2 gap-1 sm:gap-2">
                                <span className="text-muted-foreground">Region</span>
                                <span>{resource.region}</span>
                              </div>
                              <div className="grid grid-cols-2 gap-1 sm:gap-2">
                                <span className="text-muted-foreground">ARN</span>
                                <span className="font-mono text-xs truncate">
                                  {resource.arn}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </RadioGroup>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-4 sm:p-8 text-center">
                <p className="text-sm font-medium">No resources found</p>
                <p className="text-xs sm:text-sm text-muted-foreground mt-1">
                  {searchTerm ? "Try adjusting your search" : "No resources available"}
                </p>
              </div>
            )}
          </ScrollArea>
        </div>

        <DialogFooter className="p-4 border-t">
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => {
                const searchParams = new URLSearchParams();
                searchParams.set('autonomous', agentType === 'autonomous_agent' ? 'true' : 'false');
                router.push(`/agents/${agentId}?${searchParams.toString()}`);
                onClose();
              }}
              className="px-4 py-2 transition-all duration-200 hover:bg-muted/50 hover:border-muted-foreground/30"
            >
              <span className="flex items-center gap-2">
                Start without resource
              </span>
            </Button>
            <Button
              onClick={handleStartChat}
              disabled={!selectedResource}
              className={cn(
                "px-4 py-2 shadow-sm hover:shadow-md transition-all duration-200",
                !selectedResource && "opacity-70"
              )}
            >
              <span className="flex items-center gap-2">
                {selectedResource ? (
                  <>
                    Start with resource
                  </>
                ) : (
                  "Select a resource"
                )}
              </span>
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
