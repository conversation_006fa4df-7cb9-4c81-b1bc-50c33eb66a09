import { DollarSign, Shield, BarChart, Cloud } from 'lucide-react';
import { cn } from '@/lib/utils';
import { type ResourceType } from '@/lib/constants/resource-types';

interface QuestionCardProps {
  icon: React.ReactNode;
  title: string;
  question: string;
  onClick: (question: string) => void;
  color: string;
  index: number;
}

const QuestionCard = ({ icon, title, question, onClick, color, index }: QuestionCardProps) => (
  <button
    onClick={() => onClick(question)}
    className={cn(
      "group relative w-full p-5 rounded-3xl border border-border/40 transition-all duration-300",
      "hover:border-primary/60 hover:shadow-lg hover:scale-[1.03]",
      "bg-card/50 backdrop-blur-sm overflow-hidden"
    )}
    style={{ animationDelay: `${index * 100}ms` }}
  >
    {/* Gradient background that appears on hover */}
    <div className={cn(
      "absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300",
      `bg-gradient-to-br ${color}`
    )} />

    <div className="flex items-start gap-4 relative z-10">
      <div className={cn(
        "p-2.5 rounded-3xl bg-background border border-border/30 shadow-sm",
        "group-hover:bg-primary/10 group-hover:border-primary/30 group-hover:shadow",
        "transition-all duration-300"
      )}>
        {icon}
      </div>
      <div className="flex flex-col items-start text-left">
        <h3 className={cn(
          "font-medium text-sm text-muted-foreground",
          "group-hover:text-primary transition-colors duration-200"
        )}>
          {title}
        </h3>
        <p className="text-sm font-medium mt-1.5 leading-relaxed">
          {question}
        </p>
      </div>
    </div>

    {/* Animated arrow that appears on hover */}
    <div className="absolute bottom-4 right-4 opacity-0 transform translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300 text-primary">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8 3.33334L12.6667 8.00001L8 12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </div>
  </button>
);

interface AgentExampleQuestionsProps {
  onQuestionClick: (question: string) => void;
  resourceType?: ResourceType;
}

// Resource-specific example questions
const RESOURCE_QUESTIONS: Record<ResourceType, Array<{
  icon: React.ReactNode;
  title: string;
  question: string;
  color: string;
}>> = {
  // Compute Services
  EC2: [
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cost Optimization",
      question: "Is this EC2 instance underutilized and can it be optimized for cost savings?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Analysis",
      question: "What is the CPU and memory utilization pattern for this EC2 instance this month?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Right-sizing",
      question: "What instance type would be more cost-effective for this EC2 instance's workload?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Security Compliance",
      question: "Is this EC2 instance following security best practices and compliance requirements?",
      color: "from-orange-400 to-red-600"
    }
  ],
  LAMBDA: [
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cost Analysis",
      question: "How much is this Lambda function costing and can it be optimized?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Optimization",
      question: "What is the execution time and error rate pattern for this Lambda function?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Error Rate Analysis",
      question: "What are the failure patterns and causes for this Lambda function?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cold Start Optimization",
      question: "How can I optimize cold starts for this specific Lambda function?",
      color: "from-orange-400 to-red-600"
    }
  ],
  ECS: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Container Optimization",
      question: "How can I optimize this ECS service's container resource allocation?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cost Efficiency",
      question: "What are the cost implications of this ECS service's current configuration?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Service Scaling",
      question: "Is this ECS service properly configured for auto-scaling based on demand?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Monitoring",
      question: "Is this ECS service experiencing performance issues or resource constraints?",
      color: "from-orange-400 to-red-600"
    }
  ],
  EKS: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cluster Optimization",
      question: "How can I optimize this EKS cluster for better cost efficiency?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Node Utilization",
      question: "Are the nodes in this EKS cluster underutilized and can they be downsized?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Security Compliance",
      question: "Is this EKS cluster following Kubernetes security best practices?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Monitoring",
      question: "How is this EKS cluster performing in terms of pod scheduling and resource allocation?",
      color: "from-orange-400 to-red-600"
    }
  ],
  // Database Services
  RDS: [
    {
      icon: <BarChart className="h-4.5 w-4.5 text-blue-500" />,
      title: "Performance Optimization",
      question: "Is this RDS instance experiencing performance bottlenecks or slow queries?",
      color: "from-blue-400 to-indigo-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-blue-500" />,
      title: "Cost Optimization",
      question: "Is this RDS instance underutilized and can it be downsized?",
      color: "from-blue-400 to-indigo-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-blue-500" />,
      title: "Security Compliance",
      question: "Is this RDS instance following security best practices and compliance requirements?",
      color: "from-blue-400 to-indigo-600"
    },
    {
      icon: <Cloud className="h-4.5 w-4.5 text-blue-500" />,
      title: "Backup & Maintenance",
      question: "Are this RDS instance's backup and maintenance windows optimally configured?",
      color: "from-blue-400 to-indigo-600"
    }
  ],
  DYNAMODB: [
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-blue-500" />,
      title: "Cost Optimization",
      question: "How can I optimize this DynamoDB table's read/write capacity for cost savings?",
      color: "from-blue-400 to-indigo-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-blue-500" />,
      title: "Performance Optimization",
      question: "Does this DynamoDB table have throttling issues or hot partitions?",
      color: "from-blue-400 to-indigo-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-blue-500" />,
      title: "Security Compliance",
      question: "Is this DynamoDB table properly encrypted and access-controlled?",
      color: "from-blue-400 to-indigo-600"
    },
    {
      icon: <Cloud className="h-4.5 w-4.5 text-blue-500" />,
      title: "Data Modeling",
      question: "Can I improve this DynamoDB table's design for better performance and cost efficiency?",
      color: "from-blue-400 to-indigo-600"
    }
  ],
  // Storage Services
  S3: [
    {
      icon: <Shield className="h-4.5 w-4.5 text-green-500" />,
      title: "Security Compliance",
      question: "Does this S3 bucket have any potential security risks or misconfigurations?",
      color: "from-green-400 to-emerald-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-green-500" />,
      title: "Cost Optimization",
      question: "How can I optimize this S3 bucket's storage costs with lifecycle policies?",
      color: "from-green-400 to-emerald-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-green-500" />,
      title: "Data Governance",
      question: "What is the usage pattern and data lifecycle for this S3 bucket?",
      color: "from-green-400 to-emerald-600"
    },
    {
      icon: <Cloud className="h-4.5 w-4.5 text-green-500" />,
      title: "Performance Optimization",
      question: "How can I optimize this S3 bucket's access patterns and transfer performance?",
      color: "from-green-400 to-emerald-600"
    }
  ],
  EBS: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-green-500" />,
      title: "Volume Optimization",
      question: "Is this EBS volume underutilized and can it be resized or optimized?",
      color: "from-green-400 to-emerald-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-green-500" />,
      title: "Cost Optimization",
      question: "Can I optimize this EBS volume type (gp2 to gp3) for better cost efficiency?",
      color: "from-green-400 to-emerald-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-green-500" />,
      title: "Performance Analysis",
      question: "Is this EBS volume experiencing IOPS or throughput bottlenecks?",
      color: "from-green-400 to-emerald-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-green-500" />,
      title: "Snapshot Management",
      question: "Are this EBS volume's snapshot policies optimized for cost and retention?",
      color: "from-green-400 to-emerald-600"
    }
  ],
  // Networking Services
  VPC: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-teal-500" />,
      title: "Network Optimization",
      question: "How can I optimize this VPC setup for better cost efficiency and performance?",
      color: "from-teal-400 to-cyan-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-teal-500" />,
      title: "Security Group Analysis",
      question: "Are this VPC's security groups properly configured with least privilege access?",
      color: "from-teal-400 to-cyan-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-teal-500" />,
      title: "Cost Analysis",
      question: "What are the data transfer costs associated with this VPC configuration?",
      color: "from-teal-400 to-cyan-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-teal-500" />,
      title: "Connectivity Issues",
      question: "Are there any connectivity issues or routing problems in this VPC?",
      color: "from-teal-400 to-cyan-600"
    }
  ],
  ELB: [
    {
      icon: <BarChart className="h-4.5 w-4.5 text-teal-500" />,
      title: "Performance Monitoring",
      question: "Is this load balancer performing optimally with proper response times and throughput?",
      color: "from-teal-400 to-cyan-600"
    },
    {
      icon: <Cloud className="h-4.5 w-4.5 text-teal-500" />,
      title: "Health Check Optimization",
      question: "Are this load balancer's health checks properly configured and not causing overhead?",
      color: "from-teal-400 to-cyan-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-teal-500" />,
      title: "Cost Analysis",
      question: "What are the costs associated with this load balancer's usage and data processing?",
      color: "from-teal-400 to-cyan-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-teal-500" />,
      title: "Security Configuration",
      question: "Is this load balancer properly configured with SSL/TLS and security policies?",
      color: "from-teal-400 to-cyan-600"
    }
  ],
  // Default fallback for other resource types
  BATCH: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Job Optimization",
      question: "How can I optimize this AWS Batch job queue and compute environment for cost efficiency?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Analysis",
      question: "How long is this Batch job taking to complete and what resources is it consuming?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cost Analysis",
      question: "What are the cost implications of this Batch job's configuration and instance types?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Resource Utilization",
      question: "Is this Batch compute environment properly sized and utilizing resources efficiently?",
      color: "from-orange-400 to-red-600"
    }
  ],
  EC2_AUTO_SCALING: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Scaling Optimization",
      question: "Is this Auto Scaling group properly configured to handle traffic patterns efficiently?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Monitoring",
      question: "How are this Auto Scaling group's policies performing in terms of response time and accuracy?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cost Efficiency",
      question: "Can I optimize this Auto Scaling group's thresholds to reduce unnecessary scaling events and costs?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Health Check Analysis",
      question: "Are this Auto Scaling group's health checks properly configured to avoid unnecessary instance replacements?",
      color: "from-orange-400 to-red-600"
    }
  ],
  ELASTIC_BEANSTALK: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Environment Optimization",
      question: "How can I optimize this Elastic Beanstalk environment for better performance and cost efficiency?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Resource Utilization",
      question: "Is this Beanstalk application properly utilizing the allocated resources?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Instance Type Analysis",
      question: "Can I reduce costs by optimizing instance types for this Beanstalk environment?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Health Monitoring",
      question: "How is the health and performance of this Elastic Beanstalk application trending?",
      color: "from-orange-400 to-red-600"
    }
  ],
  APP_RUNNER: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-orange-500" />,
      title: "Service Optimization",
      question: "How can I optimize this App Runner service for better performance and cost efficiency?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-orange-500" />,
      title: "Auto Scaling Analysis",
      question: "Is this App Runner service's auto-scaling configuration properly handling traffic patterns?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-orange-500" />,
      title: "Cost Monitoring",
      question: "What are the cost trends for this App Runner service and how can I optimize them?",
      color: "from-orange-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-orange-500" />,
      title: "Performance Metrics",
      question: "How is this App Runner service performing in terms of response time and availability?",
      color: "from-orange-400 to-red-600"
    }
  ],
  ELASTICACHE: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-purple-500" />,
      title: "Cache Optimization",
      question: "How can I optimize this ElastiCache cluster for better performance and cost efficiency?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-purple-500" />,
      title: "Hit Rate Analysis",
      question: "What is the cache hit rate for this ElastiCache cluster and how can I improve it?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-purple-500" />,
      title: "Instance Sizing",
      question: "Is this ElastiCache instance properly sized for the workload requirements?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-purple-500" />,
      title: "Memory Utilization",
      question: "How is memory utilization for this ElastiCache cluster and are there optimization opportunities?",
      color: "from-purple-400 to-blue-600"
    }
  ],
  NEPTUNE: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-purple-500" />,
      title: "Graph Database Optimization",
      question: "How can I optimize this Neptune graph database for better query performance and cost efficiency?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-purple-500" />,
      title: "Query Performance",
      question: "What queries are consuming the most resources on this Neptune instance and how can I optimize them?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-purple-500" />,
      title: "Instance Sizing",
      question: "Is this Neptune instance properly sized for the graph workload requirements?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-purple-500" />,
      title: "Storage Analysis",
      question: "How is this Neptune instance's storage growing and are there opportunities for optimization?",
      color: "from-purple-400 to-blue-600"
    }
  ],
  DOCUMENTDB: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-purple-500" />,
      title: "Document Database Optimization",
      question: "How can I optimize this DocumentDB cluster for better performance and cost efficiency?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-purple-500" />,
      title: "Query Performance",
      question: "What DocumentDB queries are taking the longest and consuming the most resources on this cluster?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-purple-500" />,
      title: "Instance Sizing",
      question: "Is this DocumentDB instance properly sized for the MongoDB-compatible workloads?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-purple-500" />,
      title: "Storage Growth",
      question: "How is this DocumentDB cluster's storage growing and what are the cost implications?",
      color: "from-purple-400 to-blue-600"
    }
  ],
  OPENSEARCH: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-purple-500" />,
      title: "Search Optimization",
      question: "How can I optimize this OpenSearch cluster for better search performance and cost efficiency?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-purple-500" />,
      title: "Index Performance",
      question: "What OpenSearch indices are consuming the most resources on this cluster and how can I optimize them?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-purple-500" />,
      title: "Instance Sizing",
      question: "Is this OpenSearch instance properly sized for the search and analytics workloads?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-purple-500" />,
      title: "Storage Management",
      question: "How can I optimize this OpenSearch cluster's storage usage and implement effective data lifecycle policies?",
      color: "from-purple-400 to-blue-600"
    }
  ],
  REDSHIFT: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-purple-500" />,
      title: "Data Warehouse Optimization",
      question: "How can I optimize this Redshift cluster for better query performance and cost efficiency?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-purple-500" />,
      title: "Query Performance",
      question: "What Redshift queries are taking the longest and consuming the most resources on this cluster?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-purple-500" />,
      title: "Reserved Instance Analysis",
      question: "Should I consider Reserved Instances for this Redshift cluster to reduce costs?",
      color: "from-purple-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-purple-500" />,
      title: "Storage Optimization",
      question: "How can I optimize this Redshift cluster's storage usage and implement effective data compression?",
      color: "from-purple-400 to-blue-600"
    }
  ],
  EFS: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-green-500" />,
      title: "File System Optimization",
      question: "How can I optimize this EFS file system for better performance and cost efficiency?",
      color: "from-green-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-green-500" />,
      title: "Performance Analysis",
      question: "What are the throughput and IOPS patterns for this EFS file system?",
      color: "from-green-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-green-500" />,
      title: "Storage Class Optimization",
      question: "Can I reduce costs by moving infrequently accessed data in this EFS to IA storage class?",
      color: "from-green-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-green-500" />,
      title: "Access Patterns",
      question: "How are applications accessing this EFS file system and are there optimization opportunities?",
      color: "from-green-400 to-blue-600"
    }
  ],
  BACKUP: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-green-500" />,
      title: "Backup Strategy Optimization",
      question: "How can I optimize this AWS Backup plan for better cost efficiency and compliance?",
      color: "from-green-400 to-blue-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-green-500" />,
      title: "Backup Performance",
      question: "How are backup jobs for this resource performing in terms of completion time and success rates?",
      color: "from-green-400 to-blue-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-green-500" />,
      title: "Storage Cost Analysis",
      question: "What are the costs associated with this backup storage and how can I optimize them?",
      color: "from-green-400 to-blue-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-green-500" />,
      title: "Retention Policy Review",
      question: "Are this backup plan's retention policies aligned with business requirements and cost optimization?",
      color: "from-green-400 to-blue-600"
    }
  ],
  CLOUDFORMATION: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-pink-500" />,
      title: "Stack Optimization",
      question: "How can I optimize this CloudFormation stack for better resource management and cost efficiency?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-pink-500" />,
      title: "Deployment Analysis",
      question: "Why is this CloudFormation stack taking a long time to deploy and how can I improve it?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-pink-500" />,
      title: "Resource Cost Tracking",
      question: "What are the cost implications of resources created by this CloudFormation stack?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-pink-500" />,
      title: "Stack Drift Detection",
      question: "Are there any configuration drifts in this CloudFormation stack that need attention?",
      color: "from-pink-400 to-red-600"
    }
  ],
  CLOUDWATCH: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-pink-500" />,
      title: "Monitoring Optimization",
      question: "How can I optimize this CloudWatch monitoring setup for better cost efficiency and coverage?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-pink-500" />,
      title: "Metrics Analysis",
      question: "What CloudWatch metrics are most valuable for this resource's cost optimization efforts?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-pink-500" />,
      title: "Log Cost Management",
      question: "How can I reduce CloudWatch Logs costs for this resource while maintaining necessary visibility?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-pink-500" />,
      title: "Alert Optimization",
      question: "Are this resource's CloudWatch alarms properly configured to detect cost anomalies and performance issues?",
      color: "from-pink-400 to-red-600"
    }
  ],
  SQS: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-pink-500" />,
      title: "Queue Optimization",
      question: "How can I optimize this SQS queue for better performance and cost efficiency?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-pink-500" />,
      title: "Message Processing",
      question: "What are the message processing patterns and throughput for this SQS queue?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-pink-500" />,
      title: "Cost Analysis",
      question: "How can I reduce costs for this SQS queue through better message batching and configuration?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-pink-500" />,
      title: "Dead Letter Queues",
      question: "Is this SQS queue's dead letter queue properly configured and monitored for failed messages?",
      color: "from-pink-400 to-red-600"
    }
  ],
  SNS: [
    {
      icon: <Cloud className="h-4.5 w-4.5 text-pink-500" />,
      title: "Notification Optimization",
      question: "How can I optimize this SNS topic and its subscriptions for better cost efficiency?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <BarChart className="h-4.5 w-4.5 text-pink-500" />,
      title: "Message Delivery",
      question: "What are the delivery success rates and patterns for this SNS topic?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <DollarSign className="h-4.5 w-4.5 text-pink-500" />,
      title: "Cost Analysis",
      question: "How can I reduce costs for this SNS topic through better message filtering and subscription management?",
      color: "from-pink-400 to-red-600"
    },
    {
      icon: <Shield className="h-4.5 w-4.5 text-pink-500" />,
      title: "Subscription Management",
      question: "Are this SNS topic's subscriptions properly configured and are there any unused or redundant ones?",
      color: "from-pink-400 to-red-600"
    }
  ]
};

// Default questions when no specific resource type is provided
const DEFAULT_QUESTIONS = [
  {
    icon: <DollarSign className="h-4.5 w-4.5 text-emerald-500" />,
    title: "Cost Optimization",
    question: "Can you identify any underutilized resources in our environment?",
    color: "from-emerald-400 to-teal-600"
  },
  {
    icon: <Shield className="h-4.5 w-4.5 text-blue-500" />,
    title: "Security Compliance",
    question: "Can you review our AWS resources and identify any potential security risks?",
    color: "from-blue-400 to-indigo-600"
  },
  {
    icon: <BarChart className="h-4.5 w-4.5 text-violet-500" />,
    title: "Performance Monitoring",
    question: "How are our AWS resources performing in terms of utilization and efficiency?",
    color: "from-violet-400 to-purple-600"
  },
  {
    icon: <Cloud className="h-4.5 w-4.5 text-amber-500" />,
    title: "Resource Provisioning",
    question: "How can I set up new AWS resources with maximum cost savings for a development environment?",
    color: "from-amber-400 to-orange-600"
  }
];

export function AgentExampleQuestions({ onQuestionClick, resourceType }: AgentExampleQuestionsProps) {
  // Get questions based on resource type or use defaults
  const questions = resourceType && RESOURCE_QUESTIONS[resourceType]?.length > 0
    ? RESOURCE_QUESTIONS[resourceType]
    : DEFAULT_QUESTIONS;

  const title = resourceType
    ? `${resourceType} Optimization Questions`
    : "Explore Cloud Cost Optimization";

  return (
    <div className="w-full border-none py-6">
      <div className="flex flex-col items-center px-4 md:px-8 lg:px-12">
        <div className="w-full max-w-4xl mx-auto">
          <h2 className="text-lg font-medium mb-3 text-center text-foreground/90">{title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 animate-fade-in">
            {questions.map((q, idx) => (
              <QuestionCard
                key={idx}
                index={idx}
                icon={q.icon}
                title={q.title}
                question={q.question}
                color={q.color}
                onClick={onQuestionClick}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
