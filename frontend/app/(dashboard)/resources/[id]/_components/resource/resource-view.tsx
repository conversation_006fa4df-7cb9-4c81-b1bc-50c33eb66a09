'use client';

import { memo, useEffect, useMemo } from 'react';
import React from 'react';
import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { ResourceAutonomousAgent } from './resource-autonomous-agent';
import { useResourceView } from '../hooks/use-resource-view';
import { useResourceConversations } from '../hooks';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
interface ResourceViewProps {
  id: string;
  searchParams?: { [key: string]: string | string[] | undefined };
}

// Memoized ResourceAutonomousAgent to prevent unnecessary re-renders
const MemoizedResourceAutonomousAgent = memo(ResourceAutonomousAgent);

export default function ResourceView({ id, searchParams }: ResourceViewProps) {
  // Extract conversationId from searchParams - support both 'conversationId' and 'conversation' parameters
  const conversationId = (searchParams?.conversationId || searchParams?.conversation) as string | undefined;
  const router = useRouter();

  // Destructure state from custom resource view hook - now passing conversationId
  const {
    resource,
    streamingRecommendations,
    setStreamingRecommendations,
    isLoading: isLoadingResource,
    lastLoadedConversation
  } = useResourceView(id, conversationId);

  const {
    conversations: conversationsResponse,
    isLoading: isLoadingConversations,
    createConversation,
    defaultAgent,
  } = useResourceConversations(id);

  const conversations = useMemo(() => {
    if (!conversationsResponse) {
      return [];
    }
    if (Array.isArray(conversationsResponse)) {
      return conversationsResponse;
    }
    return (conversationsResponse as any).data || [];
  }, [conversationsResponse]);

  // Redirect to the last viewed or most recent conversation
  useEffect(() => {
    const isLoading = isLoadingResource || isLoadingConversations;
    if (conversationId || isLoading) {
      return;
    }

    if (lastLoadedConversation) {
      router.replace(`/resources/${id}?conversation=${lastLoadedConversation}`);
    } else if (conversations.length > 0) {
      const sorted = [...conversations].sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      router.replace(`/resources/${id}?conversation=${sorted[0].id}`);
    }
  }, [
    conversationId,
    lastLoadedConversation,
    id,
    router,
    isLoadingResource,
    isLoadingConversations,
    conversations,
  ]);

  // Determine which conversation ID to pass to ResourceAutonomousAgent
  const effectiveConversationId = conversationId || (lastLoadedConversation || undefined);
  const isLoading = isLoadingResource || isLoadingConversations;

  return (
    <PageContainer scrollable={false} className="px-0">
      <div className={cn("flex flex-col h-[calc(100vh-4rem)] bg-background")}>
        <div className="w-full h-full overflow-hidden">
          {isLoading ? (
            <div className="h-full flex items-center justify-center">
              <Skeleton className="h-12 w-12 rounded-full" />
            </div>
          ) : (
            <MemoizedResourceAutonomousAgent
              resourceId={id}
              initialConversationId={effectiveConversationId}
              setStreamingRecommendations={setStreamingRecommendations}
              resource={resource}
              conversations={conversations}
              isLoadingConversations={isLoadingConversations}
              createConversation={createConversation}
              defaultAgent={defaultAgent || null}
            />
          )}
        </div>
      </div>
    </PageContainer>
  );
}
