'use client';

import { useQuery } from '@tanstack/react-query';
import { ResourcesService } from '@/client';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { CacheKey } from '@/components/utils/cache-key';
import {
  Server,
  CircleOff,
  CheckCircle2,
  AlertTriangle,
  Clock,
  CloudCog,
  BarChart4,
  DollarSign
} from 'lucide-react';
import { useMemo } from 'react';

export function ResourceStatusSummary() {
  const { data, isLoading } = useQuery({
    queryKey: [CacheKey.Resources, 'summary'],
    queryFn: () => ResourcesService.readResources({ limit: 1000 }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const statusCounts = useMemo(() => {
    if (!data?.data) return {};
    return data.data.reduce((acc: Record<string, number>, resource) => {
      const status = resource.status || 'unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
  }, [data]);

  const totalSavings = useMemo(() => {
    if (!data?.data) return 0;
    return data.data.reduce((sum, resource) => sum + (resource.total_potential_saving || 0), 0);
  }, [data]);

  const topResourceTypes = useMemo(() => {
    if (!data?.data) return [];
    const types = data.data.reduce((acc: Record<string, number>, resource) => {
      const type = resource.type || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
    return Object.entries(types).sort((a, b) => b[1] - a[1]).slice(0, 3); // Show top 3
  }, [data]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="flex-1 min-w-[220px] h-[120px]">
            <CardContent className="p-4 h-full flex flex-col">
              <Skeleton className="h-5 w-24 mb-2 flex-shrink-0" />
              <div className="flex-1 flex flex-col justify-center space-y-1">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statusOrder = ['running', 'found', 'stopped', 'warning', 'unknown'];
  const sortedStatusCounts = Object.entries(statusCounts)
    .filter(([_, count]) => count > 0) // Only show statuses with count > 0
    .sort(([a], [b]) => statusOrder.indexOf(a) - statusOrder.indexOf(b));

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Status Overview Card */}
      <Card className="flex-1 min-w-[220px] h-[120px]">
        <CardContent className="p-4 h-full flex flex-col">
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <p className="text-sm font-medium text-muted-foreground">Status Overview</p>
            <Server className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="flex-1 grid grid-cols-2 gap-x-2 gap-y-1.5 content-start">
            {sortedStatusCounts.length > 0 ? sortedStatusCounts.map(([status, count]) => (
              <StatusItem
                key={status}
                status={status}
                count={count}
              />
            )) : <p className="text-sm text-muted-foreground col-span-2">No status data</p>}
          </div>
        </CardContent>
      </Card>

      {/* Total Resources Card */}
      <Card className="flex-1 min-w-[220px] h-[120px]">
        <CardContent className="p-4 h-full flex flex-col">
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <p className="text-sm font-medium text-muted-foreground">Total Resources</p>
            <CloudCog className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <h3 className="text-2xl font-bold">{data?.count || 0}</h3>
            <p className="text-xs text-muted-foreground mt-1">Across the workspace</p>
          </div>
        </CardContent>
      </Card>

      {/* Top Resource Types Card */}
      <Card className="flex-1 min-w-[220px] h-[120px]">
        <CardContent className="p-4 h-full flex flex-col">
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <p className="text-sm font-medium text-muted-foreground">Top Resource Types</p>
            <BarChart4 className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="flex-1 grid grid-cols-2 gap-x-2 gap-y-1 content-start">
            {topResourceTypes.length > 0 ? (
              topResourceTypes.map(([type, count]) => (
                <div key={type} className="flex items-center justify-between text-sm min-w-0">
                  <span className="font-medium text-xs truncate">{type}</span>
                  <span className="font-semibold text-muted-foreground text-xs flex-shrink-0">
                    {count}
                  </span>
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground col-span-2">No type data</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Potential Monthly Savings Card */}
      <Card className="flex-1 min-w-[220px] h-[120px]">
        <CardContent className="p-4 h-full flex flex-col">
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <p className="text-sm font-medium text-muted-foreground">Potential Monthly Savings</p>
            <DollarSign className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <h3 className="text-2xl font-bold">${totalSavings.toFixed(2)}</h3>
            <p className="text-xs text-muted-foreground mt-1">
              From {data?.data.filter(r => r.total_potential_saving && r.total_potential_saving > 0).length || 0} resources
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper component for status items
function StatusItem({ status, count }: { status: string, count: number }) {
  let icon;
  switch (status.toLowerCase()) {
    case 'running': icon = <CheckCircle2 className="h-4 w-4 text-green-500" />; break;
    case 'stopped': icon = <CircleOff className="h-4 w-4 text-gray-500" />; break;
    case 'warning': icon = <AlertTriangle className="h-4 w-4 text-yellow-500" />; break;
    case 'found': icon = <Clock className="h-4 w-4 text-blue-500" />; break;
    default: icon = <Clock className="h-4 w-4 text-slate-400" />; break;
  }
  return (
    <div className="flex items-center justify-between text-sm py-0.5 min-w-0">
      <div className="flex items-center gap-1 min-w-0">
        {icon}
        <span className="capitalize text-xs truncate">{status}</span>
      </div>
      <span className="font-semibold text-muted-foreground text-xs flex-shrink-0">{count}</span>
    </div>
  );
}
