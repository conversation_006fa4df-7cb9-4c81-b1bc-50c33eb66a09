'use client';

import { CheckCircle, XCircle, RefreshCcw, StopCircleIcon, Clock, AlertTriangle } from 'lucide-react';
import { TaskExecutionStatus } from '@/client/types.gen';
import { cn } from '@/lib/utils';

interface StatusLegendProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
}

// Status configuration using design system colors
const getStatusConfig = (status: TaskExecutionStatus) => {
  switch (status) {
    case 'succeeded':
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-500',
        label: 'Success'
      };
    case 'failed':
      return {
        icon: XCircle,
        color: 'text-red-700 dark:text-red-400',
        bgColor: 'bg-red-500 hover:bg-red-600 shadow-red-500/20',
        label: 'Failed'
      };
    case 'running':
      return {
        icon: RefreshCcw,
        color: 'text-info',
        bgColor: 'bg-info',
        label: 'Running'
      };
    case 'cancelled':
      return {
        icon: StopCircleIcon,
        color: 'text-warning',
        bgColor: 'bg-warning',
        label: 'Cancelled'
      };
    case 'required_approval':
      return {
        icon: AlertTriangle,
        color: 'text-amber-600',
        bgColor: 'bg-amber-500',
        label: 'Requires Approval'
      };
    default:
      return {
        icon: Clock,
        color: 'text-muted-foreground',
        bgColor: 'bg-muted',
        label: 'Pending'
      };
  }
};

export default function TimelineStatusLegend({
  className,
  size = 'md',
  orientation = 'horizontal'
}: StatusLegendProps) {
  const statusTypes: TaskExecutionStatus[] = ['succeeded', 'failed', 'running', 'cancelled', 'required_approval'];

  const sizeClasses = {
    sm: {
      dot: 'w-2 h-2',
      text: 'text-xs',
      gap: 'gap-1'
    },
    md: {
      dot: 'w-3 h-3',
      text: 'text-sm',
      gap: 'gap-1.5'
    },
    lg: {
      dot: 'w-4 h-4',
      text: 'text-base',
      gap: 'gap-2'
    }
  };

  const containerClasses = 'flex flex-wrap items-center gap-4';

  return (
    <div className={cn(containerClasses, className)}>
      {statusTypes.map((status) => {
        const config = getStatusConfig(status);
        return (
          <div
            key={status}
            className={cn(
              'flex items-center group hover:scale-105 transition-transform duration-200 cursor-default',
              sizeClasses[size].gap
            )}
          >
            <div className={cn(
              'rounded-full shadow-sm transition-all duration-200 group-hover:shadow-md group-hover:scale-110 border border-background/20',
              config.bgColor,
              sizeClasses[size].dot
            )} />
            <span className={cn(
              'font-medium text-muted-foreground transition-colors duration-200 group-hover:text-foreground/70',
              sizeClasses[size].text
            )}>
              {config.label}
            </span>
          </div>
        );
      })}
    </div>
  );
}
