'use client';

import { TrendingUp, TrendingDown, Minus, Target, Zap, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TaskHistory } from '@/client/types.gen';
import { cn } from '@/lib/utils';

interface TimelineSuccessRateProps {
  taskHistory?: TaskHistory[];
  className?: string;
  variant?: 'compact' | 'detailed' | 'minimal';
  showTrend?: boolean;
}

interface SuccessMetrics {
  successRate: number;
  totalExecutions: number;
  successCount: number;
  failedCount: number;
  runningCount: number;
  cancelledCount: number;
  trend: 'up' | 'down' | 'stable';
  healthStatus: 'excellent' | 'good' | 'warning' | 'poor';
}

// Calculate comprehensive success metrics
const calculateSuccessMetrics = (executions: TaskHistory[]): SuccessMetrics => {
  if (!executions.length) {
    return {
      successRate: 0,
      totalExecutions: 0,
      successCount: 0,
      failedCount: 0,
      runningCount: 0,
      cancelledCount: 0,
      trend: 'stable',
      healthStatus: 'poor'
    };
  }

  const successCount = executions.filter(e => e.status === 'succeeded').length;
  const failedCount = executions.filter(e => e.status === 'failed').length;
  const runningCount = executions.filter(e => e.status === 'running').length;
  const cancelledCount = executions.filter(e => e.status === 'cancelled').length;
  const successRate = Math.round((successCount / executions.length) * 100);

  // Calculate trend (comparing recent vs older executions)
  const halfPoint = Math.floor(executions.length / 2);
  const recentExecutions = executions.slice(0, halfPoint);
  const olderExecutions = executions.slice(halfPoint);

  let trend: 'up' | 'down' | 'stable' = 'stable';
  if (recentExecutions.length && olderExecutions.length) {
    const recentSuccessRate = (recentExecutions.filter(e => e.status === 'succeeded').length / recentExecutions.length) * 100;
    const olderSuccessRate = (olderExecutions.filter(e => e.status === 'succeeded').length / olderExecutions.length) * 100;

    if (recentSuccessRate > olderSuccessRate + 10) trend = 'up';
    else if (recentSuccessRate < olderSuccessRate - 10) trend = 'down';
  }

  // Determine health status
  let healthStatus: 'excellent' | 'good' | 'warning' | 'poor';
  if (successRate >= 95) healthStatus = 'excellent';
  else if (successRate >= 80) healthStatus = 'good';
  else if (successRate >= 60) healthStatus = 'warning';
  else healthStatus = 'poor';

  return {
    successRate,
    totalExecutions: executions.length,
    successCount,
    failedCount,
    runningCount,
    cancelledCount,
    trend,
    healthStatus
  };
};

// Get health status configuration using design system colors
const getHealthConfig = (status: 'excellent' | 'good' | 'warning' | 'poor') => {
  switch (status) {
    case 'excellent':
      return {
        color: 'text-green-600',
        bgColor: 'bg-green-500',
        badgeVariant: 'default' as const,
        icon: CheckCircle,
        label: 'Excellent'
      };
    case 'good':
      return {
        color: 'text-green-600',
        bgColor: 'bg-green-500',
        badgeVariant: 'default' as const,
        icon: Target,
        label: 'Good'
      };
    case 'warning':
      return {
        color: 'text-warning',
        bgColor: 'bg-warning',
        badgeVariant: 'secondary' as const,
        icon: Zap,
        label: 'Warning'
      };
    case 'poor':
      return {
        color: 'text-destructive',
        bgColor: 'bg-destructive',
        badgeVariant: 'destructive' as const,
        icon: TrendingDown,
        label: 'Poor'
      };
  }
};

// Get trend configuration
const getTrendConfig = (trend: 'up' | 'down' | 'stable') => {
  switch (trend) {
    case 'up':
      return {
        icon: TrendingUp,
        color: 'text-emerald-600',
        label: 'Improving',
        description: 'Success rate is trending upward'
      };
    case 'down':
      return {
        icon: TrendingDown,
        color: 'text-red-600',
        label: 'Declining',
        description: 'Success rate is trending downward'
      };
    case 'stable':
      return {
        icon: Minus,
        color: 'text-gray-600',
        label: 'Stable',
        description: 'Success rate is consistent'
      };
  }
};

// Compact variant
const CompactSuccessRate = ({ metrics, showTrend }: { metrics: SuccessMetrics; showTrend: boolean }) => {
  const healthConfig = getHealthConfig(metrics.healthStatus);
  const trendConfig = getTrendConfig(metrics.trend);
  const TrendIcon = trendConfig.icon;

  return (
    <div className="flex items-center gap-3 group">
      <div className="flex items-center gap-2 hover:scale-105 transition-transform duration-200 cursor-default">
        <TrendIcon className={cn(
          "w-4 h-4 transition-all duration-200 group-hover:scale-110",
          healthConfig.color
        )} />
        <span className={cn(
          "text-sm font-semibold transition-all duration-200 group-hover:text-opacity-80",
          healthConfig.color
        )}>
          {metrics.successRate}%
        </span>
      </div>
      {showTrend && (
        <Badge
          variant="outline"
          className="h-5 text-xs hover:bg-muted/50 hover:border-foreground/20 transition-all duration-200"
        >
          {trendConfig.label}
        </Badge>
      )}
    </div>
  );
};

// Detailed variant
const DetailedSuccessRate = ({ metrics, showTrend }: { metrics: SuccessMetrics; showTrend: boolean }) => {
  const healthConfig = getHealthConfig(metrics.healthStatus);
  const trendConfig = getTrendConfig(metrics.trend);
  const HealthIcon = healthConfig.icon;
  const TrendIcon = trendConfig.icon;

  return (
    <div className="space-y-3">
      {/* Main Success Rate Display */}
      <div className="flex items-center justify-between group">
        <div className="flex items-center gap-3 hover:scale-105 transition-transform duration-200 cursor-default">
          <div className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center shadow-sm transition-all duration-200 group-hover:shadow-md",
            healthConfig.bgColor
          )}>
            <HealthIcon className="w-4 h-4 text-white transition-transform duration-200 group-hover:scale-110" />
          </div>
          <div>
            <div className={cn(
              "text-2xl font-bold transition-all duration-200 group-hover:text-opacity-80",
              healthConfig.color
            )}>
              {metrics.successRate}%
            </div>
            <div className="text-sm text-muted-foreground transition-colors duration-200 group-hover:text-foreground/60">
              Success Rate
            </div>
          </div>
        </div>

        {showTrend && (
          <div className="flex items-center gap-2 hover:scale-105 transition-transform duration-200 cursor-default">
            <TrendIcon className={cn(
              "w-4 h-4 transition-transform duration-200 hover:scale-110",
              trendConfig.color
            )} />
            <span className={cn(
              "text-sm font-medium transition-colors duration-200 hover:text-opacity-80",
              trendConfig.color
            )}>
              {trendConfig.label}
            </span>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress
          value={metrics.successRate}
          className="h-2 bg-muted"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{metrics.successCount} successful</span>
          <span>{metrics.totalExecutions} total executions</span>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-emerald-500" />
          <span>Success: {metrics.successCount}</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-red-500" />
          <span>Failed: {metrics.failedCount}</span>
        </div>
        {metrics.runningCount > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-blue-500" />
            <span>Running: {metrics.runningCount}</span>
          </div>
        )}
        {metrics.cancelledCount > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-orange-500" />
            <span>Cancelled: {metrics.cancelledCount}</span>
          </div>
        )}
      </div>

      {/* Health Status */}
      <Badge variant={healthConfig.badgeVariant} className="w-fit">
        {healthConfig.label}
      </Badge>
    </div>
  );
};

// Minimal variant
const MinimalSuccessRate = ({ metrics }: { metrics: SuccessMetrics }) => {
  const healthConfig = getHealthConfig(metrics.healthStatus);

  return (
    <div className="flex items-center gap-2">
      <span className={cn("text-lg font-bold", healthConfig.color)}>
        {metrics.successRate}%
      </span>
      <span className="text-sm text-muted-foreground">success</span>
    </div>
  );
};

export default function TimelineSuccessRate({
  taskHistory,
  className,
  variant = 'compact',
  showTrend = true
}: TimelineSuccessRateProps) {
  const metrics = calculateSuccessMetrics(taskHistory || []);

  if (metrics.totalExecutions === 0) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <span className="text-sm text-muted-foreground">No executions yet</span>
      </div>
    );
  }

  const componentMap = {
    compact: <CompactSuccessRate metrics={metrics} showTrend={showTrend} />,
    detailed: <DetailedSuccessRate metrics={metrics} showTrend={showTrend} />,
    minimal: <MinimalSuccessRate metrics={metrics} />
  };

  return (
    <div className={className}>
      {componentMap[variant]}
    </div>
  );
}
