'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { TasksService } from '@/client';
import FormCardSkeleton from '@/components/form-card-skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ItemForm from './item-form';
import { ActionType } from '@/types/common.enum';
import {
  Settings2Icon,
  History,
  ChevronRight,
  Target,
  FileText
} from 'lucide-react';
import EnhancedRecentActivity from './enhanced-recent-activity';
import ExecutionHistoryList from './execution-history-list';
import { Button } from '@/components/ui/button';
import ScheduleTimeline, { allTimezones } from './schedule-timeline';
import { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';

type ItemViewPageProps = {
  id: string;
};

// Description component with progressive disclosure for long content
const DescriptionContent = ({ description }: { description: string }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const maxLength = 400; // Character limit for initial display
  const shouldTruncate = description.length > maxLength;

  const displayText = shouldTruncate && !isExpanded
    ? description.slice(0, maxLength) + '...'
    : description;

  return (
    <div className="space-y-2">
      <div className="text-sm text-foreground/90 leading-relaxed break-words whitespace-pre-wrap">
        {displayText}
      </div>

      {shouldTruncate && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="h-auto p-0 text-xs text-primary hover:text-primary/80 font-medium"
        >
          {isExpanded ? (
            <div className="flex items-center gap-1">
              <span>Show less</span>
              <ChevronRight className="h-3 w-3 rotate-90" />
            </div>
          ) : (
            <div className="flex items-center gap-1">
              <span>Show more</span>
              <ChevronRight className="h-3 w-3" />
            </div>
          )}
        </Button>
      )}
    </div>
  );
};

export default function ItemViewPage({ id }: ItemViewPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedTimezone, setSelectedTimezone] = useState(allTimezones[0]);
  const [activeTab, setActiveTab] = useState('overview');
  const [highlightedExecutionId, setHighlightedExecutionId] = useState<string | undefined>();
  const [isNavigating, setIsNavigating] = useState(false);

  // Handle URL parameters for navigation
  const highlightExecutionId = searchParams.get('execution');
  const tabFromUrl = searchParams.get('tab');

  // Handle tab changes with instant feedback
  const handleTabChange = useCallback((newTab: string) => {
    // Instant visual feedback
    setActiveTab(newTab);

    // Clear execution highlighting when switching tabs manually
    setHighlightedExecutionId(undefined);

    // Clear URL execution parameter when switching tabs manually
    if (newTab !== 'history') {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('execution');
      newUrl.searchParams.set('tab', newTab);
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    } else {
      // When clicking directly on history tab, just update tab parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('execution'); // Remove execution highlighting
      newUrl.searchParams.set('tab', 'history');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    }
  }, [router]);

  // Simple scroll-to-execution without flash animation
  const scrollToExecution = useCallback((executionId: string) => {
    const element = document.getElementById(`execution-${executionId}`);
    if (element) {
      // Smooth scroll to center
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    }
  }, []);

  // Handle clicking on timeline execution points - Enhanced with proper navigation
  const handleExecutionClick = useCallback((executionId: string) => {
    // Set navigation state for visual feedback
    setIsNavigating(true);

    // INSTANT tab switch for immediate UI feedback
    setActiveTab('history');

    // Set highlighted execution for targeting
    setHighlightedExecutionId(executionId);

    // Update URL immediately for navigation state
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('execution', executionId);
    newUrl.searchParams.set('tab', 'history');
    router.replace(newUrl.pathname + newUrl.search, { scroll: false });

    // Delay scroll to allow tab content to render
    setTimeout(() => {
      scrollToExecution(executionId);
      setIsNavigating(false);
    }, 150);
  }, [router, scrollToExecution]);

  // Auto-switch to history tab if execution ID is provided
  useEffect(() => {
    if (highlightExecutionId && tabFromUrl === 'history') {
      setActiveTab('history');
      setHighlightedExecutionId(highlightExecutionId);
    }
  }, [highlightExecutionId, tabFromUrl]);

  // Handle scrolling when execution ID changes
  useEffect(() => {
    if (highlightedExecutionId && activeTab === 'history') {
      const timer = setTimeout(() => {
        scrollToExecution(highlightedExecutionId);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [highlightedExecutionId, activeTab, scrollToExecution]);

  const { data: task, isPending } = useQuery({
    queryFn: () => TasksService.getTask({ taskId: id, includeHistory: true, historyLimit: 10 }),
    queryKey: ['task', id]
  });

  if (isPending) {
    return <FormCardSkeleton />;
  }

  if (!task) {
    router.push('/404');
    return null;
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
      {/* Sticky Tab Navigation */}
      <div className="sticky top-0 z-40 bg-background/95 backdrop-blur-sm border-b pb-3 -mb-3">
        <TabsList className="inline-flex h-10 items-center justify-center rounded-full bg-muted p-1 text-muted-foreground">
          <TabsTrigger
            value="overview"
            className="rounded-full px-4 py-2 text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
          >
            <Target className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="rounded-full px-4 py-2 text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
          >
            <History className="w-4 h-4 mr-2" />
            Execution History
            {task.task_history && task.task_history.length > 0 && (
              <Badge variant="secondary" className="ml-2 h-5">
                {task.task_history.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="edit"
            className="rounded-full px-4 py-2 text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
          >
            <Settings2Icon className="w-4 h-4 mr-2" />
            Edit Task
          </TabsTrigger>
      </TabsList>
      </div>

      <TabsContent value="overview" className="space-y-6">
        {/* Simplified Header */}
        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                <Target className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-foreground">
                  {task.title || 'Untitled Task'}
                </h1>
                <p className="text-sm text-muted-foreground">Task Overview</p>
              </div>
            </div>
          </div>

          {/* Key Stats - Simplified Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">{task.task_history?.length || 0}</div>
              <div className="text-sm text-muted-foreground">Total Executions</div>
              <div className="text-xs text-muted-foreground/70 mt-1">
                {task.task_history?.length ? 'execution history available' : 'no executions yet'}
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-emerald-600">
                {task.task_history && task.task_history.length > 0
                  ? Math.round((task.task_history.filter(h => h.status === 'succeeded').length / task.task_history.length) * 100)
                  : 0
                }%
              </div>
              <div className="text-sm text-muted-foreground">Success Rate</div>
              <div className="text-xs text-muted-foreground/70 mt-1">
                {task.task_history && task.task_history.length > 0
                  ? `${task.task_history.filter(h => h.status === 'succeeded').length} of ${task.task_history.length} succeeded`
                  : 'no data available'
                }
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {(() => {
                  if (!task.task_history || task.task_history.length === 0) return 'N/A';

                  const completedRuns = task.task_history.filter(run =>
                    run.start_time && run.end_time &&
                    (run.status === 'succeeded' || run.status === 'failed' || run.status === 'cancelled')
                  );

                  if (completedRuns.length === 0) return 'N/A';

                  const totalDuration = completedRuns.reduce((sum, run) => {
                    const start = new Date(run.start_time!);
                    const end = new Date(run.end_time!);
                    return sum + (end.getTime() - start.getTime());
                  }, 0);

                  const averageDurationMs = totalDuration / completedRuns.length;

                  if (averageDurationMs < 1000) return `${Math.round(averageDurationMs)}ms`;
                  if (averageDurationMs < 60000) return `${Math.round(averageDurationMs / 1000)}s`;
                  if (averageDurationMs < 3600000) return `${Math.round(averageDurationMs / 60000)}m`;
                  return `${Math.round(averageDurationMs / 3600000)}h`;
                })()}
              </div>
              <div className="text-sm text-muted-foreground">Avg Runtime</div>
              <div className="text-xs text-muted-foreground/70 mt-1">
                {task.task_history && task.task_history.length > 0
                  ? `from ${task.task_history.filter(run => run.start_time && run.end_time).length} completed runs`
                  : 'no completed runs'
                }
              </div>
            </div>

            <div className="text-center">
              <div className={cn(
                "text-2xl font-bold capitalize",
                !task.enable ? "text-gray-500" :
                task.execution_status === 'succeeded' ? "text-emerald-600" :
                task.execution_status === 'failed' ? "text-red-600" :
                task.execution_status === 'running' ? "text-blue-600" : "text-gray-600"
              )}>
                {!task.enable ? 'Disabled' : task.execution_status || 'Pending'}
              </div>
              <div className="text-sm text-muted-foreground">Status</div>
              <div className="text-xs text-muted-foreground/70 mt-1">
                {!task.enable ? 'task is currently disabled' : 'last execution status'}
              </div>
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Description */}
          <Card className="border">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-primary" />
                <CardTitle className="text-lg">Description</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {task.description ? (
                <DescriptionContent description={task.description} />
              ) : (
                <span className="text-muted-foreground italic text-sm">
                  No description provided for this task.
                </span>
              )}
            </CardContent>
          </Card>

          {/* Schedule Timeline */}
          <Card className="border">
            <CardContent className="p-4">
              <ScheduleTimeline
                lastRun={task.last_run}
                nextRun={task.next_run}
                schedule={task.schedule}
                enable={task.enable}
                selectedTimezone={selectedTimezone}
                onTimezoneChange={setSelectedTimezone}
                taskId={task.id}
              />
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="border">
          <CardContent className="p-4">
            <EnhancedRecentActivity
              taskHistory={task.task_history}
              task={task}
              onViewAll={() => handleTabChange('history')}
              onExecutionClick={handleExecutionClick}
              taskId={task.id}
              initialItems={8}
              maxExpandedItems={20}
              enablePagination={true}
              variant="standard"
            />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="history" className="space-y-4">
        {/* Enhanced execution history with navigation support */}
        <div className={cn(isNavigating && "navigation-loading")}>
          <ExecutionHistoryList
            taskHistory={task.task_history}
                            taskId={task.id}
            agentId={task.agent_config?.agent_id as string}
            highlightExecutionId={highlightedExecutionId}
                          />
                        </div>
      </TabsContent>

      <TabsContent value="edit">
        <ItemForm initialData={task} pageTitle="Edit Task" action={ActionType.Edit} />
      </TabsContent>
    </Tabs>
  );
}
