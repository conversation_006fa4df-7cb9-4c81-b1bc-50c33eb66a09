'use client';

import { memo, useState, useMemo, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import {
  Activity,
  ChevronRight,
  BarChart3,
  Power,
  PowerOff,
  RefreshCcw,
  StopCircle,
} from 'lucide-react';
import { TaskHistory, TaskResponse } from '@/client/types.gen';
import { TasksService } from '@/client';
import { cn } from '@/lib/utils';
import { formatUtcDate, DateFormat } from '@/lib/date-utils';
import TimelineExecutionPoint from './timeline-execution-point';
import TimelineStatusLegend from './timeline-status-legend';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CacheKey } from '@/components/utils/cache-key';
import { toast } from 'sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface EnhancedRecentActivityProps {
  taskHistory?: TaskHistory[];
  task?: TaskResponse; // Add task prop for enable/disable functionality
  onViewAll: () => void;
  onExecutionClick?: (executionId: string) => void;
  taskId?: string;
  initialItems?: number;      // Default visible items
  maxExpandedItems?: number;  // Max items when expanded
  enablePagination?: boolean; // Enable pagination
  variant?: 'compact' | 'standard' | 'detailed';
  className?: string;
}

// Helper function to get status text consistently
const getStatusText = (status?: string | null): string => {
  if (!status) return 'pending';

  const normalizedStatus = status.toLowerCase().replace(/_/g, ' ').trim();

  if (normalizedStatus.includes('fail') || normalizedStatus.includes('error')) {
    return 'failed';
  }

  if (normalizedStatus.includes('success') || normalizedStatus === 'completed') {
    return 'succeeded';
  }

  if (normalizedStatus.includes('running') || normalizedStatus.includes('progress')) {
    return 'running';
  }

  if (normalizedStatus.includes('cancel')) {
    return 'cancelled';
  }

  if (normalizedStatus.includes('approval')) {
    return 'required_approval';
  }

  return normalizedStatus;
};

// Enhanced Task Enable/Disable Toggle with Modern UI
const TaskEnableToggle = ({ task }: { task: TaskResponse }) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const isRunning = task.execution_status === 'running';

  const enableMutation = useMutation({
    mutationFn: ({ id, enable }: { id: string; enable: boolean }) =>
      TasksService.updateTaskEnable({ taskId: id, enable }),
    onSuccess: (updatedTask, { enable }) => {
      if (enable) {
        toast.success('Task Enabled', {
          description: `"${task.title}" is now enabled and will run according to its schedule.`,
          duration: 5000,
        });
      } else {
        toast.success('Task Disabled', {
          description: `"${task.title}" has been disabled. Scheduled executions have been cancelled.`,
          duration: 5000,
        });
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.body?.detail || error?.message || 'Failed to update task status';
      toast.error('Operation Failed', {
        description: `Could not ${task.enable ? 'disable' : 'enable'} task: ${errorMessage}`,
        duration: 6000,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      queryClient.invalidateQueries({ queryKey: [CacheKey.Items] });
    }
  });

  const handleToggle = async () => {
    if (isRunning && task.enable) {
      toast.warning('Task Currently Running', {
        description: `"${task.title}" is currently running. Disabling will prevent future executions.`,
        duration: 6000,
      });
    }

    setLoading(true);
    try {
      await enableMutation.mutateAsync({ id: task.id, enable: !task.enable });
    } finally {
      setLoading(false);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2">
            <span className={cn(
              "text-sm font-medium",
              task.enable ? "text-emerald-700 dark:text-emerald-300" : "text-gray-600 dark:text-gray-400"
            )}>
              {task.enable ? 'Enabled' : 'Disabled'}
            </span>
            <button
              onClick={handleToggle}
              disabled={loading}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                task.enable
                  ? "bg-emerald-600 shadow-emerald-200 dark:shadow-emerald-800"
                  : "bg-gray-200 dark:bg-gray-700",
                loading && "opacity-50 cursor-not-allowed",
                isRunning && task.enable && "ring-2 ring-blue-500/30"
              )}
              role="switch"
              aria-checked={task.enable}
              aria-label={task.enable ? "Disable task" : "Enable task"}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-all duration-200 shadow-lg flex items-center justify-center",
                  task.enable ? "translate-x-6" : "translate-x-1"
                )}
              >
                {loading ? (
                  <RefreshCcw className="h-2 w-2 text-gray-600" />
                ) : task.enable ? (
                  <Power className="h-2 w-2 text-emerald-600" />
                ) : (
                  <PowerOff className="h-2 w-2 text-gray-400" />
                )}
              </span>
            </button>
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          className="max-w-xs bg-card border-border/60 shadow-lg backdrop-blur-sm"
        >
          <div className="text-center space-y-1.5">
            <div className="font-medium text-card-foreground">
              {task.enable ? "Disable Task" : "Enable Task"}
            </div>
            <div className="text-xs text-muted-foreground">
              {task.enable
                ? "Stop scheduled and running executions"
                : "Allow task to run according to schedule"
              }
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Enhanced Stop Execution Control - Always Visible for Running Tasks
const StopExecutionControl = ({
  task,
  execution
}: {
  task: TaskResponse;
  execution: TaskHistory;
}) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const status = getStatusText(execution.status);

  const stopMutation = useMutation({
    mutationFn: () => TasksService.stopTaskExecution({
      taskId: task.id,
      conversationId: execution.conversation_id
    }),
    onSuccess: () => {
      toast.success('Execution Stopped', {
        description: `Execution has been cancelled successfully.`,
        duration: 4000,
      });
    },
    onError: (error: any) => {
      const errorMessage = error?.body?.detail || error?.message || 'Failed to stop execution';
      toast.error('Stop Failed', {
        description: `Could not stop execution: ${errorMessage}`,
        duration: 6000,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      queryClient.invalidateQueries({ queryKey: [CacheKey.Items] });
    }
  });

  const handleStop = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setLoading(true);
    try {
      await stopMutation.mutateAsync();
    } finally {
      setLoading(false);
    }
  };

  if (status !== 'running') return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            onClick={handleStop}
            disabled={loading}
            className={cn(
              "h-6 w-6 p-0 flex items-center justify-center transition-colors duration-200",
              "border-red-500 text-red-600 bg-red-50",
              "dark:border-red-500 dark:text-red-400 dark:bg-red-950/30",
              loading && "opacity-70 cursor-not-allowed"
            )}
          >
            {loading ? (
              <RefreshCcw className="h-3 w-3" />
            ) : (
              <StopCircle className="h-3 w-3" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent
          className="bg-card/95 border-border/60 shadow-lg backdrop-blur-sm tooltip-error"
          side="top"
        >
          <div className="text-center space-y-1.5 tooltip-content-enhanced">
            <div className="tooltip-title text-destructive-foreground">
              {loading ? "Stopping Execution" : "Stop Execution"}
            </div>
            <div className="tooltip-description text-muted-foreground">
              {loading
                ? "Cancelling the running task execution..."
                : "Immediately terminate this running execution"
              }
            </div>
            {!loading && (
              <div className="text-xs text-orange-600 dark:text-orange-400 border-t border-border/40 pt-1.5 mt-2">
                ⚠️ This action cannot be undone
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const EnhancedRecentActivity = memo(function EnhancedRecentActivity({
  taskHistory,
  task,
  onViewAll,
  onExecutionClick,
  taskId,
  initialItems = 8,
  maxExpandedItems = 20,
  enablePagination = true,
  variant = 'standard',
  className
}: EnhancedRecentActivityProps) {
  // Pagination state
  const [showAll, setShowAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = showAll ? maxExpandedItems : initialItems;

  // Calculate visible executions with pagination
  const { visibleExecutions, totalPages, hasMore, canPaginate } = useMemo(() => {
    const executions = taskHistory || [];
    const total = executions.length;
    const hasMoreThanInitial = total > initialItems;

    if (!showAll) {
      // Show initial items only
      return {
        visibleExecutions: executions.slice(0, initialItems),
        totalPages: 1,
        hasMore: hasMoreThanInitial,
        canPaginate: false
      };
    }

    // Show paginated expanded view
    const pages = Math.ceil(total / maxExpandedItems);
    const startIndex = currentPage * maxExpandedItems;
    const endIndex = startIndex + maxExpandedItems;

    return {
      visibleExecutions: executions.slice(startIndex, endIndex),
      totalPages: pages,
      hasMore: hasMoreThanInitial,
      canPaginate: pages > 1
    };
  }, [taskHistory, showAll, currentPage, initialItems, maxExpandedItems]);

  // Handle expand/collapse
  const handleToggleExpand = useCallback(() => {
    setShowAll(prev => {
      if (prev) {
        // Collapsing - reset to first page
        setCurrentPage(0);
      }
      return !prev;
    });
  }, []);

  // Handle pagination
  const handlePrevPage = useCallback(() => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  }, [totalPages]);

  // Empty state
  if (!taskHistory || taskHistory.length === 0) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <Activity className="w-4 h-4 text-primary" />
            </div>
            <div>
              <div className="flex items-center gap-4">
                <h3 className="font-medium text-base">Recent Activity</h3>
                {task && <TaskEnableToggle task={task} />}
              </div>
              <p className="text-sm text-muted-foreground">
                Monitor task execution history and controls
              </p>
            </div>
          </div>
        </div>

        <div className="card p-8 border-dashed">
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="w-12 h-12 rounded-lg bg-muted/50 flex items-center justify-center">
              <Activity className="w-6 h-6 text-muted-foreground" />
            </div>
            <div>
              <h4 className="font-medium text-muted-foreground">No Recent Activity</h4>
              <p className="text-sm text-muted-foreground/70 mt-1 max-w-sm">
                Task executions will appear here as an interactive timeline with controls.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Enhanced Header Section with Task Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
            <Activity className="w-4 h-4 text-primary" />
          </div>
          <div>
            <div className="flex items-center gap-4">
              <h3 className="font-medium text-base">Recent Activity</h3>
              {task && <TaskEnableToggle task={task} />}

              {/* Running Executions Stop Controls */}
              {task && taskHistory && taskHistory.some(exec => getStatusText(exec.status) === 'running') && (
                <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800/50">
                  <div className="flex items-center gap-1 text-xs text-blue-700 dark:text-blue-300">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span className="font-medium">Running executions:</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {taskHistory
                      .filter(exec => getStatusText(exec.status) === 'running')
                      .map((execution, index) => (
                        <StopExecutionControl
                          key={execution.id || `stop-${index}`}
                          task={task}
                          execution={execution}
                        />
                      ))
                    }
                  </div>
                </div>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-0.5">
              {showAll ? (
                <>Showing {visibleExecutions.length} of {taskHistory.length} executions</>
              ) : (
                <>Last {visibleExecutions.length} execution{visibleExecutions.length > 1 ? 's' : ''}</>
              )}
            </p>
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="h-8 px-3 text-xs button"
          onClick={onViewAll}
        >
          <BarChart3 className="w-3 h-3 mr-1.5" />
          View All History
          <ChevronRight className="w-3 h-3 ml-1.5" />
        </Button>
      </div>

      {/* Timeline Container */}
      <div className="card p-6 overflow-hidden">
        {/* Enhanced Beautiful Timeline with Intelligent Spacing */}
        <div className="relative px-20 py-8">
          {/* Calculate intelligent spacing based on execution count and UI/UX best practices */}
          {(() => {
            const executionCount = visibleExecutions.length;
            const hasExecutions = executionCount > 0;
            const containerWidth = executionCount <= 3 ? 'max-w-2xl' : 'w-full';

            // Calculate intelligent positioning
            const getExecutionPositions = () => {
              if (executionCount <= 1) return ['50%']; // Center single execution
              if (executionCount === 2) return ['25%', '75%']; // Balanced positioning for 2
              if (executionCount === 3) return ['16.67%', '50%', '83.33%']; // Balanced for 3

              // For 4+ executions, use even distribution
              return visibleExecutions.map((_, index) =>
                `${(index / Math.max(executionCount - 1, 1)) * 100}%`
              );
            };

            const positions = getExecutionPositions();
            return (
               <div className={cn("mx-auto timeline-intelligent-spacing", containerWidth)}>
                {/* Intelligent Timeline Line - Centered */}
                <div className="relative" style={{ height: '6rem' }}>
                  {/* Main timeline line positioned in the middle */}
                  <div className="absolute top-1/4 left-0 right-0 transform -translate-y-1 z-0">
                    <div className="relative h-1 timeline-line-enhanced">
                      {/* Subtle glow effect */}
                      <div className="absolute inset-0 h-1 timeline-line-glow rounded-full"></div>
                      {/* Smart end caps - only show if we have executions */}
                      {hasExecutions && (
                        <>
                          <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 rounded-full timeline-end-cap"></div>
                          <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 rounded-full timeline-end-cap"></div>
                        </>
                      )}

                      {/* Latest/Oldest indicators positioned at the tips of the timeline bar */}
                      {hasExecutions && visibleExecutions.length > 1 && (
                        <>
                          {/* Latest indicator at the left tip */}
                          <div className="absolute -left-24 top-1/2 transform -translate-y-1/2">
                            <div className="flex items-center gap-2 text-xs timeline-time-label bg-background/90 px-2 py-1 rounded border border-border/50 shadow-sm">
                              <span className="font-medium text-foreground whitespace-nowrap">Latest</span>
                              <div className="w-2 h-2 rounded-full bg-primary" />
                            </div>
                          </div>

                          {/* Oldest indicator at the right tip */}
                          <div className="absolute -right-24 top-1/2 transform -translate-y-1/2">
                            <div className="flex items-center gap-2 text-xs timeline-time-label bg-background/90 px-2 py-1 rounded border border-border/50 shadow-sm">
                            <div className="w-2 h-2 rounded-full bg-primary" />
                              <span className="font-medium text-foreground whitespace-nowrap">Oldest</span>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Timeline Execution Points positioned exactly on the timeline */}
                  <div className="timeline-execution-container">
                    {hasExecutions ? (
                      visibleExecutions.map((execution, index) => {
                        // Format time for display above execution point (convert UTC to local time)
                        const timeLabel = execution.start_time
                          ? formatUtcDate(execution.start_time, DateFormat.TIME)
                          : 'Time here';

                        return (
                          <div
                            key={execution.id || `timeline-${index}`}
                            className="timeline-point-positioned"
                            style={{
                              left: positions[index]
                            }}
                          >
                            {/* Time label above the execution point */}
                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4">
                              <div className="px-3 py-1.5 text-xs font-medium text-foreground bg-background/95 rounded-md border border-border/60 shadow-md whitespace-nowrap backdrop-blur-sm">
                                {execution.start_time
                                  ? formatUtcDate(execution.start_time, DateFormat.SHORT_DATE) + ' ' + timeLabel
                                  : 'Time here'
                                }
                              </div>
                            </div>

                            <div className="relative group flex flex-col items-center timeline-execution-point">
                              <TimelineExecutionPoint
                                execution={execution}
                                index={index}
                                onClick={onExecutionClick ? undefined : onViewAll}
                                onExecutionClick={onExecutionClick}
                                taskId={taskId}
                                size={variant === 'compact' ? 'sm' : variant === 'detailed' ? 'lg' : 'md'}
                                showOrderIndicator={false}
                                showStatusBadge={true}
                              />
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      /* Placeholder when no executions visible */
                      <div className="flex items-center justify-center">
                        <div className="text-sm text-muted-foreground italic">No executions to display</div>
                      </div>
                    )}
                  </div>


                </div>
              </div>
            );
          })()}
        </div>

        {/* Summary Section */}
        <TimelineStatusLegend
          size="sm"
          className="flex-wrap"
        />
      </div>
    </div>
  );
});

export default EnhancedRecentActivity;
