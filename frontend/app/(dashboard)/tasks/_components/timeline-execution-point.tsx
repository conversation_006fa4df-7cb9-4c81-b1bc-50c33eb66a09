'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  CheckCircle,
  XCircle,
  RefreshCcw,
  StopCircleIcon,
  Clock,
  Zap,
  Timer,
  AlertTriangle,
  ExternalLink
} from 'lucide-react';
import { TaskHistory, TaskExecutionStatus } from '@/client/types.gen';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface TimelineExecutionPointProps {
  execution: TaskHistory;
  index: number;
  onClick?: () => void;
  onExecutionClick?: (executionId: string) => void;
  taskId?: string;
  size?: 'sm' | 'md' | 'lg';
  showOrderIndicator?: boolean;
  showStatusBadge?: boolean;
}

// Status configuration aligned with design system and unified color scheme
const getStatusConfig = (status: TaskExecutionStatus) => {
  switch (status) {
    case 'succeeded':
      return {
        icon: CheckCircle,
        color: 'text-emerald-600 dark:text-emerald-400',
        bgColor: 'bg-emerald-500 hover:bg-emerald-600',
        badgeColor: 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950/50 dark:text-emerald-300 dark:border-emerald-800/50',
        badgeVariant: 'default' as const,
        lightBg: 'bg-emerald-50 dark:bg-emerald-950/20'
      };
    case 'failed':
      return {
        icon: XCircle,
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-500 hover:bg-red-600',
        badgeColor: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950/20 dark:text-red-400 dark:border-red-800/50',
        badgeVariant: 'destructive' as const,
        lightBg: 'bg-red-50 dark:bg-red-950/20 border-red-100 dark:border-red-900/30'
      };
    case 'running':
      return {
        icon: RefreshCcw,
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-500 hover:bg-blue-600',
        badgeColor: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800/50',
        badgeVariant: 'default' as const,
        lightBg: 'bg-blue-50 dark:bg-blue-950/50'
      };
    case 'cancelled':
      return {
        icon: StopCircleIcon,
        color: 'text-orange-600 dark:text-orange-400',
        bgColor: 'bg-orange-500 hover:bg-orange-600',
        badgeColor: 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950/50 dark:text-orange-300 dark:border-orange-800/50',
        badgeVariant: 'secondary' as const,
        lightBg: 'bg-orange-50 dark:bg-orange-950/50'
      };
    case 'required_approval':
      return {
        icon: AlertTriangle,
        color: 'text-amber-600 dark:text-amber-400',
        bgColor: 'bg-amber-500 hover:bg-amber-600',
        badgeColor: 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/50 dark:text-amber-300 dark:border-amber-800/50',
        badgeVariant: 'secondary' as const,
        lightBg: 'bg-amber-50 dark:bg-amber-950/50'
      };
    default:
      return {
        icon: Clock,
        color: 'text-gray-600 dark:text-gray-400',
        bgColor: 'bg-gray-400 hover:bg-gray-500',
        badgeColor: 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-300 dark:border-gray-800/50',
        badgeVariant: 'outline' as const,
        lightBg: 'bg-gray-50 dark:bg-gray-950/50'
      };
  }
};

// Calculate execution duration with better formatting (handle UTC times)
const calculateDuration = (startTime?: string, endTime?: string | null) => {
  if (!startTime) return 'N/A';
  const start = new Date(startTime + 'Z');
  const end = endTime ? new Date(endTime + 'Z') : new Date();
  const durationMs = end.getTime() - start.getTime();

  if (durationMs < 1000) return `${durationMs}ms`;
  if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`;
  if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m`;
  return `${Math.round(durationMs / 3600000)}h ${Math.round((durationMs % 3600000) / 60000)}m`;
};

// Size configurations for responsive design
const getSizeConfig = (size: 'sm' | 'md' | 'lg') => {
  switch (size) {
    case 'sm':
      return {
        point: 'w-4 h-4',
        icon: 'w-2 h-2',
        orderIndicator: 'text-xs px-1.5 py-0.5',
        badge: 'h-4 px-1.5 text-xs',
        container: 'gap-1'
      };
    case 'lg':
      return {
        point: 'w-8 h-8',
        icon: 'w-4 h-4',
        orderIndicator: 'text-sm px-3 py-1',
        badge: 'h-6 px-3 text-sm',
        container: 'gap-3'
      };
    default: // md
      return {
        point: 'w-6 h-6',
        icon: 'w-3 h-3',
        orderIndicator: 'text-xs px-2 py-1',
        badge: 'h-5 px-2 text-xs',
        container: 'gap-2'
      };
  }
};

export default function TimelineExecutionPoint({
  execution,
  index,
  onClick,
  onExecutionClick,
  taskId,
  size = 'md',
  showOrderIndicator = true,
  showStatusBadge = true
}: TimelineExecutionPointProps) {
  const config = getStatusConfig(execution.status);
  const sizeConfig = getSizeConfig(size);
  const StatusIcon = config.icon;
  const isRunning = execution.status === 'running';
  const shortId = execution.conversation_id?.split('-')[0] || 'N/A';
  const timeAgo = execution.start_time
    ? formatDistanceToNow(new Date(execution.start_time + 'Z')) + ' ago'
    : 'Unknown time';
  const duration = calculateDuration(execution.start_time, execution.end_time);

  // State for instant visual feedback
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    // Immediate visual feedback with enhanced animation
    setIsClicked(true);

    // Prioritize execution-specific navigation over generic onClick
    if (onExecutionClick && execution.conversation_id) {
      onExecutionClick(execution.conversation_id);
    } else if (onClick) {
      onClick();
    }

    // Reset clicked state after animation completes
    setTimeout(() => setIsClicked(false), 300);
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div
            className={cn(
              "relative flex flex-col items-center cursor-pointer group",
              "hover:scale-105 transition-all duration-200 ease-out",
              "hover:-translate-y-0.5",
              isClicked && "timeline-point-clicked",
              sizeConfig.container
            )}
            onClick={handleClick}
            role="button"
            tabIndex={0}
            aria-label={`Execution ${index + 1}: ${execution.status} - Click to view details`}
          >
            {/* Execution Point */}
            <div className={cn(
              "relative rounded-full border-2 border-background shadow-sm z-20 flex items-center justify-center",
              "transition-all duration-200 ease-out",
              "group-hover:shadow-md group-hover:border-background/80",
              sizeConfig.point,
              config.bgColor,

            )}>
              <StatusIcon className={cn(
                "text-white transition-transform duration-200",
                sizeConfig.icon,
                "group-hover:scale-110"
              )} />

              {/* Ring for Running Status */}
              {isRunning && (
                <div className="absolute inset-0 rounded-full border-2 border-info animate-ping opacity-30" />
              )}

              {/* Hover Ring Effect */}
              <div className={cn(
                "absolute inset-0 rounded-full opacity-0 transition-opacity duration-200",
                "group-hover:opacity-20",
                config.bgColor,
                "scale-125"
              )} />
            </div>

            {/* Order Indicator */}
            {showOrderIndicator && (
              <div className={cn(
                "mt-1.5 rounded-lg font-medium text-xs px-1.5 py-0.5",
                "dark:bg-white/10 dark:border dark:border-white/20",
                "dark:shadow-sm dark:backdrop-blur-sm dark:hover:bg-white/20",
                config.lightBg,
                config.color
              )}>
                {index + 1}
              </div>
            )}

            {/* Status Information */}
            {showStatusBadge && (
              <div className="flex flex-col items-center gap-1 mt-1.5">
                <Badge
                  variant="outline"
                  className={cn(
                    "font-medium flex items-center gap-1 text-xs border-2",
                    sizeConfig.badge,
                    config.badgeColor
                  )}
                >
                  {isRunning && <Zap className="w-2.5 h-2.5" />}
                  {execution.status}
                </Badge>

                {/* Duration Badge - hide for running tasks */}
                {size !== 'sm' && (
                  <Badge variant="outline" className={cn(
                    "h-4 px-1 text-xs",
                    !isRunning && duration !== 'N/A' ? "opacity-75" : "opacity-0"
                  )}>
                    <Timer className="w-2 h-2 mr-0.5" />
                    {!isRunning && duration !== 'N/A' ? duration : "00:00"}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </TooltipTrigger>

        {/* Tooltip with Execution Details */}
        <TooltipContent
          side="bottom"
          className="max-w-sm p-4 bg-popover/95 backdrop-blur-sm border shadow-lg animate-in fade-in-0 zoom-in-95"
          sideOffset={12}
        >
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-center gap-3 pb-3 border-b border-border/50">
              <div className={cn(
                "w-8 h-8 rounded-lg flex items-center justify-center shadow-sm",
                config.bgColor
              )}>
                <StatusIcon className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-semibold text-sm text-popover-foreground">Execution #{shortId}</div>
                <div className="text-xs text-muted-foreground">{timeAgo}</div>
              </div>
            </div>

            {/* Execution Information */}
            <div className="space-y-2.5 text-sm">
              <div className="flex items-center justify-between p-2 rounded-lg bg-muted/30 border border-border/50">
                <div className="flex items-center gap-2">
                  <div className={cn("w-2 h-2 rounded-full", config.bgColor)} />
                  <span className="font-medium text-popover-foreground">Status:</span>
                </div>
                <Badge variant="outline" className={cn("h-5 text-xs shadow-sm border-2", config.badgeColor)}>
                  {execution.status}
                </Badge>
              </div>

              {duration !== 'N/A' && !isRunning && (
                <div className="flex items-center justify-between p-2 rounded-lg bg-muted/30 border border-border/50">
                  <div className="flex items-center gap-2">
                    <Timer className="w-3 h-3 text-muted-foreground" />
                    <span className="font-medium text-popover-foreground">Duration:</span>
                  </div>
                  <span className="text-xs text-popover-foreground font-mono">{duration}</span>
                </div>
              )}

              {/* Error Display */}
              {execution.message && execution.status === 'failed' && (
                <div className="p-3 rounded-lg bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800/50 shadow-sm failed-status-enhanced">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-error-600 dark:text-error-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-semibold text-error-700 dark:text-error-300 mb-1">Error Details</div>
                      <div className="text-xs text-error-600 dark:text-error-400 break-words leading-relaxed">
                        {execution.message.slice(0, 120)}
                        {execution.message.length > 120 && '...'}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Approval Required Display */}
              {execution.message && execution.status === 'required_approval' && (() => {
                const parseApprovalMessage = (message: string) => {
                  const operationMatch = message.match(/Operation:\s*([^\n]+)/);
                  const detailsMatch = message.match(/Details:\s*([\s\S]*?)(?:\n\nPlease confirm|$)/);

                  return {
                    toolName: operationMatch?.[1]?.trim() || 'Unknown Operation',
                    toolArgs: detailsMatch?.[1]?.trim() || message
                  };
                };

                const { toolName, toolArgs } = parseApprovalMessage(execution.message);

                return (
                  <div className="p-3 rounded-lg bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 shadow-sm">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0 space-y-2">
                        <div className="text-xs font-semibold text-amber-700 dark:text-amber-300">
                          Approval Required
                        </div>
                        <div className="space-y-1">
                          <div className="text-xs">
                            <span className="font-medium text-amber-700 dark:text-amber-300">Operation: </span>
                            <span className="text-amber-600 dark:text-amber-400 font-mono">
                              {toolName.length > 30 ? toolName.slice(0, 30) + '...' : toolName}
                            </span>
                          </div>
                          <div className="text-xs">
                            <span className="font-medium text-amber-700 dark:text-amber-300">Details: </span>
                            <span className="text-amber-600 dark:text-amber-400">
                              {toolArgs.slice(0, 60)}
                              {toolArgs.length > 60 && '...'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Call to Action */}
            <div className="flex items-center gap-2 pt-3 border-t border-border/50 text-xs text-muted-foreground">
              <ExternalLink className="w-3 h-3" />
              <span>
                {onExecutionClick ? 'Click to jump to this execution' : 'Click to view full execution history'}
              </span>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
