// QuickStart.tsx
'use client';

import { cn } from '@/lib/utils';
import { useState, useRef, useEffect } from 'react';
import { Hub, QuickStartCardType } from './hub';
import { PrefetchAgents } from './prefetch-agents';
import { QuickGenerate } from './quick-generate';
import { QuickJumpDialog } from './quick-jump';
import { ExamplePrompts, type ExamplePrompt } from './example-prompts';
import { HeroHeading } from './hero-heading';
import { useTaskTemplates } from '@/hooks/use-task-templates';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useAgents } from '@/hooks/use-agents';
import { AutonomousAgentsService } from '@/client';
import { useAgentConversation } from '@/lib/utils/agent-conversation';
import { ArrowDown, LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useInView } from 'react-intersection-observer';

export interface RecentTask extends QuickStartCardType {
  timestamp: number;
  type: 'generated' | 'direct';
}

const MAX_SAVED_TASKS = 10;
const TASK_TEMPLATES_KEY = 'task_templates';

const TAGS = [
  { label: 'Analysis', value: 'analysis' },
  { label: 'Optimization', value: 'optimization' },
  { label: 'Monitoring', value: 'monitoring' },
  { label: 'Security', value: 'security' }
];

export default function QuickStart() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<{
    title: string;
    description: string;
    category?: string;
    isGenerated?: boolean;
    service?: string;
    icon?: LucideIcon;
  } | null>(null);

  const { createAgentConversation } = useAgentConversation();
  const hubSectionNodeRef = useRef<HTMLDivElement | null>(null);
  const { ref: inViewRef, inView: isHubInView } = useInView({ threshold: 0.1 });

  const setHubSectionRef = (node: HTMLDivElement | null) => {
    hubSectionNodeRef.current = node;
    inViewRef(node);
  };

  const handleCardClick = (card: QuickStartCardType) => {
    setSelectedTask({
      title: card.title,
      description: card.description,
      category: card.category,
      service: card.service,
      icon: card.icon,
      isGenerated: false
    });
    setIsDialogOpen(true);
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      setTimeout(() => {
        if (!isDialogOpen) {
          setSelectedTask(null);
        }
      }, 300);
    }
  };

  const handleTaskGenerated = (task: {
    title: string;
    description: string;
    category?: string;
  }) => {
    setSelectedTask(task);
    setIsDialogOpen(true);
  };

  // Handle example prompt click
  const handleExamplePrompt = async (prompt: ExamplePrompt) => {
    await createAgentConversation({
      message: prompt.question,
      onSuccess: () => {
        // Any additional success handling specific to example prompts
      },
      onError: (error) => {
        // Any additional error handling specific to example prompts
        console.error('Example prompt error:', error);
      }
    });
  };

  const handleScrollToHub = () => {
    hubSectionNodeRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="flex flex-1 flex-col overflow-y-auto bg-gradient-to-b from-background via-background/95 to-background/90">
      {/* Prefetch agents data */}
      <PrefetchAgents />

      <div className="relative flex min-h-full flex-col justify-center gap-y-8 overflow-y-auto">
        <HeroHeading />

        <div className='flex flex-col justify-center'>
          <div className="w-full px-2">
            <QuickGenerate
              showGitHubButton={false}
              setSelectedTask={handleTaskGenerated}
              setIsDialogOpen={setIsDialogOpen}
              messagesRemaining={0}
              className="rounded-lg shadow-none transition-all duration-200 hover:shadow-none"
            />
            <div className="pointer-events-none absolute inset-0 -z-10 rounded-3xl bg-primary/10 blur-lg" />
          </div>

          {/* Example Prompts Section */}
          <div className="px-2">
            <ExamplePrompts onPromptClick={handleExamplePrompt} />
          </div>
        </div>

        {/* Scroll to Hub Button */}
        {!isHubInView && (
          <div className="pointer-events-none absolute bottom-4 left-0 right-0 z-50 flex justify-center">
            <Button
              variant="outline"
              size="sm"
              className="pointer-events-auto animate-bounce rounded-3xl border-2 border-primary/20 bg-background/80 text-primary shadow-lg backdrop-blur-sm hover:border-primary/40 hover:bg-primary/10"
              onClick={handleScrollToHub}
            >
              <ArrowDown className="mr-2 h-4 w-4" />
              Hub Section
            </Button>
          </div>
        )}
      </div>

      {/* Hub Section */}
      <div ref={setHubSectionRef} className="relative">
        <Hub onCardClick={handleCardClick} isLoading={false} />
      </div>

      {/* Dialog */}
      {selectedTask && (
        <QuickJumpDialog
          open={isDialogOpen}
          onOpenChange={handleDialogOpenChange}
          initialMessage={selectedTask.description}
          title={`Configure Agent for: ${selectedTask.title}`}
          task_title={selectedTask.title}
          isGenerated={selectedTask.isGenerated}
        />
      )}
    </div>
  );
}
