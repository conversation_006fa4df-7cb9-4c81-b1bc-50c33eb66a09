'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, DollarSign, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

interface ExamplePrompt {
  icon: React.ReactNode;
  title: string;
  question: string;
  color: string;
  id: string;
}

const EXAMPLE_PROMPTS: ExamplePrompt[] = [
  {
    icon: <DollarSign className="h-9 w-9 text-emerald-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.3)]" />,
    title: 'Cost Optimization',
    question:
      'Can you identify any underutilized EC2 instances in our environment?',
    color: 'from-emerald-400/30 to-teal-600/30',
    id: 'ec2-optimization'
  },
  {
    icon: <Shield className="h-9 w-9 text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.3)]" />,
    title: 'Security Audit',
    question:
      'Can you review our S3 buckets and identify any potential security risks?',
    color: 'from-blue-400/30 to-indigo-600/30',
    id: 's3-security'
  },
  {
    icon: <BarChart className="h-9 w-9 text-violet-400 drop-shadow-[0_0_8px_rgba(167,139,250,0.3)]" />,
    title: 'Performance Monitoring',
    question: 'Is our main web application performing normally on our ALB?',
    color: 'from-violet-400/30 to-purple-600/30',
    id: 'alb-monitoring'
  },
  {
    icon: <Cloud className="h-9 w-9 text-amber-400 drop-shadow-[0_0_8px_rgba(251,191,36,0.3)]" />,
    title: 'Resource Provisioning',
    question:
      'How can I set up a new VPC with maximum cost savings for a development environment?',
    color: 'from-amber-400/30 to-orange-600/30',
    id: 'vpc-setup'
  }
];

const EXAMPLE_PROMPT_DETAILS: Record<
  string,
  { description: string; category?: string }
> = {
  'ec2-optimization': {
    description:
      'Analyze EC2 instance utilization metrics to identify opportunities for cost optimization and resource right-sizing.',
    category: 'optimization'
  },
  's3-security': {
    description:
      'Perform a comprehensive security audit of S3 bucket configurations, permissions, and access patterns.',
    category: 'security'
  },
  'alb-monitoring': {
    description:
      'Monitor and analyze the performance metrics of your web application load balancer for any anomalies or issues.',
    category: 'monitoring'
  },
  'vpc-setup': {
    description:
      'Design and implement a cost-effective VPC architecture optimized for development workloads.',
    category: 'optimization'
  }
};

interface ExamplePromptsProps {
  onPromptClick: (prompt: ExamplePrompt) => void;
}

export function ExamplePrompts({ onPromptClick }: ExamplePromptsProps) {
  return (
    <div className="mx-auto mt-4 h-full w-full max-w-4xl px-4">
      <h2 className="mb-4 text-center text-xl font-semibold tracking-tight text-foreground/90 md:mb-6 md:text-2xl">
        Quick Start
      </h2>
      <div className="flex animate-fade-in flex-row items-center justify-center gap-6 py-2 md:gap-8">
        <TooltipProvider>
          {EXAMPLE_PROMPTS.map((prompt, idx) => (
            <Tooltip key={prompt.id}>
              <TooltipTrigger asChild>
                <button
                  onClick={() => onPromptClick(prompt)}
                  className={cn(
                    'group relative rounded-full border p-4 transition-all duration-300 md:p-5',
                    'hover:scale-105 hover:border-primary/40 hover:shadow-lg',
                    'bg-card/80 backdrop-blur-md',
                    'active:scale-95',
                    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50',
                    'transform-gpu'
                  )}
                  style={{ 
                    animationDelay: `${idx * 150}ms`,
                    animationFillMode: 'both'
                  }}
                >
                  {/* Gradient background with improved hover effect */}
                  <div
                    className={cn(
                      'absolute inset-0 rounded-full opacity-0 transition-all duration-500',
                      'group-hover:opacity-20 group-hover:scale-110',
                      `bg-gradient-to-br ${prompt.color}`,
                      'transform-gpu'
                    )}
                  />
                  {/* Subtle shine effect on hover */}
                  <div
                    className={cn(
                      'absolute inset-0 rounded-full opacity-0 transition-opacity duration-500',
                      'group-hover:opacity-15',
                      'bg-gradient-to-tr from-white/30 to-transparent'
                    )}
                  />
                  <div className="relative transform-gpu transition-all duration-300 group-hover:scale-110 group-hover:brightness-110">
                    {prompt.icon}
                  </div>
                </button>
              </TooltipTrigger>
              <TooltipContent 
                side="bottom" 
                className="rounded-lg border bg-popover/95 px-3 py-2 text-sm shadow-md backdrop-blur-sm"
              >
                <p className="font-medium">{prompt.title}</p>
                <p className="text-xs text-muted-foreground">{prompt.question}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </TooltipProvider>
      </div>
    </div>
  );
}

export type { ExamplePrompt };
export { EXAMPLE_PROMPT_DETAILS };
