'use client'

export function HeroHeading() {
    return (
        <div className="relative py-8">
            {/* Background Decorative Elements */}
            <div className="absolute inset-0 overflow-hidden">
                {/* Top gradient beam - animated version */}
                <div className="absolute -top-4 left-1/2 -translate-x-1/2 w-32 h-1.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent blur-sm animate-pulse" />

                {/* Glowing orb - animated version */}
                <div 
                    className="absolute -top-8 left-1/2 -translate-x-1/2 w-24 h-24 bg-primary/5 rounded-full blur-xl"
                    style={{
                        animation: 'float 6s ease-in-out infinite',
                    }}
                />

                {/* Additional decorative beams - animated version */}
                <div className="w-full h-32 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent rotate-[-35deg] animate-pulse" style={{ animationDuration: '4s' }} />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-secondary/5 to-transparent rotate-[35deg] animate-pulse" style={{ animationDuration: '4s', animationDelay: '1s' }} />
                </div>
            </div>

            {/* Main Content */}
            <div className="relative space-y-4">
                <h1
                    className="text-4xl font-bold text-center sm:text-5xl md:text-6xl pb-1 transition-all duration-300 hover:scale-[1.02] cursor-default opacity-0 translate-y-4 animate-reveal"
                    style={{
                        background: `linear-gradient(
                            135deg,
                            hsl(var(--foreground)) 0%,
                            hsl(var(--foreground)/0.9) 25%,
                            hsl(var(--primary)/0.9) 50%,
                            hsl(var(--foreground)/0.9) 75%,
                            hsl(var(--foreground)) 100%
                        )`,
                        backgroundSize: '200% auto',
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        animation: 'gradient 8s linear infinite, reveal 1s ease-out forwards',
                    }}
                >
                    Operate your cloud infrastructure with AI Agents
                </h1>

                <p className="text-center text-muted-foreground text-lg sm:text-xl max-w-2xl mx-auto px-4 opacity-0 translate-y-4 animate-reveal-delayed">
                    Automate, optimize, and scale your cloud operations with intelligent AI agents that work 24/7
                </p>

                {/* Animated underline */}
                <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-48 h-0.5 bg-gradient-to-r from-transparent via-primary/20 to-transparent blur-sm animate-pulse opacity-0 animate-reveal-delayed-2" />
            </div>

            <style jsx global>{`
                @keyframes float {
                    0%, 100% { transform: translate(-50%, 0); }
                    50% { transform: translate(-50%, -10px); }
                }
                @keyframes gradient {
                    0% { background-position: 0% 50%; }
                    50% { background-position: 100% 50%; }
                    100% { background-position: 0% 50%; }
                }
                @keyframes reveal {
                    0% {
                        opacity: 0;
                        transform: translateY(1rem);
                    }
                    100% {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-reveal {
                    animation: reveal 1s ease-out forwards;
                }
                .animate-reveal-delayed {
                    animation: reveal 1s ease-out 0.3s forwards;
                }
                .animate-reveal-delayed-2 {
                    animation: reveal 1s ease-out 0.6s forwards;
                }
            `}</style>
        </div>
    )
}