'use client';

import { cn } from '@/lib/utils';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import React from 'react';
import { LucideIcon } from 'lucide-react';
import { highlightMentions } from '@/components/chat/utils/mention-highlighting';

interface QuickStartCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
  onClick?: () => void;
}

export function QuickStartCard({
  icon,
  title,
  description,
  className,
  onClick,
}: QuickStartCardProps) {

  const { highlightedText } = highlightMentions(description);


  return (
    <Card
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      }}
      className={cn(
        'group relative overflow-hidden transition-all duration-300',
        'hover:scale-[1.02] hover:shadow-md active:scale-[0.98]',
        'bg-gradient-to-br from-card to-card/40',
        'border border-border/50',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary',
        onClick && 'cursor-pointer',
        className
      )}
    >
      <CardHeader className="flex h-[120px] flex-col justify-between">
        <div className="flex items-center space-x-3">
          <div className="shrink-0 rounded-lg bg-primary/10 p-1.5 text-primary">
            {React.createElement(icon)}
          </div>
          <CardTitle className="truncate text-sm font-medium">
            {title}
          </CardTitle>
        </div>
        <div className="relative">
          <p
            className={cn(
              'text-sm text-muted-foreground',
              'line-clamp-2 leading-[1.4]',
              'h-[2.8em]'
            )}
            title={description}
          >
            {highlightedText}
          </p>
        </div>
      </CardHeader>
      <div
        className="pointer-events-none absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/0 opacity-0 transition-all duration-300 group-hover:opacity-100"
        aria-hidden="true"
      />
    </Card>
  );
}
