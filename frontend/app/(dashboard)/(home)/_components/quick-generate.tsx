'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Icons } from '@/components/icons';
import { cn } from '@/lib/utils';
import { TaskTemplatesService } from '@/client';
import { useToast } from '@/hooks/use-toast';
import debounce from 'lodash/debounce';
import { useRouter } from 'next/navigation';
import { useAgentConversation } from '@/lib/utils/agent-conversation';

// Import mention-related components and utilities
import { ResourceMentionDropdown } from '@/components/chat/components/resource-mention';
import { AgentMentionDropdown } from '@/components/chat/components/agent-mention';
import {
  highlightMentions,
  getIncompleteAtCursor
} from '@/components/chat/utils/mention-highlighting';
import { resourceCategories } from '@/components/chat/components/data';
import { useBuiltinConnectors } from '@/hooks/use-builtin-connectors';
import { useKnowledgeBases } from '@/components/chat/hooks/useKnowledgeBases';
import { useAgents } from '@/hooks/use-agents';
import { AutonomousAgentsService } from '@/client';

// New components for buttons
interface GenerateButtonProps {
  value: string;
  isGenerating: boolean;
  isSending: boolean;
  onGenerate: () => void;
}

const GenerateButton = ({
  value,
  isGenerating,
  isSending,
  onGenerate
}: GenerateButtonProps) => (
  <Button
    size="icon"
    variant="outline"
    disabled={!value.trim() || isGenerating || isSending}
    onClick={onGenerate}
    className="h-8 w-8 rounded-full transition-all duration-200"
    title="Create a standard task definition format based on your requirements"
  >
    {isGenerating ? (
      <Icons.spinner className="h-4 w-4 animate-spin" />
    ) : (
      <Icons.calendarCog className="h-4 w-4" />
    )}
    <span className="sr-only">Generate</span>
  </Button>
);

interface SendButtonProps {
  value: string;
  isGenerating: boolean;
  isSending: boolean;
  onSend: () => void;
}

const SendButton = ({
  value,
  isGenerating,
  isSending,
  onSend
}: SendButtonProps) => (
  <Button
    size="icon"
    className={cn(
      'h-8 w-8',
      'bg-primary/90 hover:bg-primary',
      'rounded-full transition-all duration-200',
      (!value.trim() || isSending) && 'opacity-50'
    )}
    disabled={!value.trim() || isGenerating || isSending}
    onClick={onSend}
    title="Send direct message"
  >
    {isSending ? (
      <Icons.spinner className="h-4 w-4 animate-spin" />
    ) : (
      <Icons.send className="h-4 w-4" />
    )}
    <span className="sr-only">Send</span>
  </Button>
);

interface QuickGenerateProps {
  placeholder?: string;
  messagesRemaining?: number;
  showGitHubButton?: boolean;
  onGitHubClick?: () => void;
  className?: string;
  setSelectedTask: (task: {
    title: string;
    description: string;
    category?: string;
    isGenerated: boolean;
  }) => void;
  setIsDialogOpen: (open: boolean) => void;
  isBottomInput?: boolean; // New prop to determine input position
}

// Custom placeholder component for styling mentions
const StyledPlaceholder = () => (
  <div className="pointer-events-none absolute inset-0 flex items-start px-5 pt-4 text-muted-foreground/70">
    Type <span className="mx-1 font-medium text-primary">#</span> to mention a
    tool, or document, or{' '}
    <span className="mx-1 font-medium text-primary">@</span> to mention an
    agent...
  </div>
);

// Utility function to calculate dropdown position
function calculateDropdownPosition(
  textareaRef: HTMLTextAreaElement,
  isBottomInput: boolean
): { top: number; left: number } {
  const rect = textareaRef.getBoundingClientRect();
  const scrollY = window.scrollY;
  const scrollX = window.scrollX;

  if (isBottomInput) {
    // Position above for bottom inputs
    return {
      top: scrollY + rect.top - 320, // Height of dropdown
      left: scrollX + rect.left + 50
    };
  } else {
    // Position below for top inputs
    return {
      top: scrollY + rect.bottom + 20,
      left: scrollX + rect.left + 50
    };
  }
}

export function QuickGenerate({
  placeholder = 'Ask me to analyze and optimize your current infrastructure configuration...',
  messagesRemaining = 0,
  showGitHubButton = true,
  onGitHubClick,
  className,
  setSelectedTask,
  setIsDialogOpen,
  isBottomInput = false // Default to top input
}: QuickGenerateProps) {
  const router = useRouter();
  // Original state
  const [value, setValue] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const { toast } = useToast();

  // Mention-related state
  const [showResourceMention, setShowResourceMention] = useState(false);
  const [resourceMentionPosition, setResourceMentionPosition] = useState({
    top: 0,
    left: 0
  });
  const [resourceMentionFilter, setResourceMentionFilter] = useState('');
  const [showAgentMention, setShowAgentMention] = useState(false);
  const [agentMentionPosition, setAgentMentionPosition] = useState({
    top: 0,
    left: 0
  });
  const [agentMentionFilter, setAgentMentionFilter] = useState('');
  //   const [highlightedText, setHighlightedText] = useState<React.ReactNode[]>([]);
  //   const [hasMentions, setHasMentions] = useState(false);
  const [mentionedKbIds, setMentionedKbIds] = useState<Record<string, string>>(
    {}
  );
  const [justPasted, setJustPasted] = useState(false);

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const isSubmittingRef = useRef(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // Fetch builtin tools and knowledge bases
  const { data: builtinTools, isPending: isBuiltinToolsLoading } =
    useBuiltinConnectors();
  const { kbs, isLoading: kbsLoading } = useKnowledgeBases();
  const { data: agentsResponse } = useAgents({
    limit: 1,
    refetchOnWindowFocus: false
  });

  // Prepare dynamic resource categories
  const dynamicResourceCategories = [
    {
      id: 'tools',
      name: 'Tools',
      icon: <Icons.settings className="h-5 w-5 text-purple-500" />,
      items: isBuiltinToolsLoading
        ? [
            {
              id: 'loading',
              title: 'Loading tools...',
              description: 'Please wait'
            }
          ]
        : builtinTools
            ?.filter((tool) => tool.is_active)
            .map((tool) => ({
              id: tool.id,
              title: tool.display_name || tool.name,
              description: tool.description || 'No description available'
            })) || []
    },
    ...resourceCategories.map((category) => {
      if (category.isDynamic && category.source === 'kb_collections') {
        return {
          ...category,
          items: kbsLoading
            ? [
                {
                  id: 'loading',
                  title: 'Loading collections...',
                  description: 'Please wait'
                }
              ]
            : kbs
        };
      }
      return category;
    })
  ];

  // Derived state
  const hasContent = Boolean(value?.trim());

  // Original functions - preserved exactly as they were
  const handleGenerate = async () => {
    if (!value.trim() || isGenerating) return;

    setIsGenerating(true);
    try {
      const response = await TaskTemplatesService.generate({
        input: value.trim()
      });

      const formattedMessage = response.context;

      setSelectedTask({
        title: response.task,
        description: formattedMessage,
        category: response.category,
        isGenerated: true
      });
      setIsDialogOpen(true);

      setValue('');
      toast({
        title: 'Task generated successfully',
        description: 'Your task has been generated and is ready to configure.'
      });
    } catch (error) {
      console.error('Generation error:', error);
      toast({
        variant: 'destructive',
        title: 'Generation failed',
        description:
          'There was an error generating your task. Please try again.'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const { createAgentConversation } = useAgentConversation();

  const handleDirectMessage = async () => {
    if (!value.trim() || isSending) return;

    setIsSending(true);
    try {
      await createAgentConversation({
        message: value.trim(),
        onSuccess: () => {
          setValue('');
        },
        onError: (error) => {
          console.error('Direct message error:', error);
        }
      });
    } finally {
      setIsSending(false);
    }
  };

  // Update handleKeyDown to handle mention navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Let the dropdowns handle their own keyboard events
    if (showResourceMention || showAgentMention) {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowResourceMention(false);
        setShowAgentMention(false);
        return;
      }
      // Don't handle other keys when dropdowns are open
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isGenerating && !isSending && value.trim()) {
        handleDirectMessage();
      }
    }

    // Handle # and @ key presses to trigger mentions
    if (e.key === '#' || e.key === '@') {
      const cursorPos = (e.currentTarget as HTMLTextAreaElement).selectionStart;
      const textBeforeCursor = value.substring(0, cursorPos);

      // Check if the character before is a space or start of line
      const lastChar = textBeforeCursor.slice(-1);
      if (!lastChar || lastChar === ' ' || lastChar === '\n') {
        // Let the character be typed, checkForMentions will handle showing the dropdown
        return;
      }
    }
  };

  // Add effect to handle global keyboard events for dropdowns
  useEffect(() => {
    if (!showResourceMention && !showAgentMention) return;

    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowResourceMention(false);
        setShowAgentMention(false);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [showResourceMention, showAgentMention]);

  // Add effect to handle clicks outside dropdowns
  useEffect(() => {
    if (!showResourceMention && !showAgentMention) return;

    const handleClickOutside = (e: MouseEvent) => {
      // Check if click is outside both textarea AND dropdown
      if (
        textareaRef.current &&
        !textareaRef.current.contains(e.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setShowResourceMention(false);
        setShowAgentMention(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showResourceMention, showAgentMention]);

  // Add effect to update mention position on window resize
  useEffect(() => {
    if (!showResourceMention && !showAgentMention) return;

    const handleResize = () => {
      if (textareaRef.current) {
        const position = calculateDropdownPosition(
          textareaRef.current,
          isBottomInput
        );
        if (showResourceMention) {
          setResourceMentionPosition(position);
        }
        if (showAgentMention) {
          setAgentMentionPosition(position);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [showResourceMention, showAgentMention, isBottomInput]);

  const { highlightedText, hasMentions } = highlightMentions(value);

  // Add handlers for mention selection
  const handleResourceSelect = (itemId: string, fullPath: string) => {
    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = value.substring(0, cursorPos);
      const textAfterCursor = value.substring(cursorPos);

      const mentionInfo = getIncompleteAtCursor(value, cursorPos);

      if (mentionInfo && mentionInfo.type === 'resource') {
        const cleanPath = fullPath.replace(/\//g, '/').toLowerCase();
        const newText =
          textBeforeCursor.substring(0, mentionInfo.startIndex) +
          `#${cleanPath}` +
          ' ' +
          textAfterCursor;

        setMentionedKbIds((prev) => ({
          ...prev,
          [`#${cleanPath}`]: itemId
        }));

        setValue(newText);
        setShowResourceMention(false);

        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos =
              mentionInfo.startIndex + `#${cleanPath} `.length;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  };

  const handleAgentSelect = (agentId: string, agentName: string) => {
    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = value.substring(0, cursorPos);
      const textAfterCursor = value.substring(cursorPos);

      const mentionInfo = getIncompleteAtCursor(value, cursorPos);

      if (mentionInfo && mentionInfo.type === 'agent') {
        const displayName = agentName.split(' (')[0];
        const newText =
          textBeforeCursor.substring(0, mentionInfo.startIndex) +
          `@${displayName}` +
          ' ' +
          textAfterCursor;

        setValue(newText);
        setShowAgentMention(false);

        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos =
              mentionInfo.startIndex + `@${displayName} `.length;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  };

  // Add new methods for handling mentions
  const checkForMentions = (text: string, cursorPos: number) => {
    const mentionInfo = getIncompleteAtCursor(text, cursorPos);

    if (!mentionInfo) {
      setShowResourceMention(false);
      setShowAgentMention(false);
      return;
    }

    if (mentionInfo.type === 'resource' && textareaRef.current) {
      setResourceMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateDropdownPosition(
        textareaRef.current,
        isBottomInput
      );
      setResourceMentionPosition(position);
      setShowResourceMention(true);
      setShowAgentMention(false);
    } else if (mentionInfo.type === 'agent' && textareaRef.current) {
      setAgentMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateDropdownPosition(
        textareaRef.current,
        isBottomInput
      );
      setAgentMentionPosition(position);
      setShowAgentMention(true);
      setShowResourceMention(false);
    }
  };

  // Update handleChange to use debounced resize
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (isSubmittingRef.current) return;

    const newValue = e.target.value;
    setValue(newValue);
    checkForMentions(newValue, e.target.selectionStart);
  };

  // Add syncScroll for overlay synchronization
  const syncScroll = (e: React.UIEvent<HTMLTextAreaElement>) => {
    if (overlayRef.current && textareaRef.current) {
      // Use RAF for smooth scrolling without flickering
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
    }
  };

  // UI Components styled like MessageInput
  const renderGitHubButton = () => {
    if (!showGitHubButton) return null;

    return (
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={onGitHubClick}
        className="h-8 w-8 rounded-full text-muted-foreground transition-all duration-200 hover:bg-muted/70 hover:text-foreground active:scale-95 dark:hover:bg-muted/30"
      >
        <Icons.gitHub className="h-4 w-4" />
      </Button>
    );
  };

  const renderMessagesRemaining = () => {
    if (!messagesRemaining) return null;

    return (
      <div
        className={cn(
          'rounded-full px-2.5 py-0.5 text-xs font-medium text-white shadow-sm',
          'bg-blue-400'
        )}
      >
        {messagesRemaining} messages remaining
      </div>
    );
  };

  return (
    <div className={className}>
      <div className="mx-auto max-w-[1000px] px-4 pb-2 pt-4">
        <div className="overflow-hidden rounded-3xl border-2 border-primary/10 bg-muted/30 shadow-none transition-all duration-300 hover:border-primary/40 hover:border-2 dark:bg-muted/10">
          {/* Resource Mention Dropdown */}
          <ResourceMentionDropdown
            isVisible={showResourceMention}
            position={resourceMentionPosition}
            filter={resourceMentionFilter}
            categories={dynamicResourceCategories}
            onSelect={handleResourceSelect}
            onClose={() => setShowResourceMention(false)}
            dropdownRef={dropdownRef}
          />

          {/* Agent Mention Dropdown */}
          <AgentMentionDropdown
            isVisible={showAgentMention}
            position={agentMentionPosition}
            filter={agentMentionFilter}
            onSelect={handleAgentSelect}
            onClose={() => setShowAgentMention(false)}
          />

          {/* Input Area */}
          <div ref={containerRef} className="relative">
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onPaste={() => {
                if (overlayRef.current) {
                  console.log('overlayRef.current paste', overlayRef.current);
                  overlayRef.current.scrollTo({
                    top: 100,
                    behavior: 'smooth'
                  });
                }
              }}
              onScroll={syncScroll}
              placeholder=""
              disabled={isGenerating || isSending}
              className={cn(
                'w-full',
                'max-h-[150px] min-h-[150px]',
                'custom-scrollbar resize-none overflow-y-auto',
                'px-4 py-4',
                'border-0 hover:shadow-none focus-visible:ring-0',
                'bg-transparent',
                'text-base',
                'transition-[height] duration-100 ease-out', // Add smooth height transition
                hasMentions
                  ? 'text-transparent caret-foreground selection:bg-primary/20'
                  : 'caret-foreground',
                'placeholder:opacity-0',
                '!border-none !shadow-none'
              )}
            />

            {/* Highlighted Text Overlay */}
            <div
              ref={overlayRef}
              className={cn(
                'custom-scrollbar pointer-events-none absolute inset-0 h-full w-full resize-none overflow-y-auto whitespace-pre-wrap break-words px-4 py-4 text-base transition-[height] duration-100 ease-out',
                { invisible: !hasMentions }
              )}
            >
              {highlightedText}
            </div>

            {/* Placeholder */}
            {!value && <StyledPlaceholder />}
          </div>

          {/* Bottom Toolbar */}
          <div className="flex items-center gap-2 px-4 py-1">
            <div className="flex items-center gap-2">
              {renderGitHubButton()}
            </div>

            <div className="flex-1" />

            {/* Messages Remaining */}
            {renderMessagesRemaining()}

            {/* Generate Button */}
            <GenerateButton
              value={value}
              isGenerating={isGenerating}
              isSending={isSending}
              onGenerate={() => {
                if (value.trim()) {
                  setSelectedTask({
                    title: value.split('\n')[0] || 'New Task',
                    description: value.trim(),
                    category: 'Direct Message',
                    isGenerated: true
                  });
                  setIsDialogOpen(true);
                  setValue('');
                }
              }}
            />

            {/* Send Button */}
            <SendButton
              value={value}
              isGenerating={isGenerating}
              isSending={isSending}
              onSend={handleDirectMessage}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
