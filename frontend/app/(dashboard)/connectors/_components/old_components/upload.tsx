import { useUploadFile } from "@/hooks/use-upload-file";

export default function UploadComponent() {
    const { uploadFile, cancelUpload, status, progress, error, fileData } = useUploadFile();

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            await uploadFile(file);
        }
    };

    return (
        <div>
            <input type="file" onChange={handleFileChange} />

            <div>
                <p>Status: {status}</p>
                <p>Progress: {progress}%</p>

                {fileData && (
                    <div>
                        <p>File Name: {fileData.filename}</p>
                        <p>Size: {fileData.file_size}</p>
                        <p>Type: {fileData.file_type}</p>
                        <p>Upload ID: {fileData.id}</p>
                        <p>Created: {new Date(fileData.created_at).toLocaleString()}</p>
                    </div>
                )}

                {error && (
                    <div className="error">
                        Error: {error.message}
                    </div>
                )}

                {(status === 'pending' || status === 'in_progress') && (
                    <button onClick={cancelUpload}>Cancel Upload</button>
                )}
            </div>
        </div>
    );
}
