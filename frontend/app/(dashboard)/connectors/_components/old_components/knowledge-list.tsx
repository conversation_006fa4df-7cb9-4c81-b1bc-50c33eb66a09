// import { useState, useEffect } from 'react';
// import { useQuery } from '@tanstack/react-query';
// import { ConnectorsService, ConnectorResponse } from '@/client';
// import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
// import ConnectorCard from './connector-card';
// import { useRouter } from 'next/navigation';
// import { Skeleton } from '@/components/ui/skeleton';
// import { Badge } from '@/components/ui/badge';
// import { Loader2 } from 'lucide-react';

// type KnowledgeListProps = {
//   page?: number;
//   limit?: number;
// };

// function getKnowledgeQueryOptions({
//   page,
//   limit
// }: {
//   page: number;
//   limit: number;
// }) {
//   return {
//     queryFn: () =>
//       ConnectorsService.listConnectors({
//         skip: (page - 1) * limit,
//         limit
//       }),
//     queryKey: [CacheKey.Items, { page, limit }]
//   };
// }

// // Summary component to display knowledge counts
// interface KnowledgeSummaryProps {
//   count: number;
//   isLoading: boolean;
// }

// const KnowledgeSummary = ({ count, isLoading }: KnowledgeSummaryProps) => {
//   if (isLoading) {
//     return (
//       <div className="flex items-center gap-2 text-muted-foreground text-sm">
//         <Loader2 className="h-4 w-4 animate-spin" />
//         <span>Loading knowledge sources...</span>
//       </div>
//     );
//   }

//   return (
//     <div className="space-y-3 mt-2 mb-3">
//       <div className="flex flex-wrap items-center gap-3">
//         <Badge variant="outline" className="bg-slate-50 hover:bg-slate-50 dark:bg-slate-800 dark:text-slate-300 dark:hover:bg-slate-800 dark:border-slate-700 px-3 py-1">
//           {count} Total
//         </Badge>
//       </div>
//     </div>
//   );
// };

// export default function KnowledgeList({
//   page = 1,
//   limit = 10
// }: KnowledgeListProps) {
//   const router = useRouter();

//   const {
//     data: knowledgeSources,
//     isPending,
//     isPlaceholderData
//   } = useQuery({
//     ...getKnowledgeQueryOptions({
//       page,
//       limit
//     }),
//     placeholderData: (prevData) => prevData
//   });

//   const handleViewKnowledge = (knowledge: ConnectorResponse) => {
//     if (knowledge.id) {
//       router.push(`/connectors/knowledge/${knowledge.id}`);
//     }
//   };

//   // Card grid layout
//   const cardGridStyle = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4";

//   return (
//     <div className="space-y-8">
//       <KnowledgeSummary
//         count={knowledgeSources?.data?.length || 0}
//         isLoading={isPending}
//       />

//       {/* Knowledge Sources */}
//       <div>
//         <h3 className="text-lg font-medium mb-4">Knowledge Sources</h3>
//         {isPending ? (
//           <div className={cardGridStyle}>
//             {Array.from({ length: 3 }).map((_, i) => (
//               <div key={i} className="border rounded-lg p-4">
//                 <Skeleton className="h-6 w-24 mb-2" />
//                 <Skeleton className="h-4 w-full mb-4" />
//                 <div className="flex justify-end">
//                   <Skeleton className="h-8 w-16" />
//                 </div>
//               </div>
//             ))}
//           </div>
//         ) : knowledgeSources?.data && knowledgeSources.data.length > 0 ? (
//           <div className={cardGridStyle}>
//             {knowledgeSources.data.map((knowledge) => (
//               <ConnectorCard
//                 key={knowledge.id}
//                 connector={knowledge}
//                 isBuiltin={false}
//                 onView={handleViewKnowledge}
//               />
//             ))}
//           </div>
//         ) : (
//           <div className="text-center py-8 border rounded-lg bg-gray-50 dark:bg-gray-800/50">
//             <p className="text-muted-foreground">No knowledge sources found</p>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }
