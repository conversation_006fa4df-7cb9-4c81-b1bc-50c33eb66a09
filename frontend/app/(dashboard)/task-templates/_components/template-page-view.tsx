'use client';

import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';
import TemplateListing from './template-listing';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { TemplateDialog } from './template-dialog';

interface TemplatePageViewProps {
  searchParams: {
    page?: string;
    limit?: string;
    q?: string;
    category?: string;
    service?: string;
    include_defaults?: string;
  };
}

export function TemplatePageView({ searchParams }: TemplatePageViewProps) {
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <PageContainer scrollable={false}>
      <div className="flex h-[calc(100vh-4rem)] flex-col">
        <div className="flex-none">
          <PageHeader
            title="Task Templates"
            description="Manage task templates for automated cloud cost optimization"
            actions={
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Template
              </Button>
            }
          />
          <Separator className="my-6" />
        </div>

        <div className="min-h-0 flex-1">
          <TemplateListing
            page={searchParams.page ? parseInt(searchParams.page) : undefined}
            limit={searchParams.limit ? parseInt(searchParams.limit) : undefined}
            q={searchParams.q}
            category={searchParams.category}
            service={searchParams.service}
            include_defaults={
              searchParams.include_defaults !== undefined
                ? searchParams.include_defaults === 'true'
                : undefined
            }
          />
        </div>

        <TemplateDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          onSuccess={() => setDialogOpen(false)}
        />
      </div>
    </PageContainer>
  );
} 