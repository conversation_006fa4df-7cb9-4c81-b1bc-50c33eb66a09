'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { TaskCategoryEnum, TaskServiceEnum, RunModeEnum } from '@/client';
import { type TaskTemplate } from '@/types/task-template';
import { type TaskTemplateUpdate } from '@/client';
import { useTemplateMutations } from '../_hooks/use-template-mutations';
import { getCategoryLabel, getServiceLabel, getRunModeLabel, getCategoryIcon, getServiceIcon } from '@/utils/task-labels';
import { TaskCouldEnumSchema, TaskCategoryEnumSchema, TaskServiceEnumSchema } from '@/client/schemas.gen';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { UpdateTemplateData } from '@/client';
import { CronInput } from '@/components/ui/cron-input';

const RUN_MODE = {
  AGENT: 'agent',
  AUTONOMOUS: 'autonomous'
} as const;

const formSchema = z.object({
  task: z.string().min(1, 'Task name is required'),
  category: z.enum(TaskCategoryEnumSchema.enum).default('OTHER'),
  service: z.enum(TaskServiceEnumSchema.enum).default('OTHER'),
  run_mode: z.enum([RUN_MODE.AGENT, RUN_MODE.AUTONOMOUS]).default(RUN_MODE.AGENT),
  schedule: z.string().optional(),
  context: z.string().min(1, 'Context is required'),
  service_name: z.string().min(1, 'Service name is required'),
  cloud: z.enum(TaskCouldEnumSchema.enum).default('AWS')
});

export type FormData = z.infer<typeof formSchema>;

interface TemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template?: TaskTemplate;
  onSuccess?: () => void;
}

export function TemplateDialog({
  open,
  onOpenChange,
  template,
  onSuccess
}: TemplateDialogProps) {
  const isEditing = !!template;
  const { updateTemplate, createTemplate, isUpdating, isCreating } =
    useTemplateMutations();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      task: '',
      category: 'OTHER',
      service: 'OTHER',
      run_mode: RUN_MODE.AGENT,
      schedule: '',
      context: '',
      service_name: '',
      cloud: 'AWS'
    }
  });

  // Reset form values when template changes
  useEffect(() => {
    if (template) {
      form.reset({
        task: template.task,
        category: template.category,
        service: template.service,
        run_mode: template.run_mode,
        schedule: template.schedule ?? '',
        context: template.context,
        service_name: template.service_name,
        cloud: template.cloud
      });
    } else {
      form.reset({
        task: '',
        category: 'OTHER',
        service: 'OTHER',
        run_mode: RUN_MODE.AGENT,
        schedule: '',
        context: '',
        service_name: '',
        cloud: 'AWS'
      });
    }
  }, [template, form]);

  const onSubmit = (data: FormData) => {
    try {
      if (isEditing && template) {
        const updateData: TaskTemplateUpdate = {
          task: data.task,
          category: data.category,
          service: data.service,
          run_mode: data.run_mode,
          schedule: data.schedule,
          context: data.context
        };
        updateTemplate({ templateId: template.id, data: updateData });
      } else {
        createTemplate({
          requestBody: {
            ...data,
            service_name: data.service,
            cloud: "ALL"
          }
        });
        form.reset();
      }
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save template:', error);
    }
  };

  const isSubmitting = isUpdating || isCreating;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Template' : 'Create Template'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the task template details below.'
              : 'Fill in the details to create a new task template.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="task"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Task Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter task name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TaskCategoryEnumSchema.enum.map((category) => {
                          const CategoryIcon = getCategoryIcon(category);
                          return (
                            <SelectItem key={category} value={category}>
                              <div className="flex items-center gap-2">
                                <CategoryIcon className="h-4 w-4" />
                                {getCategoryLabel(category)}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="service"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select service" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TaskServiceEnumSchema.enum.map((service) => {
                          const ServiceIcon = getServiceIcon(service);
                          return (
                            <SelectItem key={service} value={service}>
                              <div className="flex items-center gap-2">
                                <ServiceIcon className="h-4 w-4" />
                                {getServiceLabel(service)}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="run_mode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Run Mode</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select run mode" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(RUN_MODE).map((mode) => (
                          <SelectItem key={mode} value={mode}>
                            {getRunModeLabel(mode)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="schedule"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Schedule (Cron)</FormLabel>
                    <FormControl>
                      <CronInput
                        value={field.value ?? ''}
                        onChange={field.onChange}
                        onValidityChange={(isValid) => {
                          if (!isValid) {
                            form.setError('schedule', {
                              type: 'manual',
                              message: 'Invalid cron expression'
                            });
                          } else {
                            form.clearErrors('schedule');
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="context"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Context</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter task context"
                      className="min-h-80"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="service_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter service name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cloud"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cloud</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select cloud" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(TaskCouldEnumSchema.enum).map((cloud) => (
                        <SelectItem key={cloud} value={cloud}>
                          {cloud}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? isEditing
                    ? 'Updating...'
                    : 'Creating...'
                  : isEditing
                  ? 'Update'
                  : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
