'use client';

import { useCallback } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { TaskCategory, TaskService } from '@/constants/task';
import { parseAsBoolean, parseAsInteger, parseAsString, useQueryState } from 'nuqs';

export function useTaskTemplateFilters() {
  const router = useRouter();
  const pathname = usePathname();

  const [categoryFilter, setCategoryFilter] = useQueryState(
    'category',
    parseAsString.withOptions({ shallow: false }).withDefault('')
  );

  const [serviceFilter, setServiceFilter] = useQueryState(
    'service',
    parseAsString.withOptions({ shallow: false }).withDefault('')
  );

  const [includeDefaults, setIncludeDefaults] = useQueryState(
    'include_defaults',
    parseAsBoolean.withOptions({ shallow: false }).withDefault(true)
  );

  const [searchQuery, setSearchQuery] = useQueryState(
    'search_query',
    parseAsString.withOptions({ shallow: false }).withDefault('')
  );

  const [page, setPage] = useQueryState(
    'page',
    parseAsInteger.withOptions({ shallow: false }).withDefault(1)
  );

  const resetFilters = useCallback(() => {
    router.push(pathname, { scroll: false });
    setCategoryFilter('');
    setServiceFilter('');
    setIncludeDefaults(true);
    setSearchQuery('');
    setPage(1);
  }, [pathname, router, setCategoryFilter, setServiceFilter, setIncludeDefaults, setSearchQuery, setPage]);

  const isAnyFilterActive =
    !!categoryFilter ||
    !!serviceFilter ||
    !includeDefaults ||
    !!searchQuery;

  // Convert string values to enums for API calls
  const getCategoryEnum = (): TaskCategory | undefined => {
    return categoryFilter ? (categoryFilter as TaskCategory) : undefined;
  };

  const getServiceEnum = (): TaskService | undefined => {
    return serviceFilter ? (serviceFilter as TaskService) : undefined;
  };

  return {
    categoryFilter,
    setCategoryFilter,
    serviceFilter,
    setServiceFilter,
    includeDefaults,
    setIncludeDefaults,
    searchQuery,
    setSearchQuery,
    page,
    setPage,
    isAnyFilterActive,
    resetFilters,
    getCategoryEnum,
    getServiceEnum,
  };
} 