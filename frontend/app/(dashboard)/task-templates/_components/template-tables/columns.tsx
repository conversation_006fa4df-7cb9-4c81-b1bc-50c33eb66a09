'use client';

import { Badge } from '@/components/ui/badge';
import { type TaskTemplate } from '@/types/task-template';
import { type ColumnDef } from '@tanstack/react-table';
import { CellAction } from './cell-action';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import cronstrue from 'cronstrue';
import { getCategoryLabel, getServiceLabel, getRunModeLabel, getCategoryIcon, getServiceIcon } from '@/utils/task-labels';

type ColumnsProps = {
  onEdit: (template: TaskTemplate) => void;
};

export const getColumns = ({ onEdit }: ColumnsProps): ColumnDef<TaskTemplate>[] => [
  {
    accessorKey: 'task',
    header: 'Task',
    cell: ({ row }) => (
      <div className="font-medium">{row.original.task}</div>
    ),
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: ({ row }) => {
      const CategoryIcon = getCategoryIcon(row.original.category);
      return (
        <Badge variant="outline" className="p-2">
          <CategoryIcon size={16} className="mr-2" />
          {getCategoryLabel(row.original.category)}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'service',
    header: 'Service',
    cell: ({ row }) => {
      const ServiceIcon = getServiceIcon(row.original.service);
      return (
        <Badge variant="outline" className="p-2">
          <ServiceIcon size={16} className="mr-2" />
          {getServiceLabel(row.original.service)}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'run_mode',
    header: 'Run Mode',
    cell: ({ row }) => (
      <Badge
        variant={row.original.run_mode === 'autonomous' ? 'default' : 'secondary'}
      >
        {getRunModeLabel(row.original.run_mode)}
      </Badge>
    ),
  },
  {
    accessorKey: 'schedule',
    header: 'Schedule',
    cell: ({ row }) => {
      const schedule = row.original.schedule;
      const humanReadable = schedule ? cronstrue.toString(schedule, { verbose: true }) : 'No schedule';

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="text-muted-foreground cursor-help">
                {schedule || 'No schedule'}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{humanReadable}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <div className="flex justify-end"  >
      <CellAction data={row.original} onEdit={onEdit} />
    </div>,
  },
];
