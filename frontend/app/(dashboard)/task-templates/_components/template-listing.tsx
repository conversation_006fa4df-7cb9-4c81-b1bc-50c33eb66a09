'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { TaskTemplatesService } from '@/client';
import { TaskCategory, TaskService } from '@/constants/task';
import { type TaskTemplate } from '@/types/task-template';
import { DataTable } from '@/components/ui/table/data-table';
import { DataTableFilterBox } from '@/components/ui/table/data-table-filter-box';
import { DataTableResetFilter } from '@/components/ui/table/data-table-reset-filter';
import { DataTableSearch } from '@/components/ui/table/data-table-search';
import { CacheKey } from '@/components/utils/cache-key';
import { useTaskTemplateFilters } from './use-task-template-filters';
import { getColumns } from './template-tables/columns';
import { TemplateDialog } from './template-dialog';
import { useState } from 'react';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { getCategoryLabel, getServiceLabel, getCategoryIcon, getServiceIcon } from '@/utils/task-labels';


type TemplateListingProps = Readonly<{
  page?: number;
  limit?: number;
  q?: string | null;
  category?: string | null;
  service?: string | null;
  include_defaults?: boolean | null;
}>;

function getTemplatesQueryOptions({
  page,
  limit,
  search_query,
  category,
  service,
  include_defaults,
}: {
  page: number;
  limit: number;
  search_query: string | null;
  category: string | null;
  service: string | null;
  include_defaults: boolean | null;
}) {
  return {
    queryFn: () =>
      TaskTemplatesService.listTemplates({
        skip: (page - 1) * limit,
        limit,
        ...(search_query && { searchQuery: search_query }),
        ...(category && { category: category as TaskCategory }),
        ...(service && { service: service as TaskService }),
        ...(include_defaults !== null && { includeDefaults: include_defaults }),
      }),
    queryKey: [CacheKey.TaskTemplates, page, limit, search_query, category, service, include_defaults],
  };
}

export default function TemplateListing({
  page = 1,
  limit = 10,
  q = null,
  category = null,
  service = null,
  include_defaults = true,
}: TemplateListingProps) {
  const {
    categoryFilter,
    setCategoryFilter,
    serviceFilter,
    setServiceFilter,
    includeDefaults,
    setIncludeDefaults,
    isAnyFilterActive,
    resetFilters,
    searchQuery,
    setSearchQuery,
    setPage,
    getCategoryEnum,
    getServiceEnum,
  } = useTaskTemplateFilters();

  const [dialogState, setDialogState] = useState<{
    open: boolean;
    template?: TaskTemplate;
  }>({
    open: false,
  });

  const queryClient = useQueryClient();

  const {
    data: items,
    isPending,
    isPlaceholderData,
  } = useQuery({
    ...getTemplatesQueryOptions({
      page,
      limit,
      search_query: searchQuery || null,
      category: getCategoryEnum() || null,
      service: getServiceEnum() || null,
      include_defaults: includeDefaults,
    }),
    placeholderData: (prevData) => prevData,
  });

  const hasNextPage = !isPlaceholderData && items?.data.length === limit;

  const handleDelete = async (template: TaskTemplate) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      try {
        await TaskTemplatesService.deleteTemplate({ templateId: template.id });
        queryClient.invalidateQueries({ queryKey: [CacheKey.TaskTemplates] });
        toast.success('Template deleted successfully');
      } catch (error) {
        console.error('Failed to delete template:', error);
        toast.error('Failed to delete template');
      }
    }
  };

  const handleEdit = (template: TaskTemplate) => {
    setDialogState({ open: true, template });
  };

  const handleCreate = () => {
    setDialogState({ open: true });
  };

  const handleDialogSuccess = () => {
    queryClient.invalidateQueries({ queryKey: [CacheKey.TaskTemplates] });
    toast.success(
      dialogState.template
        ? 'Template updated successfully'
        : 'Template created successfully'
    );
  };

  const categoryOptions = Object.values(TaskCategory).map((category) => ({
    value: category,
    label: getCategoryLabel(category),
    icon: getCategoryIcon(category),
  }));

  const serviceOptions = Object.values(TaskService).map((service) => ({
    value: service,
    label: getServiceLabel(service),
    icon: getServiceIcon(service),
  }));

  const columns = getColumns({ onEdit: handleEdit });

  return (
    <div className="flex flex-col h-full">
      <div className="flex-none space-y-4 mb-4">
        <div className="flex flex-wrap items-center gap-4">
          <DataTableSearch
            searchKey="template by name"
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            setPage={setPage}
          />
          <DataTableFilterBox
            filterKey="category"
            title="Category"
            options={categoryOptions}
            setFilterValue={setCategoryFilter}
            filterValue={categoryFilter}
          />
          <DataTableFilterBox
            filterKey="service"
            title="Service"
            options={serviceOptions}
            setFilterValue={setServiceFilter}
            filterValue={serviceFilter}
          />
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-defaults"
              checked={includeDefaults}
              onCheckedChange={(checked) => setIncludeDefaults(checked === 'indeterminate' ? null : checked)}
            />
            <label htmlFor="include-defaults" className="text-sm">
              Include Default Templates
            </label>
          </div>
          <DataTableResetFilter
            isFilterActive={isAnyFilterActive}
            onReset={resetFilters}
          />
        </div>
      </div>

      <div className="flex-1 min-h-0">
        <DataTable
          columns={columns}
          data={(items?.data ?? []).filter(item => Object.values(TaskCategory).includes(item.category as TaskCategory)) as TaskTemplate[]}
          totalItems={items?.total ?? 0}
          loading={isPending}
          customScrollClass="grid h-[calc(100vh-300px)] rounded-xl border"
          enableSorting={true}
          currentPage={page}
          pageSize={limit}
          onPageChange={setPage}
          displayPagination={true}
        />
      </div>

      <TemplateDialog
        open={dialogState.open}
        onOpenChange={(open) => setDialogState({ open })}
        template={dialogState.template}
        onSuccess={handleDialogSuccess}
      />
    </div>
  );
}
