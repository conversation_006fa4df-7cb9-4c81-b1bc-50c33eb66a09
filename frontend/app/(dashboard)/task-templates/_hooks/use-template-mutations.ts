import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CreateTemplateData, TaskTemplatesService, UpdateTemplateData } from '@/client';
import { type TaskTemplate } from '@/types/task-template';
import { CacheKey } from '@/components/utils/cache-key';
import { toast } from 'sonner';

export const useTemplateMutations = () => {
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: async (templateId: string) => {
      await TaskTemplatesService.deleteTemplate({ templateId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.TaskTemplates] });
      toast.success('Template deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete template:', error);
      toast.error('Failed to delete template');
    },
  });

  const updateMutation = useMutation({
    mutationFn: async ({ templateId, data }: { templateId: string; data: UpdateTemplateData['requestBody'] }) => {
      await TaskTemplatesService.updateTemplate({
        templateId,
        requestBody: data
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.TaskTemplates] });
      toast.success('Template updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update template:', error);
      toast.error('Failed to update template');
    },
  });

  const createMutation = useMutation({
    mutationFn: async (data: CreateTemplateData) => {
      await TaskTemplatesService.createTemplate(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.TaskTemplates] });
      toast.success('Template created successfully');
    },
    onError: (error) => {
      console.error('Failed to create template:', error);
      toast.error('Failed to create template');
    },
  });

  return {
    deleteTemplate: deleteMutation.mutate,
    updateTemplate: updateMutation.mutate,
    createTemplate: createMutation.mutate,
    isDeleting: deleteMutation.isPending,
    isUpdating: updateMutation.isPending,
    isCreating: createMutation.isPending,
  };
}; 