import { searchParamsCache } from '@/lib/searchparams';
import { TemplatePageView } from './_components/template-page-view';

export const metadata = {
  title: 'Task Templates',
};

type PageProps = {
  searchParams: {
    page: string;
    limit: string;
    q: string;
    category: string;
    service: string;
    include_defaults: string;
  };
};

export default async function Page({ searchParams }: PageProps) {
  searchParamsCache.parse(searchParams);

  return <TemplatePageView searchParams={searchParams} />;
} 