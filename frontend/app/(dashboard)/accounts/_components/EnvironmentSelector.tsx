import React from 'react';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { UseFormReturn } from 'react-hook-form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { Zap, Shield, Code, Server } from 'lucide-react';

interface EnvironmentSelectorProps {
  form: UseFormReturn<any>;
  name: string;
  label?: React.ReactNode;
}

const environments = [
  {
    value: 'development',
    label: 'Development',
    description: 'For testing and development workloads',
    icon: <Code className="h-5 w-5" />,
    color: 'bg-blue-50 border-blue-200 hover:border-blue-300 text-blue-900 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-100',
    selectedColor: 'border-blue-500 ring-blue-500/20 bg-blue-100 dark:bg-blue-900'
  },
  {
    value: 'production',
    label: 'Production',
    description: 'For live, customer-facing workloads',
    icon: <Shield className="h-5 w-5" />,
    color: 'bg-red-50 border-red-200 hover:border-red-300 text-red-900 dark:bg-red-950 dark:border-red-800 dark:text-red-100',
    selectedColor: 'border-red-500 ring-red-500/20 bg-red-100 dark:bg-red-900'
  }
];

export const EnvironmentSelector: React.FC<EnvironmentSelectorProps> = ({
  form,
  name,
  label
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-4">
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              value={field.value}
              className="grid grid-cols-1 gap-4 sm:grid-cols-2"
            >
              {environments.map((env) => (
                <FormItem
                  key={env.value}
                  className={cn(
                    "relative flex flex-col space-y-0 p-4 rounded-3xl border transition-all cursor-pointer hover:shadow-md",
                    env.color,
                    field.value === env.value
                      ? `${env.selectedColor} ring-2`
                      : ""
                  )}
                >
                  <FormControl>
                    <RadioGroupItem
                      value={env.value}
                      className="sr-only"
                    />
                  </FormControl>
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      {env.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <FormLabel className={cn(
                        "text-sm font-semibold cursor-pointer block"
                      )}>
                        {env.label}
                      </FormLabel>
                      <p className="text-xs opacity-75 mt-1">
                        {env.description}
                      </p>
                    </div>
                    {field.value === env.value && (
                      <div className="absolute top-2 right-2">
                        <div className="w-3 h-3 bg-current rounded-full opacity-80"></div>
                      </div>
                    )}
                  </div>
                </FormItem>
              ))}
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
