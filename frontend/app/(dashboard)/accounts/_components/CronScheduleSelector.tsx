import React from 'react';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { UseFormReturn } from 'react-hook-form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { Clock, Zap, RotateCcw, Calendar, Settings } from 'lucide-react';
import cronstrue from 'cronstrue';

interface CronScheduleSelectorProps {
  form: UseFormReturn<any>;
  name: string;
  cronPatternList: string[];
  label?: React.ReactNode;
  customOptionName: string;
}

// Icon mapping for different schedule types
const getScheduleIcon = (cronPattern: string) => {
  if (cronPattern.includes('30')) return <Zap className="h-5 w-5" />; // Every 30 minutes
  if (cronPattern.includes('0 *')) return <RotateCcw className="h-5 w-5" />; // Every hour
  if (cronPattern.includes('0 0')) return <Calendar className="h-5 w-5" />; // Daily
  if (cronPattern.includes('0 12')) return <Clock className="h-5 w-5" />; // At 12:00
  return <Settings className="h-5 w-5" />; // Default
};

// Get color for different schedule types
const getScheduleColor = (cronPattern: string) => {
  if (cronPattern.includes('30')) return 'text-red-600'; // Frequent
  if (cronPattern.includes('0 *')) return 'text-orange-600'; // Hourly
  if (cronPattern.includes('0 0')) return 'text-green-600'; // Daily
  if (cronPattern.includes('0 12')) return 'text-blue-600'; // Scheduled
  return 'text-gray-600'; // Default
};

// Get description for schedule
const getScheduleDescription = (cronPattern: string) => {
  const descriptions: Record<string, string> = {
    '*/30 * * * *': 'High frequency monitoring - Resource intensive',
    '0 * * * *': 'Balanced resource usage - Recommended',
    '0 */2 * * *': 'Moderate monitoring - Good balance',
    '0 0 * * *': 'Light resource usage - Cost effective',
    '0 0 * * 0': 'Weekly comprehensive scan - Minimal cost',
    '0 12 * * *': 'Daily at noon - Business hours',
    '0 0 1 * *': 'Monthly report - Long term trends'
  };

  return descriptions[cronPattern] || 'Custom schedule';
};

// Get frequency badge for schedule
const getFrequencyBadge = (cronPattern: string) => {
  if (cronPattern.includes('*/30') || cronPattern.includes('*/15')) return 'Very High';
  if (cronPattern.includes('0 *') && !cronPattern.includes('*/2')) return 'High';
  if (cronPattern.includes('*/2') || cronPattern.includes('*/4')) return 'Medium';
  if (cronPattern.includes('0 0') && !cronPattern.includes('0 0 *')) return 'Low';
  if (cronPattern.includes('0 0 *') || cronPattern.includes('* * 0')) return 'Very Low';
  return 'Custom';
};

// Get frequency color
const getFrequencyColor = (frequency: string) => {
  switch (frequency) {
    case 'Very High': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'High': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
    case 'Medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'Low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'Very Low': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    default: return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
  }
};

const safeParseCronPattern = (pattern: string) => {
  try {
    return cronstrue.toString(pattern);
  } catch (error) {
    return '';
  }
};

export const CronScheduleSelector: React.FC<CronScheduleSelectorProps> = ({
  form,
  name,
  cronPatternList,
  label,
  customOptionName
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-4">
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              value={field.value}
              className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"
            >
              {cronPatternList.map((cronPattern) => {
                const scheduleIcon = getScheduleIcon(cronPattern);
                const scheduleColor = getScheduleColor(cronPattern);
                const scheduleDescription = getScheduleDescription(cronPattern);
                const humanReadable = safeParseCronPattern(cronPattern);
                const frequency = getFrequencyBadge(cronPattern);
                const frequencyColor = getFrequencyColor(frequency);

                return (
                  <FormItem
                    key={cronPattern}
                    className={cn(
                      "relative flex flex-col space-y-0 rounded-3xl border transition-all cursor-pointer hover:shadow-md hover:scale-[1.02]",
                      field.value === cronPattern
                        ? "border-primary ring-2 ring-primary/20 bg-primary/5 shadow-md"
                        : "border-border hover:border-primary/50"
                    )}
                  >
                    <FormControl>
                      <RadioGroupItem
                        value={cronPattern}
                        className="sr-only"
                      />
                    </FormControl>
                    <FormLabel className={cn(
                      "flex items-start gap-3 p-4 cursor-pointer w-full h-full",
                      field.value === cronPattern ? "text-primary" : "text-foreground"
                    )}>
                      <div className={cn("mt-1", scheduleColor)}>
                        {scheduleIcon}
                      </div>
                      <div className="flex-1 min-w-0 space-y-2">
                        <div className="flex items-center gap-2">
                          <code className="text-xs font-mono bg-muted px-2 py-1 rounded">
                            {cronPattern}
                          </code>
                          <span className={cn(
                            "text-xs font-medium px-2 py-1 rounded-full",
                            frequencyColor
                          )}>
                            {frequency}
                          </span>
                        </div>
                        {humanReadable && (
                          <div className="bg-muted/50 p-2 rounded-lg">
                            <p className="text-sm font-medium text-foreground">
                              {humanReadable}
                            </p>
                          </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {scheduleDescription}
                        </p>
                      </div>
                      {field.value === cronPattern && (
                        <div className="absolute top-3 right-3">
                          <div className="w-3 h-3 bg-primary rounded-full ring-2 ring-primary/20"></div>
                        </div>
                      )}
                    </FormLabel>
                  </FormItem>
                );
              })}

              {/* Custom Option */}
              <FormItem
                className={cn(
                  "relative flex flex-col space-y-0 rounded-3xl border transition-all cursor-pointer hover:shadow-md hover:scale-[1.02]",
                  field.value === customOptionName
                    ? "border-primary ring-2 ring-primary/20 bg-primary/5 shadow-md"
                    : "border-border hover:border-primary/50 border-dashed"
                )}
              >
                <FormControl>
                  <RadioGroupItem
                    value={customOptionName}
                    className="sr-only"
                  />
                </FormControl>
                <FormLabel className={cn(
                  "flex items-start gap-3 p-4 cursor-pointer w-full h-full",
                  field.value === customOptionName ? "text-primary" : "text-foreground"
                )}>
                  <div className="mt-1 text-purple-600">
                    <Settings className="h-5 w-5" />
                  </div>
                  <div className="flex-1 min-w-0 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {customOptionName}
                      </span>
                      <span className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 text-xs font-medium px-2 py-1 rounded-full">
                        Custom
                      </span>
                    </div>
                    <div className="bg-muted/50 p-2 rounded-lg">
                      <p className="text-sm text-muted-foreground">
                        Create your own schedule pattern
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Use the cron builder to define a custom schedule that fits your specific needs
                    </p>
                  </div>
                  {field.value === customOptionName && (
                    <div className="absolute top-3 right-3">
                      <div className="w-3 h-3 bg-primary rounded-full ring-2 ring-primary/20"></div>
                    </div>
                  )}
                </FormLabel>
              </FormItem>
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
