import React, { useState, useMemo } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { UseFormReturn } from 'react-hook-form';
import { ResourceGroup, RESOURCE_TYPE_COLORS } from '@/lib/constants/resource-types';
import { AwsIcon } from '@/components/aws-icons';
import { Input } from '@/components/ui/input';
import { Search, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface GroupedCheckboxGridProps {
  form: UseFormReturn<any>;
  name: string;
  groups: ResourceGroup[];
  label?: React.ReactNode;
  tooltip?: React.ReactNode;
}

// Function to get AWS icon for resource type
const getAwsIconForResourceType = (resourceType: string) => {
  const iconMap: Record<string, string> = {
    'ec2': 'ec2',
    'rds': 'rds',
    's3': 's3',
    'lambda': 'lambda',
    'ecs': 'ecs',
    'ecs_cluster': 'ecs_cluster',
    'vpc': 'vpc',
    'cloudformation': 'cloudformation',
    'eks': 'eks',
    'dynamodb': 'dynamodb',
    'cloudwatch': 'cloudwatch',
    'sqs': 'sqs',
    'sns': 'sns',
    'ebs': 'ebs',
    'neptune': 'neptune',
    'elasticache': 'elasticache',
    'elb': 'elb',
    'backup': 'backup',
    'efs': 'efs',
    'batch': 'batch',
    'ec2_auto_scaling': 'ec2_auto_scaling',
    'elastic_beanstalk': 'elastic_beanstalk',
    'app_runner': 'app_runner',
    'documentdb': 'documentdb',
    'opensearch': 'opensearch',
    'redshift': 'redshift',
  };

  const lowerType = resourceType.toLowerCase();
  const iconKey = iconMap[lowerType];
  return iconKey ? AwsIcon[iconKey] : null;
};

// Popular resources that should be highlighted
const POPULAR_RESOURCES = ['ec2', 'eks', 'rds', 's3', 'lambda', 'ebs', 'vpc', 'elb', 'cloudformation'];

export const GroupedCheckboxGrid: React.FC<GroupedCheckboxGridProps> = ({
  form,
  name,
  groups,
  label,
  tooltip
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Filter options based on search term
  const filteredGroups = useMemo(() => {
    if (!searchTerm) return groups;

    return groups.map(group => ({
      ...group,
      options: group.options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.value.toLowerCase().includes(searchTerm.toLowerCase())
      )
    })).filter(group => group.options.length > 0);
  }, [groups, searchTerm]);

  const toggleGroup = (groupLabel: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupLabel]: !prev[groupLabel]
    }));
  };

  const isGroupExpanded = (groupLabel: string) => {
    return expandedGroups[groupLabel] ?? true;
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        // Set default popular resources if no value exists
        let currentValue = Array.isArray(field.value) ? field.value : [];
        if (currentValue.length === 0) {
          currentValue = POPULAR_RESOURCES.map(resource => resource.toUpperCase());
          // Set the form value once if it's empty
          if (!field.value || field.value.length === 0) {
            field.onChange(POPULAR_RESOURCES.map(resource => resource.toUpperCase()));
          }
        }

        return (
          <FormItem className="space-y-4">
            {label && (
              <FormLabel>
                {tooltip ? (
                  <div className="flex items-center gap-2">
                    {label}
                    {tooltip}
                  </div>
                ) : (
                  label
                )}
              </FormLabel>
            )}

            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search resource types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rounded-3xl"
              />
            </div>

            {/* Resource Groups */}
            <div className="flex flex-col gap-4">
              {filteredGroups.map((group) => {
                const isExpanded = isGroupExpanded(group.label);
                const IconComponent = getAwsIconForResourceType(group.label);

                return (
                  <div key={group.label} className="border rounded-3xl p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {IconComponent && (
                          <div className="h-5 w-5 rounded-sm overflow-hidden flex items-center justify-center">
                            <IconComponent className="h-5 w-5" />
                          </div>
                        )}
                        <h3 className="font-semibold text-sm">{group.label}</h3>
                        <span className="text-xs text-muted-foreground">
                          ({group.options.length} services)
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleGroup(group.label)}
                        className="h-6 w-6 p-0"
                      >
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    {isExpanded && (
                      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                        {group.options
                          .sort((a, b) => {
                            const aIsPopular = POPULAR_RESOURCES.includes(a.value.toLowerCase());
                            const bIsPopular = POPULAR_RESOURCES.includes(b.value.toLowerCase());
                            if (aIsPopular && !bIsPopular) return -1;
                            if (!aIsPopular && bIsPopular) return 1;
                            return a.label.localeCompare(b.label);
                          })
                          .map((item) => {
                          const ServiceIcon = getAwsIconForResourceType(item.value);
                          const normalizedType = item.value.toUpperCase();
                          const color = RESOURCE_TYPE_COLORS[normalizedType] || '#757575';
                          const isChecked = currentValue.includes(item.value);
                          const isPopular = POPULAR_RESOURCES.includes(item.value.toLowerCase());

                          return (
                            <div
                              key={item.value}
                              className={cn(
                                "relative flex flex-row items-center space-x-3 space-y-0 rounded-3xl border transition-colors",
                                isChecked ? "border-primary/20" : "hover:bg-muted/50",
                                isPopular && "ring-1 ring-orange-200 dark:ring-orange-800"
                              )}
                            >
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0 w-full">
                                <FormControl>
                                  <Checkbox
                                    checked={isChecked}
                                    onCheckedChange={(checked) => {
                                      const newValue = checked
                                        ? [...currentValue, item.value]
                                        : currentValue.filter((value: string) => value !== item.value);
                                      field.onChange(newValue);
                                    }}
                                    className="ml-3"
                                  />
                                </FormControl>
                                <FormLabel
                                  className="flex items-center gap-2 flex-1 cursor-pointer p-3 pl-0"
                                  style={{ color, fontWeight: 'bold' }}
                                >
                                  {ServiceIcon && (
                                    <div className="h-5 w-5 rounded-sm overflow-hidden flex items-center justify-center">
                                      <ServiceIcon className="h-5 w-5" />
                                    </div>
                                  )}
                                  <span className="text-sm font-normal flex-1">
                                    {item.label}
                                    {isPopular && (
                                      <span className="ml-1 text-xs text-orange-600 dark:text-orange-400">
                                        ★
                                      </span>
                                    )}
                                  </span>
                                </FormLabel>
                              </FormItem>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {filteredGroups.length === 0 && searchTerm && (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No resource types found matching "{searchTerm}"</p>
              </div>
            )}

            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};
