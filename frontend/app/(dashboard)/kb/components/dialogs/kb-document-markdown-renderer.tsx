import React, { useState } from 'react';
import ReactMarkdown, { Components } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { useTheme } from 'next-themes';
import { Syntax<PERSON><PERSON>lighter } from '@/components/ui/code-block';
import {
  Code,
  Copy,
  Check,
  FileJson,
  FileType,
  Hash,
  Terminal,
  Database,
  Globe,
  Cpu,
  Cog,
  PenTool,
  FileText,
  FileCode,
  Puzzle,
  Braces
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface KBDocumentMarkdownRendererProps {
  content: string;
}

// Function to get language-specific icon
const getLanguageIcon = (language: string) => {
  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
      return <FileJson className="h-3.5 w-3.5" />;
    case 'typescript':
    case 'ts':
      return <FileType className="h-3.5 w-3.5" />;
    case 'jsx':
    case 'tsx':
    case 'react':
      return <Puzzle className="h-3.5 w-3.5" />;
    case 'python':
    case 'py':
      return <Hash className="h-3.5 w-3.5" />;
    case 'java':
      return <Cpu className="h-3.5 w-3.5" />;
    case 'c':
    case 'cpp':
    case 'c++':
      return <Braces className="h-3.5 w-3.5" />;
    case 'csharp':
    case 'c#':
      return <Hash className="h-3.5 w-3.5" />;
    case 'php':
      return <Globe className="h-3.5 w-3.5" />;
    case 'ruby':
    case 'rb':
      return <PenTool className="h-3.5 w-3.5" />;
    case 'go':
      return <FileCode className="h-3.5 w-3.5" />;
    case 'html':
      return <Globe className="h-3.5 w-3.5" />;
    case 'css':
    case 'scss':
    case 'sass':
      return <PenTool className="h-3.5 w-3.5" />;
    case 'json':
      return <FileJson className="h-3.5 w-3.5" />;
    case 'sql':
      return <Database className="h-3.5 w-3.5" />;
    case 'bash':
    case 'sh':
    case 'shell':
      return <Terminal className="h-3.5 w-3.5" />;
    case 'yaml':
    case 'yml':
      return <FileText className="h-3.5 w-3.5" />;
    case 'dockerfile':
      return <Cog className="h-3.5 w-3.5" />;
    case 'markdown':
    case 'md':
      return <FileText className="h-3.5 w-3.5" />;
    default:
      return <Code className="h-3.5 w-3.5" />;
  }
};

// Inline Code Component
const InlineCodeRenderer = ({ children }: { children: React.ReactNode }) => {
  return (
    <code className="px-1.5 py-0.5 mx-0.5 rounded-md bg-muted/80 font-mono text-sm border inline align-baseline relative -top-px break-words">
      {children}
    </code>
  );
};

// Enhanced Code Block Component
const CodeBlockRenderer = ({
  className,
  children,
  inline
}: {
  className?: string;
  children: React.ReactNode;
  inline?: boolean;
}) => {
  const { theme } = useTheme();

  // Handle inline code - use dedicated inline component
  if (inline) {
    return <InlineCodeRenderer>{children}</InlineCodeRenderer>;
  }

  // Extract language from className
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : 'text';
  const codeString = String(children).replace(/\n$/, '');

  const languageIcon = getLanguageIcon(language);

  return (
    <div className="my-4 rounded-lg border border-border/60 bg-card shadow-sm overflow-hidden max-w-full">
      {/* Header */}
      <div className="flex items-center justify-between px-3 py-2 bg-muted/50 border-b border-border/60">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          {languageIcon}
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide truncate">
            {language}
          </span>
        </div>
      </div>

      {/* Code Content */}
      <div className="relative min-w-0">
        <SyntaxHighlighter
          language={language}
          className="text-sm"
          code={codeString}
          showCopyButton={true}
        />
      </div>
    </div>
  );
};

// Define custom components for the markdown renderer
const components: Components = {
    code(props) {
    const { className, children, inline, ...rest } = props as any;

    // More robust inline detection
    const childrenAsString = React.Children.toArray(children).join('');
    const hasLanguageClass = className && className.startsWith('language-');
    const hasNewlines = typeof childrenAsString === 'string' && childrenAsString.includes('\n');
    const isShortCode = typeof childrenAsString === 'string' && childrenAsString.length < 100;

    // Determine if this should be inline:
    // - Explicitly marked as inline
    // - No language class and no newlines (typical backtick code)
    // - Short code without language specification
    const isInline = inline || (!hasLanguageClass && !hasNewlines) || (!hasLanguageClass && isShortCode);

    return (
      <CodeBlockRenderer
        className={className}
        inline={isInline}
        {...rest}
      >
        {children}
      </CodeBlockRenderer>
    );
  },

  pre({ children, ...props }) {
    // Only render pre wrapper for actual code blocks, not inline code
    const isCodeBlock = React.Children.toArray(children).some(
      child => React.isValidElement(child) && child.props?.className?.startsWith('language-')
    );

    if (isCodeBlock) {
      return <>{children}</>;
    }

    // For other pre content, render normally
    return <pre className="whitespace-pre-wrap font-mono text-sm bg-muted/30 p-3 rounded-md border overflow-x-auto" {...props}>{children}</pre>;
  },

  p({ children, ...props }) {
    return (
      <p className="mb-4 last:mb-0 leading-relaxed text-foreground" {...props}>
        {children}
      </p>
    );
  },

  h1({ children, ...props }) {
    return (
      <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0 text-foreground border-b border-border/30 pb-2" {...props}>
        {children}
      </h1>
    );
  },

  h2({ children, ...props }) {
    return (
      <h2 className="text-xl font-semibold mb-3 mt-5 first:mt-0 text-foreground" {...props}>
        {children}
      </h2>
    );
  },

  h3({ children, ...props }) {
    return (
      <h3 className="text-lg font-medium mb-3 mt-4 first:mt-0 text-foreground" {...props}>
        {children}
      </h3>
    );
  },

  h4({ children, ...props }) {
    return (
      <h4 className="text-base font-medium mb-2 mt-3 first:mt-0 text-foreground" {...props}>
        {children}
      </h4>
    );
  },

  h5({ children, ...props }) {
    return (
      <h5 className="text-sm font-medium mb-2 mt-3 first:mt-0 text-foreground" {...props}>
        {children}
      </h5>
    );
  },

  h6({ children, ...props }) {
    return (
      <h6 className="text-sm font-medium mb-2 mt-2 first:mt-0 text-muted-foreground" {...props}>
        {children}
      </h6>
    );
  },

  ul({ children, ...props }) {
    return (
      <ul className="list-disc list-outside pl-6 mb-4 space-y-1" {...props}>
        {children}
      </ul>
    );
  },

  ol({ children, ...props }) {
    return (
      <ol className="list-decimal list-outside pl-6 mb-4 space-y-1" {...props}>
        {children}
      </ol>
    );
  },

  li({ children, ...props }) {
    return (
      <li className="leading-relaxed" {...props}>
        {children}
      </li>
    );
  },

  blockquote({ children, ...props }) {
    return (
      <blockquote className="border-l-4 border-primary/30 pl-4 my-4 italic text-muted-foreground bg-muted/30 py-2 rounded-r-md" {...props}>
        {children}
      </blockquote>
    );
  },

  a({ href, children, ...props }) {
    return (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-primary hover:text-primary/80 underline underline-offset-2 decoration-1 hover:decoration-2 transition-all"
        {...props}
      >
        {children}
      </a>
    );
  },

  table({ children, ...props }) {
    return (
      <div className="my-4 overflow-x-auto border border-border/60 rounded-lg">
        <table className="w-full border-collapse text-sm" {...props}>
          {children}
        </table>
      </div>
    );
  },

  thead({ children, ...props }) {
    return (
      <thead className="bg-muted/50" {...props}>
        {children}
      </thead>
    );
  },

  th({ children, ...props }) {
    return (
      <th className="px-4 py-2 text-left font-medium border-b border-border/60" {...props}>
        {children}
      </th>
    );
  },

  td({ children, ...props }) {
    return (
      <td className="px-4 py-2 border-b border-border/30 last:border-b-0" {...props}>
        {children}
      </td>
    );
  },

  hr({ ...props }) {
    return <hr className="my-6 border-border/40" {...props} />;
  },

  img({ src, alt, ...props }) {
    return (
      <img
        src={src}
        alt={alt}
        className="max-w-full h-auto rounded-lg border border-border/30 my-4"
        {...props}
      />
    );
  },

  strong({ children, ...props }) {
    return (
      <strong className="font-semibold text-foreground" {...props}>
        {children}
      </strong>
    );
  },

  em({ children, ...props }) {
    return (
      <em className="italic" {...props}>
        {children}
      </em>
    );
  },
};

export function KBDocumentMarkdownRenderer({ content }: KBDocumentMarkdownRendererProps) {
  return (
    <div className="w-full max-w-full overflow-hidden">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={components}
        className="text-foreground break-words"
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
