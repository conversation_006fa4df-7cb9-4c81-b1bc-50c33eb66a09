import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, Loader2, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SimplePDFViewer } from './SimplePDFViewer';

// Set up the worker for PDF.js
if (typeof window !== 'undefined') {
  try {
    pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
  } catch (error) {
    console.error('Failed to set PDF.js worker:', error);
  }
}

interface PDFViewerProps {
  url?: string;
  base64Content?: string;
  className?: string;
  fileName?: string;
}

export function PDFViewer({ url, base64Content, className, fileName }: PDFViewerProps) {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pdfData, setPdfData] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState<number>(1.0);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [useFallback, setUseFallback] = useState<boolean>(false);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Update container width on mount and window resize
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    };

    // Initial width calculation
    updateWidth();

    // Set up resize observer for responsive adjustments
    const resizeObserver = new ResizeObserver(updateWidth);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Clean up
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    setLoading(true);
    setError(null);

    // Handle base64 content if provided
    if (base64Content) {
      try {
        // Check if content needs to be parsed (if it's from JSON)
        let actualContent = base64Content;
        try {
          // Try to parse the content as JSON
          const contentObj = JSON.parse(base64Content);

          // Handle different JSON structures that might be returned by the API
          if (contentObj.content) {
            // If there's a content field, use that
            actualContent = contentObj.content;
          } else if (contentObj.is_binary) {
            // Some APIs might directly set is_binary flag at root level
            actualContent = base64Content;
          } else if (typeof contentObj === 'string') {
            // If it parsed as JSON but resulted in a string
            actualContent = contentObj;
          }

          // If the content seems to be base64 encoded text rather than binary PDF
          if (actualContent.length > 100 && /^[A-Za-z0-9+/=]+$/.test(actualContent.substring(0, 100))) {
            // It's likely base64, keep as is
          } else {
            // Not base64, try to convert from text to base64
            console.warn('Content is not in expected base64 format, attempting to convert');
            actualContent = btoa(actualContent);
          }
        } catch (e) {
          // If not JSON, use as is - likely already base64 encoded
          console.log('Not JSON, using as is');
        }

        // Check if it already has data URI prefix
        if (!actualContent.startsWith('data:application/pdf;base64,')) {
          actualContent = `data:application/pdf;base64,${actualContent}`;
        }

        setPdfData(actualContent);
      } catch (err) {
        console.error('Error processing base64 content:', err);
        setError('Failed to load PDF from base64 content');
      } finally {
        setLoading(false);
      }
    } else if (url) {
      // Use direct URL
      setPdfData(url);
      setLoading(false);
    } else {
      setError('No PDF source provided');
      setLoading(false);
    }
  }, [url, base64Content]);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setLoading(false);
  }

  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.2, 3.0));
  };

  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
      </div>
    );
  }

  if (error || useFallback) {
    return (
      <div className="flex flex-col h-full">
        {error && !useFallback && (
          <div className="flex items-center justify-center p-4 bg-yellow-50 border-b border-yellow-200">
            <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
            <p className="text-sm text-yellow-800">PDF viewer failed to load. Using fallback viewer.</p>
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => setUseFallback(true)}
            >
              Use Simple Viewer
            </Button>
          </div>
        )}
        <div className="flex-1">
          <SimplePDFViewer
            base64Content={base64Content}
            url={url}
            fileName={fileName}
            className="h-full"
          />
        </div>
      </div>
    );
  }

  // Calculate appropriate width for PDF pages
  const pageWidth = containerWidth ?
    Math.min(containerWidth - 40, 800) * scale :
    Math.min(500, typeof window !== 'undefined' ? window.innerWidth - 80 : 500) * scale;

  return (
    <div
      ref={containerRef}
      className={cn("flex flex-col w-full h-full relative", className)}
    >
      {/* Zoom controls - simple floating controls */}
      <div className="flex items-center absolute top-2 right-2 bg-background/80 backdrop-blur-sm rounded-2xl p-1 z-10 shadow-sm border">
        <Button
          onClick={zoomOut}
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0"
          aria-label="Zoom out"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <span className="text-xs font-medium px-1">
          {Math.round(scale * 100)}%
        </span>
        <Button
          onClick={zoomIn}
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0"
          aria-label="Zoom in"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
      </div>

      {/* Simple document container */}
      <Document
        file={pdfData}
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={(error) => {
          console.error('Error loading PDF:', error);
          setError('Failed to load PDF. The file may be corrupted or in an unsupported format.');
          // Automatically switch to fallback after a brief delay
          setTimeout(() => setUseFallback(true), 2000);
        }}
        loading={
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        }
        className="flex-1 overflow-auto w-full flex flex-col items-center"
        error={
          <div className="flex flex-col items-center justify-center h-full text-center p-4">
            <p className="text-destructive mb-2 font-medium">Error loading PDF</p>
            <p className="text-sm text-muted-foreground">The PDF could not be loaded. It may be corrupted or in an unsupported format.</p>
          </div>
        }
      >
        {Array.from(new Array(numPages), (_, index) => (
          <Page
            key={`page_${index + 1}`}
            pageNumber={index + 1}
            renderTextLayer={true}
            renderAnnotationLayer={true}
            className="mb-4"
            width={pageWidth}
            scale={1} // Scale is already applied to width
          />
        ))}
      </Document>
    </div>
  );
}
