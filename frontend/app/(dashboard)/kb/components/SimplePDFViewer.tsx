import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SimplePDFViewerProps {
  base64Content?: string;
  url?: string;
  className?: string;
  fileName?: string;
}

export function SimplePDFViewer({ base64Content, url, className, fileName }: SimplePDFViewerProps) {
  const pdfUrl = base64Content || url;

  if (!pdfUrl) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">No PDF content available</p>
      </div>
    );
  }

  const handleDownload = () => {
    if (base64Content) {
      // Create download link for base64 content
      const link = document.createElement('a');
      link.href = base64Content;
      link.download = fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (url) {
      // Open URL in new tab for download
      window.open(url, '_blank');
    }
  };

  const handleOpenInNewTab = () => {
    if (pdfUrl) {
      window.open(pdfUrl, '_blank');
    }
  };

  return (
    <div className={cn("flex flex-col w-full h-full", className)}>
      {/* Controls */}
      <div className="flex items-center justify-between p-2 border-b bg-muted/30">
        <span className="text-sm text-muted-foreground">PDF Viewer</span>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenInNewTab}
            className="text-xs"
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            Open in New Tab
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="text-xs"
          >
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
        </div>
      </div>

      {/* PDF iframe */}
      <div className="flex-1 relative">
        <iframe
          src={pdfUrl}
          className="w-full h-full border-0"
          title="PDF Viewer"
          style={{ minHeight: '400px' }}
        />
      </div>
    </div>
  );
}
