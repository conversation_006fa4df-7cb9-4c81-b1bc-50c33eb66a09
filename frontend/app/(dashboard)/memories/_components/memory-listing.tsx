'use client';

import { DataTable } from '@/components/ui/table/data-table';
import { columns } from './memory-tables/columns';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { MemoryService } from '@/client';
import { useEffect } from 'react';
import { CacheKey } from '@/components/utils/cache-key';
import { DataTableSearch } from '@/components/ui/table/data-table-search';
import { DataTableFilterBox } from '@/components/ui/table/data-table-filter-box';
import { DataTableResetFilter } from '@/components/ui/table/data-table-reset-filter';
import { useQueryState, parseAsInteger } from 'nuqs';

type MemoryListingProps = {
    page?: number;
    limit?: number;
    q?: string;
    agentRole?: string;
};

function getMemoriesQueryOptions({
    page,
    limit,
    q,
    agentRole
}: {
    page: number;
    limit: number;
    q?: string | null;
    agentRole?: string | null;
}) {
    const agentRolesArray = agentRole ? [agentRole] : undefined;

    return {
        queryFn: () =>
            MemoryService.getMemory({
                requestBody: {
                    agent_roles: agentRolesArray,
                    limit
                }
            }),
        queryKey: [CacheKey.Memory, { page, limit, q, agentRole }]
    };
}

export default function MemoryListing({
    page: initialPage = 1,
    limit = 10,
    q: initialQuery,
    agentRole: initialAgentRole
}: MemoryListingProps) {
    const queryClient = useQueryClient();
    const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(initialPage));
    const [searchQuery, setSearchQuery] = useQueryState('q', { defaultValue: initialQuery || '' });
    const [agentRoleFilter, setAgentRoleFilter] = useQueryState('agent_role', { defaultValue: initialAgentRole || '' });

    // Check if any filter is active
    const isAnyFilterActive = !!(searchQuery || agentRoleFilter);

    // Reset all filters
    const resetFilters = () => {
        setSearchQuery(null);
        setAgentRoleFilter(null);
        setPage(1);
    };

    // Agent role filter options - These should ideally come from a proper API call
    const agentRoleOptions = [
        { value: 'Tony', label: 'Tony' },
        { value: 'Oliver', label: 'Oliver' },
        { value: 'Kai', label: 'Kai' },
        { value: 'Anna', label: 'Anna' },
        { value: 'Alex', label: 'Alex' }
    ];

    const {
        data: memories,
        isPending,
        isPlaceholderData
    } = useQuery({
        ...getMemoriesQueryOptions({
            page,
            limit,
            q: searchQuery,
            agentRole: agentRoleFilter
        }),
        placeholderData: (prevData) => prevData
    });

    const hasNextPage = !isPlaceholderData && memories?.memories.length === limit;

    useEffect(() => {
        if (hasNextPage) {
            queryClient.prefetchQuery(
                getMemoriesQueryOptions({
                    page: page + 1,
                    limit,
                    q: searchQuery,
                    agentRole: agentRoleFilter
                })
            );
        }
    }, [page, queryClient, hasNextPage, limit, searchQuery, agentRoleFilter]);

    // Filter memories based on search query if provided
    const filteredMemories = memories?.memories.filter(memory => {
        if (!searchQuery) return true;
        const searchLower = searchQuery.toLowerCase();
        return (
            memory.agent_role.toLowerCase().includes(searchLower) ||
            memory.task?.toLowerCase().includes(searchLower) ||
            memory.solution?.toLowerCase().includes(searchLower) ||
            memory.tags?.some(tag => tag.toLowerCase().includes(searchLower))
        );
    }) || [];

    return (
        <div className="space-y-4">
            <div className="flex flex-wrap items-center gap-4">
                <DataTableSearch
                    searchKey="memories"
                    searchQuery={searchQuery || ''}
                    setSearchQuery={setSearchQuery}
                    setPage={setPage}
                />
                <DataTableFilterBox
                    filterKey="agent_role"
                    title="Agent Role"
                    options={agentRoleOptions}
                    setFilterValue={setAgentRoleFilter}
                    filterValue={agentRoleFilter || ''}
                />
                <DataTableResetFilter
                    isFilterActive={isAnyFilterActive}
                    onReset={resetFilters}
                />
            </div>
            <DataTable
                columns={columns}
                data={filteredMemories}
                totalItems={memories?.count ?? 0}
                loading={isPending}
                customScrollClass="grid h-[calc(80vh-220px)] rounded-xl border md:h-[calc(90dvh-380px)]"
            />
        </div>
    );
}
