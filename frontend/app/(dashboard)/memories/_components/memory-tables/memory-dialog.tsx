'use client';

import { Memory } from '@/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MarkdownRenderer } from '@/components/chat/components/message/markdown-renderer';

interface MemoryDialogProps {
  memory: Memory | null;
  isOpen: boolean;
  onClose: () => void;
}

export function MemoryDialog({ memory, isOpen, onClose }: MemoryDialogProps) {
  if (!memory) return null;

  const formattedAgentRole = memory.agent_role
    .replace('_', ' ')
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-xl flex items-center gap-2">
            Memory Details
            <Badge variant="outline">{formattedAgentRole}</Badge>
          </DialogTitle>
          <DialogDescription>
            {memory.task || 'No task specified'}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="pr-4 max-h-[calc(90vh-150px)]">
          <div className="space-y-6">
            {/* Tags Section */}
            {memory.tags && memory.tags.length > 0 && (
              <div className="flex flex-wrap gap-1.5 mb-4">
                {memory.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* Solution Section */}
            {memory.solution && (
              <div className="space-y-2">
                <h3 className="text-lg font-semibold border-b pb-1">Solution</h3>
                <div className="prose prose-sm dark:prose-invert max-w-none">
                  <MarkdownRenderer content={memory.solution} />
                </div>
              </div>
            )}

            {/* Links Section */}
            {memory.links && memory.links.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-lg font-semibold border-b pb-1">Related Links</h3>
                <ul className="space-y-2 pl-1">
                  {memory.links.map((link, index) => (
                    <li key={index} className="flex items-center">
                      <Badge variant="info" className="mr-2">
                        Link
                      </Badge>
                      <span>{link}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* ID Section */}
            <div className="pt-2 border-t text-xs text-muted-foreground">
              <span className="font-medium">Memory ID:</span>{' '}
              <span className="font-mono">{memory.id}</span>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
