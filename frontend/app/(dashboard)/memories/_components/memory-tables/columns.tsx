'use client';

import { Memory, MemoryService } from '@/client';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON><PERSON>Key } from '@/components/utils/cache-key';
import { toast } from 'sonner';
import { Eye, Trash2 } from 'lucide-react';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useState } from 'react';
import { MemoryDialog } from './memory-dialog';

// Delete action component
function DeleteMemoryAction({ memory }: { memory: Memory }) {
    const queryClient = useQueryClient();

    const { mutate: deleteMemory, isPending } = useMutation({
        mutationFn: () => MemoryService.deleteMemory({ id: memory.id, agentRole: memory.agent_role }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [CacheKey.Memory] });
            toast.success('Memory deleted successfully');
        },
        onError: (error) => {
            console.error('Failed to delete memory:', error);
            toast.error('Failed to delete memory');
        }
    });

    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <Button
                    variant="ghost"
                    size="sm"
                    disabled={isPending}
                >
                    <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Delete Memory</AlertDialogTitle>
                    <AlertDialogDescription>
                        Are you sure you want to delete this memory? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        onClick={() => deleteMemory()}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                        Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}

// View memory action component
function ViewMemoryAction({ memory }: { memory: Memory }) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    return (
        <>
            <Button
                variant="ghost"
                size="sm"
                title="View details"
                onClick={() => setIsDialogOpen(true)}
            >
                <Eye className="h-4 w-4" />
            </Button>
            <MemoryDialog
                memory={memory}
                isOpen={isDialogOpen}
                onClose={() => setIsDialogOpen(false)}
            />
        </>
    );
}

export const columns: ColumnDef<Memory>[] = [
    {
        accessorKey: 'agent_role',
        header: 'Agent Role',
        size: 150,
        cell: ({ row }) => {
            const agentRole = row.getValue('agent_role') as string;
            return (
                <Badge variant="outline">
                    {agentRole.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
            );
        }
    },
    {
        accessorKey: 'task',
        header: 'Task',
        size: 300,
        minSize: 200,
        cell: ({ row }) => {
            const task = row.getValue('task') as string | null;
            return (
                <div className="max-w-[300px]">
                    <div className="font-medium truncate" title={task || ''}>
                        {task || 'N/A'}
                    </div>
                </div>
            );
        }
    },
    {
        accessorKey: 'solution',
        header: 'Solution',
        size: 400,
        minSize: 300,
        cell: ({ row }) => {
            const solution = row.getValue('solution') as string | null;
            return (
                <div className="max-w-[400px]">
                    <div className="text-sm truncate" title={solution || ''}>
                        {solution || 'N/A'}
                    </div>
                </div>
            );
        }
    },
    {
        accessorKey: 'tags',
        header: 'Tags',
        size: 200,
        cell: ({ row }) => {
            const tags = row.getValue('tags') as string[] | null;
            if (!tags || tags.length === 0) {
                return <span className="text-muted-foreground">No tags</span>;
            }
            return (
                <div className="flex flex-wrap gap-1">
                    {tags.slice(0, 2).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                        </Badge>
                    ))}
                    {tags.length > 2 && (
                        <Badge variant="secondary" className="text-xs">
                            +{tags.length - 2}
                        </Badge>
                    )}
                </div>
            );
        }
    },
    {
        accessorKey: 'links',
        header: 'Links',
        size: 100,
        cell: ({ row }) => {
            const links = row.getValue('links') as string[] | null;
            if (!links || links.length === 0) {
                return <span className="text-muted-foreground">None</span>;
            }
            return (
                <Badge variant="info" className="text-xs">
                    {links.length} link{links.length > 1 ? 's' : ''}
                </Badge>
            );
        }
    },
    {
        id: 'actions',
        header: 'Actions',
        size: 120,
        cell: ({ row }) => {
            const memory = row.original;
            return (
                <div className="flex items-center gap-2">
                    <ViewMemoryAction memory={memory} />
                    <DeleteMemoryAction memory={memory} />
                </div>
            );
        }
    }
];
