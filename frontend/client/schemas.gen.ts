// This file is auto-generated by @hey-api/openapi-ts

export const AWSAccountCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        account_id: {
            type: 'string',
            maxLength: 12,
            minLength: 12,
            title: 'Account Id'
        },
        access_key_id: {
            type: 'string',
            maxLength: 128,
            title: 'Access Key Id'
        },
        secret_access_key: {
            type: 'string',
            maxLength: 256,
            title: 'Secret Access Key'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        regions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Regions',
            default: []
        },
        types: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Types',
            default: []
        },
        cron_pattern: {
            type: 'string',
            maxLength: 20,
            minLength: 1,
            title: 'Cron Pattern'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'account_id', 'access_key_id', 'secret_access_key', 'workspace_id', 'cron_pattern'],
    title: 'AWSAccountCreate'
} as const;

export const AWSAccountDetailSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        access_key_id: {
            type: 'string',
            title: 'Access Key Id'
        },
        secret_access_key: {
            type: 'string',
            title: 'Secret Access Key'
        },
        account_id: {
            type: 'string',
            title: 'Account Id'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'id', 'access_key_id', 'secret_access_key', 'account_id'],
    title: 'AWSAccountDetail'
} as const;

export const AWSAccountPublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'id'],
    title: 'AWSAccountPublic'
} as const;

export const AWSAccountUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    minLength: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 50
                },
                {
                    type: 'null'
                }
            ],
            title: 'Environment'
        },
        account_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Account Id'
        },
        regions: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Regions',
            default: []
        },
        types: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Types',
            default: []
        },
        cron_pattern: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Cron Pattern'
        },
        access_key_id: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 128
                },
                {
                    type: 'null'
                }
            ],
            title: 'Access Key Id'
        },
        secret_access_key: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 256
                },
                {
                    type: 'null'
                }
            ],
            title: 'Secret Access Key'
        }
    },
    type: 'object',
    title: 'AWSAccountUpdate'
} as const;

export const AWSAccountsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AWSAccountPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'AWSAccountsPublic'
} as const;

export const AccountEnvironementSchema = {
    type: 'string',
    enum: ['production', 'staging', 'development'],
    title: 'AccountEnvironement'
} as const;

export const ActivationResponseSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        },
        expires_at: {
            type: 'string',
            format: 'date-time',
            title: 'Expires At'
        }
    },
    type: 'object',
    required: ['message', 'expires_at'],
    title: 'ActivationResponse'
} as const;

export const ActivationResultSchema = {
    properties: {
        success: {
            type: 'boolean',
            title: 'Success'
        },
        message: {
            type: 'string',
            title: 'Message'
        },
        redirect_url: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Redirect Url'
        },
        welcome_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Welcome Message'
        }
    },
    type: 'object',
    required: ['success', 'message'],
    title: 'ActivationResult'
} as const;

export const AddressSchema = {
    properties: {
        city: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'City'
        },
        country: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Country'
        },
        line1: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Line1'
        },
        line2: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Line2'
        },
        postal_code: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Postal Code'
        },
        state: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'State'
        }
    },
    type: 'object',
    title: 'Address'
} as const;

export const AgentConnectorCreateSchema = {
    properties: {
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        builtin_connector_ids: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Builtin Connector Ids'
        },
        mcp_servers: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Mcp Servers'
        }
    },
    type: 'object',
    required: ['agent_id', 'builtin_connector_ids', 'mcp_servers'],
    title: 'AgentConnectorCreate'
} as const;

export const AgentConnectorResponseSchema = {
    properties: {
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        builtin_connectors: {
            items: {
                '$ref': '#/components/schemas/BuiltInConnectorResponse'
            },
            type: 'array',
            title: 'Builtin Connectors'
        },
        mcp_servers: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Mcp Servers'
        }
    },
    type: 'object',
    required: ['agent_id', 'builtin_connectors', 'mcp_servers'],
    title: 'AgentConnectorResponse'
} as const;

export const AgentConnectorUpdateSchema = {
    properties: {
        builtin_connector_ids: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Builtin Connector Ids'
        },
        mcp_servers: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Mcp Servers'
        }
    },
    type: 'object',
    required: ['builtin_connector_ids', 'mcp_servers'],
    title: 'AgentConnectorUpdate'
} as const;

export const AgentContextListInputSchema = {
    properties: {
        agent_ids: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Agent Ids'
        }
    },
    type: 'object',
    required: ['agent_ids'],
    title: 'AgentContextListInput'
} as const;

export const AgentContextListResponseSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AgentContextRead'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'AgentContextListResponse',
    description: `Response model for paginated agent context list.

Attributes:
    data: List of agent context items
    count: Total number of items available (before pagination)`
} as const;

export const AgentContextReadSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 0,
            title: 'Title'
        },
        context: {
            type: 'string',
            maxLength: 5000,
            minLength: 0,
            title: 'Context'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: true
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['title', 'context', 'id', 'agent_id', 'created_at'],
    title: 'AgentContextRead'
} as const;

export const AgentContextUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        context: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 5000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Context'
        },
        is_active: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Is Active'
        }
    },
    type: 'object',
    title: 'AgentContextUpdate'
} as const;

export const AgentCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title',
            description: 'The title/name of the agent'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            description: "Detailed description of the agent's purpose"
        },
        type: {
            '$ref': '#/components/schemas/AgentType',
            description: 'Type of the agent',
            default: 'conversation_agent'
        },
        instructions: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Instructions',
            description: 'Custom instructions for the agent'
        }
    },
    type: 'object',
    required: ['title'],
    title: 'AgentCreate'
} as const;

export const AgentPublicSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title',
            description: 'The title/name of the agent'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            description: "Detailed description of the agent's purpose"
        },
        type: {
            '$ref': '#/components/schemas/AgentType',
            description: 'Type of the agent',
            default: 'conversation_agent'
        },
        instructions: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Instructions',
            description: 'Custom instructions for the agent'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        is_active: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Is Active'
        }
    },
    type: 'object',
    required: ['title', 'id'],
    title: 'AgentPublic'
} as const;

export const AgentTypeSchema = {
    type: 'string',
    enum: ['conversation_agent', 'autonomous_agent'],
    title: 'AgentType',
    description: 'Defines the supported types of agents in the system.'
} as const;

export const AgentTypeUsageSchema = {
    properties: {
        agent_type: {
            type: 'string',
            title: 'Agent Type'
        },
        total_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Total Tokens'
        }
    },
    type: 'object',
    required: ['agent_type', 'total_tokens'],
    title: 'AgentTypeUsage'
} as const;

export const AgentUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    minLength: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            description: "Detailed description of the agent's purpose"
        },
        type: {
            '$ref': '#/components/schemas/AgentType',
            description: 'Type of the agent',
            default: 'conversation_agent'
        },
        instructions: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Instructions',
            description: 'Custom instructions for the agent'
        },
        workspace_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspace Id'
        },
        is_active: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Is Active'
        }
    },
    type: 'object',
    title: 'AgentUpdate'
} as const;

export const AgentsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AgentPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'AgentsPublic'
} as const;

export const AlertCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 200,
            title: 'Title',
            description: 'Alert title'
        },
        description: {
            type: 'string',
            title: 'Description',
            description: 'Detailed alert description'
        },
        severity: {
            '$ref': '#/components/schemas/AlertSeverity',
            description: 'Alert severity level'
        }
    },
    type: 'object',
    required: ['title', 'description', 'severity'],
    title: 'AlertCreate',
    description: 'Schema for creating a new alert'
} as const;

export const AlertListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AlertResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'AlertList',
    description: 'Schema for list of alerts with pagination'
} as const;

export const AlertResponseSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 200,
            title: 'Title',
            description: 'Alert title'
        },
        description: {
            type: 'string',
            title: 'Description',
            description: 'Detailed alert description'
        },
        severity: {
            '$ref': '#/components/schemas/AlertSeverity',
            description: 'Alert severity level'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        status: {
            '$ref': '#/components/schemas/AlertStatus'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['title', 'description', 'severity', 'id', 'workspace_id', 'status', 'created_at'],
    title: 'AlertResponse',
    description: 'Schema for alert response'
} as const;

export const AlertSeveritySchema = {
    type: 'string',
    enum: ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO'],
    title: 'AlertSeverity'
} as const;

export const AlertStatusSchema = {
    type: 'string',
    enum: ['OPEN', 'ACKNOWLEDGED', 'RESOLVED', 'CLOSED'],
    title: 'AlertStatus'
} as const;

export const AlertStatusSummarySchema = {
    properties: {
        status_counts: {
            additionalProperties: {
                type: 'integer'
            },
            type: 'object',
            title: 'Status Counts',
            description: 'Count of alerts by status'
        },
        total: {
            type: 'integer',
            title: 'Total',
            description: 'Total number of alerts in the period'
        }
    },
    type: 'object',
    required: ['status_counts', 'total'],
    title: 'AlertStatusSummary',
    description: 'Summary of alerts by status for the last 30 days'
} as const;

export const AlertUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 200
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        severity: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/AlertSeverity'
                },
                {
                    type: 'null'
                }
            ]
        },
        status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/AlertStatus'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'AlertUpdate',
    description: 'Schema for updating an existing alert'
} as const;

export const AsyncTaskStatusSchema = {
    type: 'string',
    enum: ['PENDING', 'PROGRESS', 'SUCCESS', 'FAILURE'],
    title: 'AsyncTaskStatus'
} as const;

export const AvailableUserSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        email: {
            type: 'string',
            title: 'Email'
        },
        full_name: {
            type: 'string',
            title: 'Full Name'
        }
    },
    type: 'object',
    required: ['id', 'email', 'full_name'],
    title: 'AvailableUser'
} as const;

export const AvailableUsersCurrentWorkspaceSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AvailableUser'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'AvailableUsersCurrentWorkspace'
} as const;

export const BillingDetailsSchema = {
    properties: {
        address: {
            '$ref': '#/components/schemas/Address'
        },
        email: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Email'
        },
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        phone: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Phone'
        }
    },
    type: 'object',
    required: ['address'],
    title: 'BillingDetails'
} as const;

export const Body_login_login_access_tokenSchema = {
    properties: {
        grant_type: {
            type: 'string',
            pattern: 'password',
            title: 'Grant Type'
        },
        username: {
            type: 'string',
            title: 'Username'
        },
        password: {
            type: 'string',
            title: 'Password'
        },
        scope: {
            type: 'string',
            title: 'Scope',
            default: ''
        },
        client_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Client Id'
        },
        client_secret: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Client Secret'
        },
        slackOAuth: {
            type: 'boolean',
            title: 'Slackoauth',
            default: false
        },
        appId: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Appid'
        },
        teamId: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Teamid'
        }
    },
    type: 'object',
    required: ['username', 'password'],
    title: 'Body_login-login_access_token'
} as const;

export const BuiltInConnectorSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name',
            description: 'Unique identifier for the connector'
        },
        display_name: {
            type: 'string',
            title: 'Display Name',
            description: 'Human-readable name for the connector'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        default_required_permission: {
            type: 'boolean',
            title: 'Default Required Permission',
            default: false
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['name', 'display_name'],
    title: 'BuiltInConnector',
    description: 'Definition of built-in connectors available in the system'
} as const;

export const BuiltInConnectorResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        display_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Display Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        }
    },
    type: 'object',
    required: ['id'],
    title: 'BuiltInConnectorResponse'
} as const;

export const CardDetailsSchema = {
    properties: {
        brand: {
            type: 'string',
            title: 'Brand'
        },
        country: {
            type: 'string',
            title: 'Country'
        },
        display_brand: {
            type: 'string',
            title: 'Display Brand'
        },
        exp_month: {
            type: 'integer',
            title: 'Exp Month'
        },
        exp_year: {
            type: 'integer',
            title: 'Exp Year'
        },
        last4: {
            type: 'string',
            title: 'Last4'
        }
    },
    type: 'object',
    required: ['brand', 'country', 'display_brand', 'exp_month', 'exp_year', 'last4'],
    title: 'CardDetails'
} as const;

export const ChartDataPointSchema = {
    properties: {
        date: {
            type: 'string',
            format: 'date-time',
            title: 'Date'
        },
        value: {
            type: 'number',
            title: 'Value'
        }
    },
    type: 'object',
    required: ['date', 'value'],
    title: 'ChartDataPoint'
} as const;

export const ChartTypeSchema = {
    type: 'string',
    enum: ['line', 'bar', 'pie', 'doughnut', 'area', 'scatter', 'radar', 'step_area'],
    title: 'ChartType',
    description: 'Enum for different types of charts available in the system'
} as const;

export const CheckoutSessionResponseSchema = {
    properties: {
        checkout_session_url: {
            type: 'string',
            title: 'Checkout Session Url'
        }
    },
    type: 'object',
    required: ['checkout_session_url'],
    title: 'CheckoutSessionResponse'
} as const;

export const CitationMetadataSchema = {
    properties: {
        ref_id: {
            type: 'integer',
            title: 'Ref Id'
        },
        doc_name: {
            type: 'string',
            title: 'Doc Name'
        },
        doc_section: {
            type: 'string',
            title: 'Doc Section'
        },
        text_snippet: {
            type: 'string',
            title: 'Text Snippet'
        }
    },
    type: 'object',
    required: ['ref_id', 'doc_name', 'doc_section', 'text_snippet'],
    title: 'CitationMetadata',
    description: 'Metadata for document citations'
} as const;

export const CloudProviderSchema = {
    type: 'string',
    enum: ['AWS'],
    title: 'CloudProvider'
} as const;

export const ConfirmUploadsRequestSchema = {
    properties: {
        uploaded_files: {
            items: {
                '$ref': '#/components/schemas/UploadedFileInfo'
            },
            type: 'array',
            title: 'Uploaded Files',
            description: 'Information about successfully uploaded files'
        }
    },
    type: 'object',
    required: ['uploaded_files'],
    title: 'ConfirmUploadsRequest',
    description: 'Request to confirm file uploads and start ingestion'
} as const;

export const ConnectorCreateSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        type: {
            '$ref': '#/components/schemas/ConnectorType'
        },
        approval: {
            type: 'boolean',
            title: 'Approval',
            default: false
        },
        config: {
            type: 'object',
            title: 'Config',
            default: {}
        }
    },
    type: 'object',
    required: ['name', 'type'],
    title: 'ConnectorCreate'
} as const;

export const ConnectorListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/BuiltInConnector'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'ConnectorList'
} as const;

export const ConnectorResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        type: {
            '$ref': '#/components/schemas/ConnectorType'
        },
        approval: {
            type: 'boolean',
            title: 'Approval',
            default: false
        },
        config: {
            type: 'object',
            title: 'Config'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        created_by: {
            type: 'string',
            format: 'uuid',
            title: 'Created By'
        },
        updated_by: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated By'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'name', 'description', 'type', 'config', 'workspace_id', 'created_by', 'updated_by', 'created_at', 'updated_at'],
    title: 'ConnectorResponse'
} as const;

export const ConnectorTypeSchema = {
    type: 'string',
    enum: ['bedrock_kb', 'open_api'],
    title: 'ConnectorType'
} as const;

export const ConnectorUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        config: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Config'
        },
        approval: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Approval'
        }
    },
    type: 'object',
    title: 'ConnectorUpdate'
} as const;

export const ConnectorWithStatusResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        display_name: {
            type: 'string',
            title: 'Display Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active'
        },
        required_permission: {
            type: 'boolean',
            title: 'Required Permission',
            default: false
        }
    },
    type: 'object',
    required: ['id', 'name', 'display_name', 'is_active'],
    title: 'ConnectorWithStatusResponse',
    description: `Response model for a connector with its active status and permission settings in a workspace.

Attributes:
    id: Unique identifier for the workspace-connector association
    name: Unique name of the connector
    display_name: Human-readable name for the connector
    description: Optional description of the connector
    is_active: Whether the connector is active in this workspace
    required_permission: Whether this tool requires human approval before execution`
} as const;

export const ConversationCreateRequestSchema = {
    properties: {
        agent_id: {
            type: 'string',
            format: 'uuid4',
            title: 'Agent Id'
        },
        model_provider: {
            type: 'string',
            title: 'Model Provider',
            default: 'bedrock'
        },
        resource_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid4'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Resource Id'
        },
        instructions: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Instructions'
        }
    },
    type: 'object',
    required: ['agent_id'],
    title: 'ConversationCreateRequest'
} as const;

export const ConversationPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        model_provider: {
            type: 'string',
            title: 'Model Provider'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'agent_id', 'name', 'model_provider', 'created_at'],
    title: 'ConversationPublic'
} as const;

export const ConversationsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ConversationPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'ConversationsPublic',
    description: `Response model for paginated conversations list.

Attributes:
    data: List of conversation items
    count: Total number of items available (before pagination)`
} as const;

export const DailyMessageVolumeSchema = {
    properties: {
        date: {
            type: 'string',
            format: 'date-time',
            title: 'Date'
        },
        message_count: {
            type: 'integer',
            title: 'Message Count'
        }
    },
    type: 'object',
    required: ['date', 'message_count'],
    title: 'DailyMessageVolume'
} as const;

export const DailyTokenUsageSchema = {
    properties: {
        date: {
            type: 'string',
            format: 'date-time',
            title: 'Date'
        },
        total_tokens: {
            type: 'integer',
            title: 'Tokens'
        }
    },
    type: 'object',
    required: ['date', 'total_tokens'],
    title: 'DailyTokenUsage'
} as const;

export const DocumentSchema = {
    properties: {
        id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Id'
        },
        metadata: {
            type: 'object',
            title: 'Metadata'
        },
        page_content: {
            type: 'string',
            title: 'Page Content'
        },
        type: {
            type: 'string',
            const: 'Document',
            title: 'Type',
            default: 'Document'
        }
    },
    type: 'object',
    required: ['page_content'],
    title: 'Document',
    description: `Class for storing a piece of text and associated metadata.

Example:

    .. code-block:: python

        from langchain_core.documents import Document

        document = Document(
            page_content="Hello, world!",
            metadata={"source": "https://example.com"}
        )`
} as const;

export const DocumentKBReadSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 0,
            title: 'Name'
        },
        type: {
            '$ref': '#/components/schemas/DocumentType'
        },
        url: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Url'
        },
        deep_crawl: {
            type: 'boolean',
            title: 'Deep Crawl',
            default: false
        },
        file_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Name'
        },
        file_type: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Type'
        },
        object_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Object Name'
        },
        embed_status: {
            '$ref': '#/components/schemas/AsyncTaskStatus',
            default: 'PENDING'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        kb_id: {
            type: 'string',
            format: 'uuid',
            title: 'Kb Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted'
        },
        parent_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Parent Id'
        },
        children: {
            items: {
                '$ref': '#/components/schemas/DocumentKBRead'
            },
            type: 'array',
            title: 'Children',
            default: []
        }
    },
    type: 'object',
    required: ['name', 'type', 'id', 'kb_id', 'created_at', 'updated_at', 'is_deleted'],
    title: 'DocumentKBRead'
} as const;

export const DocumentTypeSchema = {
    type: 'string',
    enum: ['url', 'file'],
    title: 'DocumentType'
} as const;

export const DocumentsKBReadSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/DocumentKBRead'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'DocumentsKBRead'
} as const;

export const EnterpriseEnquiryMessageResponseSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        }
    },
    type: 'object',
    required: ['message'],
    title: 'EnterpriseEnquiryMessageResponse',
    description: 'Response model for enterprise enquiry status messages'
} as const;

export const EnterpriseEnquiryRequestSchema = {
    properties: {
        first_name: {
            type: 'string',
            title: 'First Name'
        },
        last_name: {
            type: 'string',
            title: 'Last Name'
        },
        work_title: {
            type: 'string',
            title: 'Work Title'
        },
        work_email: {
            type: 'string',
            title: 'Work Email'
        },
        company_name: {
            type: 'string',
            title: 'Company Name'
        },
        estimated_monthly_cost: {
            type: 'string',
            title: 'Estimated Monthly Cost'
        },
        message: {
            type: 'string',
            title: 'Message'
        },
        product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Product Id'
        }
    },
    type: 'object',
    required: ['first_name', 'last_name', 'work_title', 'work_email', 'company_name', 'estimated_monthly_cost', 'message', 'product_id'],
    title: 'EnterpriseEnquiryRequest'
} as const;

export const ErrorResponseSchema = {
    properties: {
        error: {
            type: 'string',
            title: 'Error'
        },
        details: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Details'
        }
    },
    type: 'object',
    required: ['error'],
    title: 'ErrorResponse'
} as const;

export const FeedbackTypeSchema = {
    type: 'string',
    enum: ['good', 'bad'],
    title: 'FeedbackType',
    description: 'Enumeration for feedback types on agent responses.'
} as const;

export const FileInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side ID for tracking this file'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Original filename'
        },
        content_type: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Content Type',
            description: 'File MIME type'
        },
        file_size: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Size',
            description: 'File size in bytes'
        }
    },
    type: 'object',
    required: ['file_id', 'filename'],
    title: 'FileInfo',
    description: 'Information about a file to generate a presigned URL for'
} as const;

export const HTTPValidationErrorSchema = {
    properties: {
        detail: {
            items: {
                '$ref': '#/components/schemas/ValidationError'
            },
            type: 'array',
            title: 'Detail'
        }
    },
    type: 'object',
    title: 'HTTPValidationError'
} as const;

export const InvoiceResponseSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        customer: {
            type: 'string',
            title: 'Customer'
        },
        status: {
            type: 'string',
            title: 'Status'
        },
        amount_due: {
            type: 'integer',
            title: 'Amount Due'
        },
        amount_paid: {
            type: 'integer',
            title: 'Amount Paid'
        },
        amount_remaining: {
            type: 'integer',
            title: 'Amount Remaining'
        },
        currency: {
            type: 'string',
            title: 'Currency'
        },
        invoice_pdf: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Invoice Pdf'
        },
        created: {
            type: 'integer',
            title: 'Created'
        },
        due_date: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Due Date'
        },
        paid: {
            type: 'boolean',
            title: 'Paid'
        },
        payment_intent: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Payment Intent'
        },
        subscription: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Subscription'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['id', 'customer', 'status', 'amount_due', 'amount_paid', 'amount_remaining', 'currency', 'created', 'paid', 'total'],
    title: 'InvoiceResponse'
} as const;

export const ItemSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        }
    },
    type: 'object',
    required: ['title'],
    title: 'Item'
} as const;

export const ItemCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        }
    },
    type: 'object',
    required: ['title'],
    title: 'ItemCreate'
} as const;

export const ItemPublicSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        }
    },
    type: 'object',
    required: ['title', 'id', 'owner_id'],
    title: 'ItemPublic'
} as const;

export const ItemUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    minLength: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        }
    },
    type: 'object',
    title: 'ItemUpdate'
} as const;

export const ItemsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ItemPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'ItemsPublic'
} as const;

export const KBAccessLevelSchema = {
    type: 'string',
    enum: ['private', 'shared'],
    title: 'KBAccessLevel'
} as const;

export const KBCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        access_level: {
            '$ref': '#/components/schemas/KBAccessLevel',
            default: 'private'
        },
        usage_mode: {
            '$ref': '#/components/schemas/KBUsageMode',
            default: 'manual'
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags',
            default: []
        },
        allowed_users: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Allowed Users',
            default: []
        }
    },
    type: 'object',
    required: ['title'],
    title: 'KBCreate'
} as const;

export const KBReadSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        access_level: {
            '$ref': '#/components/schemas/KBAccessLevel'
        },
        usage_mode: {
            '$ref': '#/components/schemas/KBUsageMode'
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags',
            default: []
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted'
        },
        allowed_users: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Allowed Users'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        }
    },
    type: 'object',
    required: ['title', 'access_level', 'usage_mode', 'id', 'created_at', 'updated_at', 'is_deleted', 'owner_id'],
    title: 'KBRead'
} as const;

export const KBUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        access_level: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/KBAccessLevel'
                },
                {
                    type: 'null'
                }
            ]
        },
        tags: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tags'
        },
        allowed_users: {
            anyOf: [
                {
                    items: {
                        type: 'string',
                        format: 'uuid'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Allowed Users'
        },
        usage_mode: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/KBUsageMode'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'KBUpdate'
} as const;

export const KBUsageModeSchema = {
    type: 'string',
    enum: ['manual', 'agent_requested', 'always'],
    title: 'KBUsageMode'
} as const;

export const KBsReadSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/KBRead'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'KBsRead'
} as const;

export const MCPConfigCreateSchema = {
    properties: {
        config: {
            type: 'object',
            title: 'Config'
        }
    },
    type: 'object',
    required: ['config'],
    title: 'MCPConfigCreate'
} as const;

export const MCPConfigResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        config: {
            type: 'object',
            title: 'Config'
        },
        created_at: {
            title: 'Created At'
        },
        updated_at: {
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'workspace_id', 'config', 'created_at', 'updated_at'],
    title: 'MCPConfigResponse'
} as const;

export const MCPServerInfoSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        },
        type: {
            '$ref': '#/components/schemas/MCPServerTransport'
        },
        tool_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool List'
        },
        resource_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Resource List'
        },
        connected: {
            type: 'boolean',
            title: 'Connected',
            default: false
        },
        connection_error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Connection Error'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: true
        },
        is_builtin: {
            type: 'boolean',
            title: 'Is Builtin',
            description: 'Whether the server is built-in and cannot be edited',
            default: false
        },
        tools_permissions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tools Permissions',
            description: 'Permissions for the tools of the server',
            default: []
        }
    },
    type: 'object',
    required: ['name', 'type', 'tool_list', 'resource_list'],
    title: 'MCPServerInfo',
    description: 'Basic information about an MCP server'
} as const;

export const MCPServerListResponseSchema = {
    properties: {
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        servers: {
            items: {
                anyOf: [
                    {
                        '$ref': '#/components/schemas/MCPSseServerResponse'
                    },
                    {
                        '$ref': '#/components/schemas/MCPStdioServerResponse'
                    }
                ]
            },
            type: 'array',
            title: 'Servers'
        }
    },
    type: 'object',
    required: ['workspace_id', 'servers'],
    title: 'MCPServerListResponse',
    description: 'Response model for listing all MCP servers'
} as const;

export const MCPServerTransportSchema = {
    type: 'string',
    enum: ['stdio', 'sse'],
    title: 'MCPServerTransport',
    description: 'Transport types for MCP servers'
} as const;

export const MCPSseServerResponseSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        },
        type: {
            '$ref': '#/components/schemas/MCPServerTransport'
        },
        tool_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool List'
        },
        resource_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Resource List'
        },
        connected: {
            type: 'boolean',
            title: 'Connected',
            default: false
        },
        connection_error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Connection Error'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: true
        },
        is_builtin: {
            type: 'boolean',
            title: 'Is Builtin',
            description: 'Whether the server is built-in and cannot be edited',
            default: false
        },
        tools_permissions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tools Permissions',
            description: 'Permissions for the tools of the server',
            default: []
        },
        url: {
            type: 'string',
            title: 'Url'
        },
        headers: {
            additionalProperties: {
                type: 'string'
            },
            type: 'object',
            title: 'Headers'
        }
    },
    type: 'object',
    required: ['name', 'type', 'tool_list', 'resource_list', 'url'],
    title: 'MCPSseServerResponse',
    description: 'Response model for SSE transport server'
} as const;

export const MCPStdioServerResponseSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        },
        type: {
            '$ref': '#/components/schemas/MCPServerTransport'
        },
        tool_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool List'
        },
        resource_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Resource List'
        },
        connected: {
            type: 'boolean',
            title: 'Connected',
            default: false
        },
        connection_error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Connection Error'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: true
        },
        is_builtin: {
            type: 'boolean',
            title: 'Is Builtin',
            description: 'Whether the server is built-in and cannot be edited',
            default: false
        },
        tools_permissions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tools Permissions',
            description: 'Permissions for the tools of the server',
            default: []
        },
        command: {
            type: 'string',
            title: 'Command'
        },
        args: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Args'
        }
    },
    type: 'object',
    required: ['name', 'type', 'tool_list', 'resource_list', 'command', 'args'],
    title: 'MCPStdioServerResponse',
    description: 'Response model for stdio transport server'
} as const;

export const MemorySchema = {
    properties: {
        tags: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tags',
            metadata: {
                description: 'Tags extracted from the conversation.'
            }
        },
        task: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Task',
            metadata: {
                description: 'A specific task or problem (error, issue, question, etc.) that was addressed.'
            }
        },
        solution: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Solution',
            metadata: {
                description: 'Detailed solution or approach to solve the task or problem.'
            }
        },
        links: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Links',
            metadata: {
                description: 'References to related memories'
            }
        },
        id: {
            type: 'string',
            title: 'Id'
        },
        agent_role: {
            type: 'string',
            title: 'Agent Role'
        }
    },
    type: 'object',
    required: ['id', 'agent_role'],
    title: 'Memory'
} as const;

export const MemoryFilterSchema = {
    properties: {
        agent_roles: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Agent Roles'
        },
        limit: {
            type: 'integer',
            title: 'Limit',
            default: 50
        }
    },
    type: 'object',
    title: 'MemoryFilter'
} as const;

export const MemoryNodeSchema = {
    properties: {
        tags: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tags',
            metadata: {
                description: 'Tags extracted from the conversation.'
            }
        },
        task: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Task',
            metadata: {
                description: 'A specific task or problem (error, issue, question, etc.) that was addressed.'
            }
        },
        solution: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Solution',
            metadata: {
                description: 'Detailed solution or approach to solve the task or problem.'
            }
        },
        links: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Links',
            metadata: {
                description: 'References to related memories'
            }
        }
    },
    type: 'object',
    title: 'MemoryNode',
    description: 'Model for a memory node extracted from a conversation.'
} as const;

export const MemoryUpdateSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        agent_role: {
            type: 'string',
            title: 'Agent Role'
        },
        memory: {
            '$ref': '#/components/schemas/MemoryNode'
        }
    },
    type: 'object',
    required: ['id', 'agent_role', 'memory'],
    title: 'MemoryUpdate'
} as const;

export const MemorysReadSchema = {
    properties: {
        memories: {
            items: {
                '$ref': '#/components/schemas/Memory'
            },
            type: 'array',
            title: 'Memories'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['memories', 'count'],
    title: 'MemorysRead'
} as const;

export const MessageSchema = {
    properties: {
        content: {
            type: 'string',
            title: 'Content'
        },
        role: {
            type: 'string',
            maxLength: 50,
            title: 'Role',
            default: 'user'
        },
        is_interrupt: {
            type: 'boolean',
            title: 'Is Interrupt',
            default: false
        },
        interrupt_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Interrupt Message'
        },
        action_type: {
            '$ref': '#/components/schemas/MessageActionType',
            default: 'none'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        message_metadata: {
            type: 'object',
            title: 'Message Metadata',
            default: {}
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        }
    },
    type: 'object',
    required: ['content', 'conversation_id'],
    title: 'Message'
} as const;

export const MessageActionTypeSchema = {
    type: 'string',
    enum: ['none', 'recommendation'],
    title: 'MessageActionType',
    description: 'Enum for the type of action that can be taken by the message'
} as const;

export const MessageDisplayComponentPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        type: {
            '$ref': '#/components/schemas/MessageDisplayComponentType'
        },
        chart_type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/ChartType'
                },
                {
                    type: 'null'
                }
            ]
        },
        title: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        data: {
            type: 'object',
            title: 'Data'
        },
        config: {
            type: 'object',
            title: 'Config'
        },
        position: {
            type: 'integer',
            title: 'Position'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'type', 'chart_type', 'title', 'description', 'data', 'config', 'position', 'created_at'],
    title: 'MessageDisplayComponentPublic',
    description: 'Public schema for message display components'
} as const;

export const MessageDisplayComponentTypeSchema = {
    type: 'string',
    enum: ['table', 'chart'],
    title: 'MessageDisplayComponentType',
    description: 'Enum for display component types (currently supporting only tables and charts)'
} as const;

export const MessageFeedbackCreateSchema = {
    properties: {
        feedback_type: {
            '$ref': '#/components/schemas/FeedbackType',
            description: 'Type of feedback (good/bad)'
        },
        reason: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Reason',
            description: 'Optional reason for the feedback, required when feedback_type is BAD'
        },
        additional_comments: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 2000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Additional Comments',
            description: 'Additional optional comments from the user'
        },
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message Id'
        }
    },
    type: 'object',
    required: ['feedback_type', 'message_id'],
    title: 'MessageFeedbackCreate',
    description: 'Schema for creating message feedback'
} as const;

export const MessageFeedbackPublicSchema = {
    properties: {
        feedback_type: {
            '$ref': '#/components/schemas/FeedbackType',
            description: 'Type of feedback (good/bad)'
        },
        reason: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Reason',
            description: 'Optional reason for the feedback, required when feedback_type is BAD'
        },
        additional_comments: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 2000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Additional Comments',
            description: 'Additional optional comments from the user'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message Id'
        },
        user_id: {
            type: 'string',
            format: 'uuid',
            title: 'User Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['feedback_type', 'id', 'message_id', 'user_id', 'created_at', 'updated_at'],
    title: 'MessageFeedbackPublic',
    description: 'Public schema for message feedback responses'
} as const;

export const MessageFeedbackUpdateSchema = {
    properties: {
        feedback_type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/FeedbackType'
                },
                {
                    type: 'null'
                }
            ]
        },
        reason: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Reason'
        },
        additional_comments: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Additional Comments'
        }
    },
    type: 'object',
    title: 'MessageFeedbackUpdate',
    description: 'Schema for updating message feedback'
} as const;

export const MessageHistoryPublicSchema = {
    properties: {
        limit: {
            type: 'integer',
            title: 'Limit'
        },
        has_more: {
            type: 'boolean',
            title: 'Has More'
        },
        data: {
            items: {
                type: 'object'
            },
            type: 'array',
            title: 'Data',
            description: 'list of messages with agent thoughts. Each message contains: id, message_id, position, thought, tool, tool_input, created_at, observation',
            default: []
        }
    },
    type: 'object',
    required: ['limit', 'has_more'],
    title: 'MessageHistoryPublic'
} as const;

export const MessagePublicSchema = {
    properties: {
        content: {
            type: 'string',
            title: 'Content'
        },
        resume: {
            type: 'boolean',
            title: 'Resume'
        },
        approve: {
            type: 'boolean',
            title: 'Approve'
        },
        restore: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Restore'
        },
        message_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Message Id'
        },
        action_type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/MessageActionType'
                },
                {
                    type: 'null'
                }
            ]
        },
        display_components: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/MessageDisplayComponentPublic'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Display Components'
        }
    },
    type: 'object',
    required: ['content', 'resume', 'approve'],
    title: 'MessagePublic'
} as const;

export const MessageStatisticsSchema = {
    properties: {
        total_messages: {
            type: 'integer',
            title: 'Total Messages'
        },
        average_response_time: {
            type: 'number',
            title: 'Average Response Time'
        },
        average_input_tokens_per_message: {
            type: 'number',
            title: 'Average Input Tokens per Message'
        },
        average_output_tokens_per_message: {
            type: 'number',
            title: 'Average Output Tokens per Message'
        },
        daily_message_volume: {
            items: {
                '$ref': '#/components/schemas/DailyMessageVolume'
            },
            type: 'array',
            title: 'Daily Message Volume'
        },
        token_distribution_by_message_length: {
            items: {
                '$ref': '#/components/schemas/TokenDistributionCategory'
            },
            type: 'array',
            title: 'Token Distribution by Message Length'
        }
    },
    type: 'object',
    required: ['total_messages', 'average_response_time', 'average_input_tokens_per_message', 'average_output_tokens_per_message', 'daily_message_volume', 'token_distribution_by_message_length'],
    title: 'MessageStatistics'
} as const;

export const MetricCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 100,
            title: 'Name'
        },
        value: {
            type: 'number',
            title: 'Value'
        },
        unit: {
            type: 'string',
            maxLength: 50,
            title: 'Unit'
        },
        timestamp: {
            type: 'string',
            format: 'date-time',
            title: 'Timestamp'
        },
        type: {
            '$ref': '#/components/schemas/MetricType'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['name', 'value', 'unit', 'timestamp', 'type', 'resource_id'],
    title: 'MetricCreate'
} as const;

export const MetricPublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 100,
            title: 'Name'
        },
        value: {
            type: 'number',
            title: 'Value'
        },
        unit: {
            type: 'string',
            maxLength: 50,
            title: 'Unit'
        },
        timestamp: {
            type: 'string',
            format: 'date-time',
            title: 'Timestamp'
        },
        type: {
            '$ref': '#/components/schemas/MetricType'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['name', 'value', 'unit', 'timestamp', 'type', 'id', 'resource_id'],
    title: 'MetricPublic'
} as const;

export const MetricReadSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 100,
            title: 'Name'
        },
        value: {
            type: 'number',
            title: 'Value'
        },
        unit: {
            type: 'string',
            maxLength: 50,
            title: 'Unit'
        },
        timestamp: {
            type: 'string',
            format: 'date-time',
            title: 'Timestamp'
        },
        type: {
            '$ref': '#/components/schemas/MetricType'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['name', 'value', 'unit', 'timestamp', 'type', 'id', 'resource_id'],
    title: 'MetricRead'
} as const;

export const MetricTypeSchema = {
    type: 'string',
    enum: ['usage', 'performance', 'cost'],
    title: 'MetricType'
} as const;

export const MetricUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 100
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        value: {
            anyOf: [
                {
                    type: 'number'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Value'
        },
        unit: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 50
                },
                {
                    type: 'null'
                }
            ],
            title: 'Unit'
        },
        timestamp: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Timestamp'
        },
        type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/MetricType'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'MetricUpdate'
} as const;

export const MetricsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/MetricPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'MetricsPublic'
} as const;

export const ModuleSettingSchema = {
    properties: {
        key: {
            type: 'string',
            title: 'Key'
        },
        value: {
            type: 'object',
            title: 'Value',
            default: {}
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['key'],
    title: 'ModuleSetting'
} as const;

export const NewPasswordSchema = {
    properties: {
        token: {
            type: 'string',
            title: 'Token'
        },
        new_password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'New Password'
        }
    },
    type: 'object',
    required: ['token', 'new_password'],
    title: 'NewPassword'
} as const;

export const NodeTypeSchema = {
    type: 'string',
    enum: ['start', 'tool', 'human_in_loop', 'end', 'output'],
    title: 'NodeType'
} as const;

export const NotificationListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/NotificationResponse'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'NotificationList',
    description: `Response model for paginated notifications list.

Attributes:
    data: List of notification items
    count: Total number of items available (before pagination)`
} as const;

export const NotificationResponseSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        message: {
            type: 'string',
            title: 'Message'
        },
        type: {
            '$ref': '#/components/schemas/NotificationType',
            default: 'info'
        },
        status: {
            '$ref': '#/components/schemas/NotificationStatus',
            default: 'unread'
        },
        notification_metadata: {
            type: 'object',
            title: 'Notification Metadata',
            description: 'Metadata for the notification'
        },
        requires_action: {
            type: 'boolean',
            title: 'Requires Action',
            default: false
        },
        action_url: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Action Url',
            description: 'URL for direct action'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        user_id: {
            type: 'string',
            format: 'uuid',
            title: 'User Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        read_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Read At'
        },
        expires_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Expires At'
        }
    },
    type: 'object',
    required: ['title', 'message', 'id', 'user_id', 'created_at', 'updated_at'],
    title: 'NotificationResponse'
} as const;

export const NotificationStatusSchema = {
    type: 'string',
    enum: ['unread', 'read', 'archived'],
    title: 'NotificationStatus'
} as const;

export const NotificationTypeSchema = {
    type: 'string',
    enum: ['info', 'warning', 'error', 'interrupt'],
    title: 'NotificationType'
} as const;

export const PaymentMethodResponseSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        billing_details: {
            '$ref': '#/components/schemas/BillingDetails'
        },
        card: {
            '$ref': '#/components/schemas/CardDetails'
        },
        created: {
            type: 'integer',
            title: 'Created'
        },
        customer: {
            type: 'string',
            title: 'Customer'
        },
        livemode: {
            type: 'boolean',
            title: 'Livemode'
        },
        type: {
            type: 'string',
            title: 'Type'
        }
    },
    type: 'object',
    required: ['id', 'billing_details', 'card', 'created', 'customer', 'livemode', 'type'],
    title: 'PaymentMethodResponse'
} as const;

export const PlanChangeRequestCreateSchema = {
    properties: {
        first_name: {
            type: 'string',
            title: 'First Name'
        },
        last_name: {
            type: 'string',
            title: 'Last Name'
        },
        work_email: {
            type: 'string',
            title: 'Work Email'
        },
        work_title: {
            type: 'string',
            title: 'Work Title'
        },
        company_name: {
            type: 'string',
            title: 'Company Name'
        },
        reason: {
            type: 'string',
            title: 'Reason'
        },
        current_product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Current Product Id'
        },
        requested_price_id: {
            type: 'string',
            format: 'uuid',
            title: 'Requested Price Id'
        }
    },
    type: 'object',
    required: ['first_name', 'last_name', 'work_email', 'work_title', 'company_name', 'reason', 'current_product_id', 'requested_price_id'],
    title: 'PlanChangeRequestCreate'
} as const;

export const PlanChangeRequestResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        first_name: {
            type: 'string',
            title: 'First Name'
        },
        last_name: {
            type: 'string',
            title: 'Last Name'
        },
        work_email: {
            type: 'string',
            title: 'Work Email'
        },
        work_title: {
            type: 'string',
            title: 'Work Title'
        },
        company_name: {
            type: 'string',
            title: 'Company Name'
        },
        reason: {
            type: 'string',
            title: 'Reason'
        },
        status: {
            type: 'string',
            title: 'Status'
        },
        customer_id: {
            type: 'string',
            format: 'uuid',
            title: 'Customer Id'
        },
        current_product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Current Product Id'
        },
        requested_product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Requested Product Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'first_name', 'last_name', 'work_email', 'work_title', 'company_name', 'reason', 'status', 'customer_id', 'current_product_id', 'requested_product_id', 'created_at', 'updated_at'],
    title: 'PlanChangeRequestResponse'
} as const;

export const PresignedUrlInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side ID for tracking this file'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Original filename'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'Storage key for the file'
        },
        presigned_url: {
            type: 'string',
            title: 'Presigned Url',
            description: 'Presigned URL for file upload'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'storage_key', 'presigned_url'],
    title: 'PresignedUrlInfo',
    description: 'Information about a generated presigned URL'
} as const;

export const PresignedUrlRequestSchema = {
    properties: {
        kb_id: {
            type: 'string',
            title: 'Kb Id',
            description: 'ID of the knowledge base to upload files to'
        },
        files: {
            items: {
                '$ref': '#/components/schemas/FileInfo'
            },
            type: 'array',
            title: 'Files',
            description: 'Information about files to generate presigned URLs for'
        }
    },
    type: 'object',
    required: ['kb_id', 'files'],
    title: 'PresignedUrlRequest',
    description: 'Request to generate presigned URLs for file uploads'
} as const;

export const PresignedUrlResponseSchema = {
    properties: {
        kb_id: {
            type: 'string',
            title: 'Kb Id',
            description: 'Knowledge base ID'
        },
        presigned_urls: {
            items: {
                '$ref': '#/components/schemas/PresignedUrlInfo'
            },
            type: 'array',
            title: 'Presigned Urls',
            description: 'Generated presigned URLs'
        }
    },
    type: 'object',
    required: ['kb_id', 'presigned_urls'],
    title: 'PresignedUrlResponse',
    description: 'Response with presigned URLs for file uploads'
} as const;

export const PriceResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        stripe_price_id: {
            type: 'string',
            title: 'Stripe Price Id'
        },
        product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Product Id'
        },
        active: {
            type: 'boolean',
            title: 'Active'
        },
        amount: {
            type: 'number',
            title: 'Amount'
        },
        currency: {
            type: 'string',
            title: 'Currency'
        },
        interval: {
            type: 'string',
            title: 'Interval'
        }
    },
    type: 'object',
    required: ['id', 'stripe_price_id', 'product_id', 'active', 'amount', 'currency', 'interval'],
    title: 'PriceResponse'
} as const;

export const ProductResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        stripe_product_id: {
            type: 'string',
            title: 'Stripe Product Id'
        },
        active: {
            type: 'boolean',
            title: 'Active'
        },
        prices: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/PriceResponse'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Prices',
            default: []
        },
        quota_definition: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/QuotaDefinitionResponse'
                },
                {
                    type: 'null'
                }
            ]
        },
        is_custom: {
            type: 'boolean',
            title: 'Is Custom'
        }
    },
    type: 'object',
    required: ['id', 'name', 'description', 'stripe_product_id', 'active', 'is_custom'],
    title: 'ProductResponse'
} as const;

export const QuotaDefinitionResponseSchema = {
    properties: {
        max_workspaces: {
            type: 'integer',
            title: 'Max Workspaces'
        },
        max_members_per_workspace: {
            type: 'integer',
            title: 'Max Members Per Workspace'
        },
        max_fast_requests_per_month: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Max Fast Requests Per Month'
        }
    },
    type: 'object',
    required: ['max_workspaces', 'max_members_per_workspace', 'max_fast_requests_per_month'],
    title: 'QuotaDefinitionResponse'
} as const;

export const QuotaInfoSchema = {
    properties: {
        quota_used: {
            type: 'integer',
            title: 'Quota Used'
        },
        quota_limit: {
            type: 'integer',
            title: 'Quota Limit'
        },
        quota_remaining: {
            type: 'integer',
            title: 'Quota Remaining'
        },
        usage_percentage: {
            type: 'number',
            title: 'Usage Percentage'
        }
    },
    type: 'object',
    required: ['quota_used', 'quota_limit', 'quota_remaining', 'usage_percentage'],
    title: 'QuotaInfo'
} as const;

export const RecommendationCreateSchema = {
    properties: {
        type: {
            '$ref': '#/components/schemas/RecommendationType'
        },
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        potential_savings: {
            type: 'number',
            title: 'Potential Savings'
        },
        effort: {
            type: 'string',
            maxLength: 50,
            title: 'Effort'
        },
        risk: {
            type: 'string',
            maxLength: 50,
            title: 'Risk'
        },
        status: {
            '$ref': '#/components/schemas/RecommendationStatus',
            default: 'pending'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['type', 'title', 'description', 'potential_savings', 'effort', 'risk', 'resource_id'],
    title: 'RecommendationCreate'
} as const;

export const RecommendationOveralPublicSchema = {
    properties: {
        total_resource_scanned: {
            type: 'integer',
            title: 'Total Resource Scanned'
        },
        total_well_optimized: {
            type: 'integer',
            title: 'Total Well Optimized'
        },
        total_optimization_opportunities: {
            type: 'integer',
            title: 'Total Optimization Opportunities'
        },
        total_estimated_saving_amount: {
            type: 'number',
            title: 'Total Estimated Saving Amount'
        }
    },
    type: 'object',
    required: ['total_resource_scanned', 'total_well_optimized', 'total_optimization_opportunities', 'total_estimated_saving_amount'],
    title: 'RecommendationOveralPublic'
} as const;

export const RecommendationPublicSchema = {
    properties: {
        type: {
            '$ref': '#/components/schemas/RecommendationType'
        },
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        potential_savings: {
            type: 'number',
            title: 'Potential Savings'
        },
        effort: {
            type: 'string',
            maxLength: 50,
            title: 'Effort'
        },
        risk: {
            type: 'string',
            maxLength: 50,
            title: 'Risk'
        },
        status: {
            '$ref': '#/components/schemas/RecommendationStatus',
            default: 'pending'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        },
        resource: {
            '$ref': '#/components/schemas/ResourcePublic'
        }
    },
    type: 'object',
    required: ['type', 'title', 'description', 'potential_savings', 'effort', 'risk', 'id', 'resource_id', 'resource'],
    title: 'RecommendationPublic'
} as const;

export const RecommendationStatusSchema = {
    type: 'string',
    enum: ['pending', 'implemented', 'ignored', 'in_progress'],
    title: 'RecommendationStatus'
} as const;

export const RecommendationTypeSchema = {
    type: 'string',
    enum: ['instance_rightsizing', 'autoscaling_optimization', 'auto_start_stop_optimization', 'volume_optimization', 'snapshot_cleanup', 'reserved_instance_recommendation', 'savings_plan_recommendation', 'spot_instance_usage', 'idle_resource_cleanup', 'unused_eip_cleanup', 'orphaned_snapshot_cleanup', 'underutilized_ebs_cleanup', 'serverless_migration', 'container_adoption', 'multi_az_optimization', 'data_transfer_optimization', 'cloudfront_optimization', 'nat_gateway_optimization', 'rds_optimization', 'redshift_optimization', 'dynamodb_optimization', 's3_storage_class_optimization', 'lambda_optimization', 'tagging_improvement', 'cost_allocation_improvement', 'cost_anomaly_detection', 'budget_alert_setup', 'cost_explorer_usage', 'modernize_legacy_services', 'migrate_to_graviton', 'compliance_optimization', 'governance_improvement', 'cross_region_optimization', 'cross_account_optimization', 'predictive_scaling', 'ai_driven_optimization', 'quantum_computing_readiness', 'carbon_footprint_reduction', 'renewable_energy_usage', 'marketplace_alternative', 'third_party_tool_recommendation', 'custom_optimization', 'other', 'ec2_fleet_optimization', 'spot_fleet_optimization', 'graviton_migration', 'predictive_scaling_optimization', 'instance_connect_endpoint'],
    title: 'RecommendationType'
} as const;

export const RecommendationUpdateSchema = {
    properties: {
        type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/RecommendationType'
                },
                {
                    type: 'null'
                }
            ]
        },
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        potential_savings: {
            anyOf: [
                {
                    type: 'number'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Potential Savings'
        },
        effort: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 50
                },
                {
                    type: 'null'
                }
            ],
            title: 'Effort'
        },
        risk: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 50
                },
                {
                    type: 'null'
                }
            ],
            title: 'Risk'
        },
        status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/RecommendationStatus'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'RecommendationUpdate'
} as const;

export const RecommendationsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/RecommendationPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'RecommendationsPublic'
} as const;

export const ResendActivationRequestSchema = {
    properties: {
        email: {
            type: 'string',
            format: 'email',
            title: 'Email'
        },
        captcha_token: {
            type: 'string',
            title: 'Captcha Token'
        }
    },
    type: 'object',
    required: ['email', 'captcha_token'],
    title: 'ResendActivationRequest'
} as const;

export const ResourceCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        arn: {
            type: 'string',
            maxLength: 2048,
            title: 'Arn'
        },
        tags: {
            type: 'object',
            title: 'Tags',
            default: {}
        },
        configurations: {
            type: 'object',
            title: 'Configurations',
            default: {}
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        type: {
            type: 'string',
            title: 'Type'
        },
        region: {
            type: 'string',
            title: 'Region'
        },
        status: {
            '$ref': '#/components/schemas/ResourceStatus',
            default: 'found'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        }
    },
    type: 'object',
    required: ['name', 'arn', 'type', 'region', 'workspace_id'],
    title: 'ResourceCreate'
} as const;

export const ResourcePublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        arn: {
            type: 'string',
            maxLength: 2048,
            title: 'Arn'
        },
        tags: {
            type: 'object',
            title: 'Tags',
            default: {}
        },
        configurations: {
            type: 'object',
            title: 'Configurations',
            default: {}
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        type: {
            type: 'string',
            title: 'Type'
        },
        region: {
            type: 'string',
            title: 'Region'
        },
        status: {
            '$ref': '#/components/schemas/ResourceStatus',
            default: 'found'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace: {
            '$ref': '#/components/schemas/WorkspacePublic'
        },
        total_recommendation: {
            type: 'integer',
            title: 'Total Recommendation'
        },
        total_potential_saving: {
            type: 'number',
            title: 'Total Potential Saving'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['name', 'arn', 'type', 'region', 'id', 'workspace', 'total_recommendation', 'total_potential_saving', 'updated_at'],
    title: 'ResourcePublic'
} as const;

export const ResourceReadSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        arn: {
            type: 'string',
            maxLength: 2048,
            title: 'Arn'
        },
        tags: {
            additionalProperties: {
                type: 'string'
            },
            type: 'object',
            title: 'Tags'
        },
        configurations: {
            type: 'object',
            title: 'Configurations'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        type: {
            type: 'string',
            title: 'Type'
        },
        region: {
            type: 'string',
            title: 'Region'
        },
        status: {
            '$ref': '#/components/schemas/ResourceStatus',
            default: 'found'
        },
        recommendations: {
            items: {
                '$ref': '#/components/schemas/RecommendationPublic'
            },
            type: 'array',
            title: 'Recommendations'
        },
        metrics: {
            anyOf: [
                {
                    additionalProperties: {
                        items: {
                            '$ref': '#/components/schemas/MetricRead'
                        },
                        type: 'array'
                    },
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Metrics'
        }
    },
    type: 'object',
    required: ['name', 'arn', 'tags', 'configurations', 'type', 'region', 'recommendations'],
    title: 'ResourceRead'
} as const;

export const ResourceSavingsReportSchema = {
    properties: {
        rds_savings: {
            items: {
                '$ref': '#/components/schemas/ChartDataPoint'
            },
            type: 'array',
            title: 'Rds Savings'
        },
        ec2_savings: {
            items: {
                '$ref': '#/components/schemas/ChartDataPoint'
            },
            type: 'array',
            title: 'Ec2 Savings'
        },
        total_rds_savings: {
            type: 'number',
            title: 'Total Rds Savings'
        },
        total_ec2_savings: {
            type: 'number',
            title: 'Total Ec2 Savings'
        }
    },
    type: 'object',
    required: ['rds_savings', 'ec2_savings', 'total_rds_savings', 'total_ec2_savings'],
    title: 'ResourceSavingsReport'
} as const;

export const ResourceStatusSchema = {
    type: 'string',
    enum: ['stopped', 'starting', 'running', 'found', 'deleted'],
    title: 'ResourceStatus'
} as const;

export const ResourceTypeSchema = {
    type: 'string',
    enum: ['EC2', 'LAMBDA', 'ECS', 'EKS', 'BATCH', 'EC2_AUTO_SCALING', 'ELASTIC_BEANSTALK', 'APP_RUNNER', 'RDS', 'DYNAMODB', 'ELASTICACHE', 'NEPTUNE', 'DOCUMENTDB', 'OPENSEARCH', 'REDSHIFT', 'S3', 'EBS', 'EFS', 'BACKUP', 'VPC', 'ELB', 'CLOUDFORMATION', 'CLOUDWATCH', 'SQS', 'SNS'],
    title: 'ResourceType'
} as const;

export const ResourceUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    minLength: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        arn: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 2048
                },
                {
                    type: 'null'
                }
            ],
            title: 'Arn'
        },
        tags: {
            type: 'object',
            title: 'Tags',
            default: {}
        },
        configurations: {
            type: 'object',
            title: 'Configurations',
            default: {}
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        type: {
            type: 'string',
            maxLength: 50,
            title: 'Type'
        },
        region: {
            type: 'string',
            maxLength: 50,
            title: 'Region'
        },
        status: {
            '$ref': '#/components/schemas/ResourceStatus',
            default: 'found'
        }
    },
    type: 'object',
    required: ['type', 'region'],
    title: 'ResourceUpdate'
} as const;

export const ResourcesPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ResourcePublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'ResourcesPublic'
} as const;

export const RetrieverConfigSchema = {
    properties: {
        numberOfResults: {
            type: 'integer',
            title: 'Numberofresults',
            default: 4
        },
        overrideSearchType: {
            anyOf: [
                {
                    type: 'string',
                    enum: ['HYBRID', 'SEMANTIC']
                },
                {
                    type: 'null'
                }
            ],
            title: 'Overridesearchtype',
            default: 'SEMANTIC'
        }
    },
    type: 'object',
    title: 'RetrieverConfig'
} as const;

export const RunModeEnumSchema = {
    type: 'string',
    enum: ['autonomous', 'agent'],
    title: 'RunModeEnum'
} as const;

export const SavingSummaryReportSchema = {
    properties: {
        potential_savings: {
            type: 'number',
            title: 'Potential Savings'
        },
        potential_savings_percentage_change: {
            type: 'number',
            title: 'Potential Savings Percentage Change'
        },
        save_opportunities: {
            type: 'number',
            title: 'Save Opportunities'
        },
        save_opportunities_percentage_change: {
            type: 'number',
            title: 'Save Opportunities Percentage Change'
        },
        total_saved: {
            type: 'number',
            title: 'Total Saved'
        },
        total_saved_percentage_change: {
            type: 'number',
            title: 'Total Saved Percentage Change'
        },
        active_saving: {
            type: 'number',
            title: 'Active Saving'
        },
        active_saving_percentage_change: {
            type: 'number',
            title: 'Active Saving Percentage Change'
        }
    },
    type: 'object',
    required: ['potential_savings', 'potential_savings_percentage_change', 'save_opportunities', 'save_opportunities_percentage_change', 'total_saved', 'total_saved_percentage_change', 'active_saving', 'active_saving_percentage_change'],
    title: 'SavingSummaryReport'
} as const;

export const ScriptExecutionResponseSchema = {
    properties: {
        status: {
            type: 'string',
            title: 'Status'
        },
        result: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Result'
        }
    },
    type: 'object',
    required: ['status'],
    title: 'ScriptExecutionResponse'
} as const;

export const SearchResponseSchema = {
    properties: {
        query: {
            type: 'string',
            title: 'Query'
        },
        results: {
            items: {
                '$ref': '#/components/schemas/Document'
            },
            type: 'array',
            title: 'Results'
        },
        total_found: {
            type: 'integer',
            title: 'Total Found'
        },
        execution_time: {
            type: 'number',
            title: 'Execution Time'
        }
    },
    type: 'object',
    required: ['query', 'results', 'total_found', 'execution_time'],
    title: 'SearchResponse'
} as const;

export const ServiceSavingsDataSchema = {
    properties: {
        service: {
            type: 'string',
            title: 'Service'
        },
        savings: {
            type: 'number',
            title: 'Savings'
        },
        percentage: {
            type: 'number',
            title: 'Percentage'
        }
    },
    type: 'object',
    required: ['service', 'savings', 'percentage'],
    title: 'ServiceSavingsData'
} as const;

export const ServiceSavingsReportSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ServiceSavingsData'
            },
            type: 'array',
            title: 'Data'
        },
        total_savings: {
            type: 'number',
            title: 'Total Savings'
        }
    },
    type: 'object',
    required: ['data', 'total_savings'],
    title: 'ServiceSavingsReport'
} as const;

export const SettingSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        provider_name: {
            '$ref': '#/components/schemas/CloudProvider',
            default: 'AWS'
        },
        regions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Regions',
            default: []
        },
        types: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Types',
            default: []
        },
        cron_patterns: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Cron Patterns',
            default: []
        }
    },
    type: 'object',
    title: 'Setting'
} as const;

export const ShareResponseSchema = {
    properties: {
        share_id: {
            type: 'string',
            format: 'uuid',
            title: 'Share Id'
        },
        is_shared: {
            type: 'boolean',
            title: 'Is Shared'
        },
        shared_at: {
            type: 'string',
            format: 'date-time',
            title: 'Shared At'
        },
        shared_by: {
            type: 'string',
            format: 'uuid',
            title: 'Shared By'
        }
    },
    type: 'object',
    required: ['share_id', 'is_shared', 'shared_at', 'shared_by'],
    title: 'ShareResponse'
} as const;

export const StreamResponseSchema = {
    properties: {
        type: {
            type: 'string',
            title: 'Type'
        },
        content: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Content'
        },
        message_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Message Id'
        }
    },
    type: 'object',
    required: ['type'],
    title: 'StreamResponse'
} as const;

export const SubscriptionStatusSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        customer_id: {
            type: 'string',
            title: 'Customer Id'
        },
        status: {
            type: 'string',
            title: 'Status'
        },
        current_period_end: {
            type: 'string',
            format: 'date-time',
            title: 'Current Period End'
        },
        cancel_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Cancel At'
        },
        product_name: {
            type: 'string',
            title: 'Product Name'
        },
        product_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Product Id'
        },
        price_amount: {
            type: 'number',
            title: 'Price Amount'
        },
        price_currency: {
            type: 'string',
            title: 'Price Currency'
        },
        price_interval: {
            type: 'string',
            title: 'Price Interval'
        }
    },
    type: 'object',
    required: ['id', 'customer_id', 'status', 'current_period_end', 'product_name', 'price_amount', 'price_currency', 'price_interval'],
    title: 'SubscriptionStatus'
} as const;

export const SummaryResponseSchema = {
    properties: {
        query: {
            type: 'string',
            title: 'Query'
        },
        summary: {
            type: 'string',
            title: 'Summary'
        },
        sources: {
            items: {},
            type: 'array',
            title: 'Sources'
        },
        citations: {
            items: {
                '$ref': '#/components/schemas/CitationMetadata'
            },
            type: 'array',
            title: 'Citations'
        },
        execution_time: {
            type: 'number',
            title: 'Execution Time'
        }
    },
    type: 'object',
    required: ['query', 'summary', 'sources', 'citations', 'execution_time'],
    title: 'SummaryResponse'
} as const;

export const TaskCategoryEnumSchema = {
    type: 'string',
    enum: ['COST_OPTIMIZE', 'OPERATIONAL', 'SCALABILITY', 'SECURITY', 'OPERATIONAL_EFFICIENCY', 'OTHER'],
    title: 'TaskCategoryEnum',
    description: 'Enumeration of possible task categories.'
} as const;

export const TaskCouldEnumSchema = {
    type: 'string',
    enum: ['AWS', 'AZURE', 'GCP', 'ALL'],
    title: 'TaskCouldEnum'
} as const;

export const TaskCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            default: ''
        },
        priority: {
            '$ref': '#/components/schemas/TaskPriority',
            default: 0
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        agent_config: {
            type: 'object',
            title: 'Agent Config'
        }
    },
    type: 'object',
    required: ['title'],
    title: 'TaskCreate',
    description: 'Schema for creating a new task.'
} as const;

export const TaskDeleteResponseSchema = {
    properties: {
        status: {
            type: 'string',
            title: 'Status',
            default: 'success'
        }
    },
    type: 'object',
    title: 'TaskDeleteResponse',
    description: 'Schema for task delete response.'
} as const;

export const TaskExecutionStatusSchema = {
    type: 'string',
    enum: ['running', 'succeeded', 'failed', 'cancelled', 'required_approval'],
    title: 'TaskExecutionStatus',
    description: `Enumeration of execution statuses for task.

Attributes:
    RUNNING: Currently executing
    SUCCEEDED: Successfully completed
    FAILED: Execution failed
    CANCELLED: Execution cancelled
    REQUIRED_APPROVAL: Execution requires approval`
} as const;

export const TaskHistorySchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        task_id: {
            type: 'string',
            format: 'uuid',
            title: 'Task Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        status: {
            '$ref': '#/components/schemas/TaskExecutionStatus',
            description: 'Current task status'
        },
        message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Message',
            description: 'Message from the task execution: error or required action'
        },
        celery_task_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Celery Task Id',
            description: 'Celery task ID associated with the task'
        },
        start_time: {
            type: 'string',
            format: 'date-time',
            title: 'Start Time',
            description: 'Timestamp when the task history was started'
        },
        end_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'End Time',
            description: 'Timestamp when the task history was ended'
        },
        run_time: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Run Time',
            description: 'Time taken to run the task'
        }
    },
    type: 'object',
    required: ['task_id', 'conversation_id', 'status'],
    title: 'TaskHistory',
    description: 'Execution history of a task conversation.'
} as const;

export const TaskListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/TaskResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total',
            default: 0
        }
    },
    type: 'object',
    title: 'TaskList',
    description: 'Schema for paginated task list.'
} as const;

export const TaskPrioritySchema = {
    type: 'integer',
    enum: [0, 1, 2, 3],
    title: 'TaskPriority',
    description: `Enumeration of task priority levels.

Attributes:
    LOW (0): Regular priority, no urgency
    MEDIUM (1): Moderate priority, should be done soon
    HIGH (2): High priority, urgent attention needed
    CRITICAL (3): Critical priority, requires immediate attention`
} as const;

export const TaskResponseSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            default: ''
        },
        priority: {
            '$ref': '#/components/schemas/TaskPriority',
            default: 0
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        },
        scheduled_status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskScheduledStatus'
                },
                {
                    type: 'null'
                }
            ]
        },
        execution_status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskExecutionStatus'
                },
                {
                    type: 'null'
                }
            ]
        },
        error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Error'
        },
        last_run: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Run'
        },
        next_run: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Next Run'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        agent_config: {
            type: 'object',
            title: 'Agent Config'
        },
        enable: {
            type: 'boolean',
            title: 'Enable',
            default: true
        },
        task_history: {
            items: {
                '$ref': '#/components/schemas/TaskHistory'
            },
            type: 'array',
            title: 'Task History'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        created_by: {
            type: 'string',
            format: 'uuid',
            title: 'Created By'
        },
        updated_by: {
            type: 'string',
            format: 'uuid',
            title: 'Updated By'
        }
    },
    type: 'object',
    required: ['title', 'id', 'workspace_id', 'owner_id', 'created_at', 'updated_at', 'created_by', 'updated_by'],
    title: 'TaskResponse',
    description: 'Schema for task response.'
} as const;

export const TaskScheduledStatusSchema = {
    type: 'string',
    enum: ['pending', 'scheduled'],
    title: 'TaskScheduledStatus',
    description: 'Enumeration of scheduled statuses for task.'
} as const;

export const TaskServiceEnumSchema = {
    type: 'string',
    enum: ['ALL', 'OTHER', 'COMPUTE', 'STORAGE', 'SERVERLESS', 'DATABASE', 'NETWORK', 'MESSAGING', 'MANAGEMENT', 'BILLING', 'CROSS_SERVICE', 'MONITORING', 'STREAMING', 'SECURITY'],
    title: 'TaskServiceEnum',
    description: 'Enumeration of possible task services.'
} as const;

export const TaskStatusResponseSchema = {
    properties: {
        task_id: {
            type: 'string',
            title: 'Task Id',
            description: 'Celery task ID'
        },
        status: {
            '$ref': '#/components/schemas/AsyncTaskStatus',
            description: 'Task status (PENDING, PROGRESS, SUCCESS, FAILURE)'
        },
        progress: {
            type: 'integer',
            title: 'Progress',
            description: 'Progress percentage (0-100)',
            default: 0
        },
        result: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Result',
            description: 'Task result if completed'
        },
        error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Error',
            description: 'Error message if failed'
        },
        status_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Status Message',
            description: 'Human-readable status message'
        }
    },
    type: 'object',
    required: ['task_id', 'status'],
    title: 'TaskStatusResponse',
    description: 'Response schema for task status operations'
} as const;

export const TaskStopResponseSchema = {
    properties: {
        task_id: {
            type: 'string',
            format: 'uuid',
            title: 'Task Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        status: {
            type: 'string',
            title: 'Status'
        }
    },
    type: 'object',
    required: ['task_id', 'conversation_id', 'status'],
    title: 'TaskStopResponse',
    description: 'Schema for task stop response.'
} as const;

export const TaskTemplateCreateSchema = {
    properties: {
        task: {
            type: 'string',
            title: 'Task'
        },
        category: {
            '$ref': '#/components/schemas/TaskCategoryEnum'
        },
        service: {
            '$ref': '#/components/schemas/TaskServiceEnum'
        },
        service_name: {
            type: 'string',
            title: 'Service Name'
        },
        cloud: {
            '$ref': '#/components/schemas/TaskCouldEnum'
        },
        run_mode: {
            '$ref': '#/components/schemas/RunModeEnum'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        context: {
            type: 'string',
            title: 'Context'
        }
    },
    type: 'object',
    required: ['task', 'category', 'service', 'service_name', 'cloud', 'run_mode', 'context'],
    title: 'TaskTemplateCreate'
} as const;

export const TaskTemplateListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/TaskTemplateResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'TaskTemplateList'
} as const;

export const TaskTemplateResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        task: {
            type: 'string',
            title: 'Task'
        },
        category: {
            '$ref': '#/components/schemas/TaskCategoryEnum'
        },
        service: {
            '$ref': '#/components/schemas/TaskServiceEnum'
        },
        service_name: {
            type: 'string',
            title: 'Service Name'
        },
        cloud: {
            '$ref': '#/components/schemas/TaskCouldEnum'
        },
        run_mode: {
            '$ref': '#/components/schemas/RunModeEnum'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        context: {
            type: 'string',
            title: 'Context'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'task', 'category', 'service', 'service_name', 'cloud', 'run_mode', 'schedule', 'context', 'is_default', 'created_at', 'updated_at'],
    title: 'TaskTemplateResponse'
} as const;

export const TaskTemplateUpdateSchema = {
    properties: {
        task: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Task'
        },
        category: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskCategoryEnum'
                },
                {
                    type: 'null'
                }
            ]
        },
        service: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskServiceEnum'
                },
                {
                    type: 'null'
                }
            ]
        },
        run_mode: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/RunModeEnum'
                },
                {
                    type: 'null'
                }
            ]
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        context: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Context'
        }
    },
    type: 'object',
    title: 'TaskTemplateUpdate'
} as const;

export const TaskUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    minLength: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            default: ''
        },
        priority: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskPriority'
                },
                {
                    type: 'null'
                }
            ]
        },
        tags: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tags'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        agent_config: {
            type: 'object',
            title: 'Agent Config'
        }
    },
    type: 'object',
    title: 'TaskUpdate',
    description: 'Schema for updating an existing task.'
} as const;

export const TokenSchema = {
    properties: {
        access_token: {
            type: 'string',
            title: 'Access Token'
        },
        token_type: {
            type: 'string',
            title: 'Token Type',
            default: 'bearer'
        },
        workspace_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspace Id'
        },
        is_first_login: {
            type: 'boolean',
            title: 'Is First Login',
            default: false
        },
        slack_oauth: {
            type: 'boolean',
            title: 'Slack Oauth',
            default: false
        },
        app_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'App Id'
        },
        team_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Team Id'
        }
    },
    type: 'object',
    required: ['access_token'],
    title: 'Token'
} as const;

export const TokenDistributionCategorySchema = {
    properties: {
        category: {
            type: 'string',
            title: 'Category'
        },
        percentage: {
            type: 'number',
            title: 'Percentage'
        }
    },
    type: 'object',
    required: ['category', 'percentage'],
    title: 'TokenDistributionCategory'
} as const;

export const TokenUsageCreateSchema = {
    properties: {
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message ID',
            description: 'Unique identifier of the associated message'
        },
        input_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Input Tokens',
            description: 'Number of tokens in the input text',
            example: 100
        },
        output_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Output Tokens',
            description: 'Number of tokens in the output text',
            example: 150
        },
        model_id: {
            type: 'string',
            title: 'Model ID',
            description: 'Identifier of the AI model used',
            example: 'gpt-4'
        }
    },
    type: 'object',
    required: ['message_id', 'input_tokens', 'output_tokens', 'model_id'],
    title: 'TokenUsageCreate'
} as const;

export const TokenUsageResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'ID',
            description: 'Unique identifier for the usage record'
        },
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message ID',
            description: 'ID of the associated message'
        },
        input_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Input Tokens',
            description: 'Number of tokens in the input text'
        },
        output_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Output Tokens',
            description: 'Number of tokens in the output text'
        },
        model_id: {
            type: 'string',
            title: 'Model ID',
            description: 'Identifier of the AI model used'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace ID',
            description: 'ID of the workspace'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At',
            description: 'Timestamp of record creation'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At',
            description: 'Timestamp of last update'
        },
        total_tokens: {
            type: 'integer',
            title: 'Total Tokens',
            description: 'Calculate total tokens from input and output tokens.',
            readOnly: true
        }
    },
    type: 'object',
    required: ['id', 'message_id', 'input_tokens', 'output_tokens', 'model_id', 'workspace_id', 'created_at', 'total_tokens'],
    title: 'TokenUsageResponse',
    description: `Schema for token usage response.

Attributes:
    id: Unique identifier for the usage record
    message_id: ID of the associated message
    input_tokens: Number of tokens in input text
    output_tokens: Number of tokens in output text
    model_id: ID of the AI model used
    total_tokens: Total number of tokens used
    created_at: Timestamp of record creation`
} as const;

export const ToolPermissionRequestSchema = {
    properties: {
        permission: {
            type: 'string',
            title: 'Permission'
        }
    },
    type: 'object',
    required: ['permission'],
    title: 'ToolPermissionRequest'
} as const;

export const TopSavingsReportSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/RecommendationPublic'
            },
            type: 'array',
            title: 'Data'
        }
    },
    type: 'object',
    required: ['data'],
    title: 'TopSavingsReport'
} as const;

export const URLsUploadRequestSchema = {
    properties: {
        urls: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Urls',
            description: 'URLs to crawl (required if source_type is website)'
        },
        deep_crawls: {
            anyOf: [
                {
                    items: {
                        type: 'boolean'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Deep Crawls',
            description: 'Whether to enable deep crawling for each URL'
        }
    },
    type: 'object',
    title: 'URLsUploadRequest'
} as const;

export const UpdatePasswordSchema = {
    properties: {
        current_password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'Current Password'
        },
        new_password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'New Password'
        }
    },
    type: 'object',
    required: ['current_password', 'new_password'],
    title: 'UpdatePassword'
} as const;

export const UploadCreateSchema = {
    properties: {
        filename: {
            type: 'string',
            title: 'Filename'
        },
        file_size: {
            type: 'integer',
            title: 'File Size'
        },
        file_type: {
            type: 'string',
            title: 'File Type'
        }
    },
    type: 'object',
    required: ['filename', 'file_size', 'file_type'],
    title: 'UploadCreate'
} as const;

export const UploadPublicSchema = {
    properties: {
        filename: {
            type: 'string',
            title: 'Filename'
        },
        file_size: {
            type: 'integer',
            title: 'File Size'
        },
        file_type: {
            type: 'string',
            title: 'File Type'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        status: {
            '$ref': '#/components/schemas/UploadStatus'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['filename', 'file_size', 'file_type', 'id', 'status', 'created_at'],
    title: 'UploadPublic'
} as const;

export const UploadResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        upload_url: {
            type: 'string',
            title: 'Upload Url'
        },
        expires_in: {
            type: 'integer',
            title: 'Expires In'
        }
    },
    type: 'object',
    required: ['id', 'upload_url', 'expires_in'],
    title: 'UploadResponse'
} as const;

export const UploadStatusSchema = {
    type: 'string',
    enum: ['pending', 'in_progress', 'completed', 'failed'],
    title: 'UploadStatus'
} as const;

export const UploadedFileInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side ID for tracking this file'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Original filename'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'Storage key for the uploaded file'
        },
        content_type: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Content Type',
            description: 'File MIME type'
        },
        file_size: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Size',
            description: 'File size in bytes'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'storage_key'],
    title: 'UploadedFileInfo',
    description: 'Information about a successfully uploaded file'
} as const;

export const UsageQuotaResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'ID'
        },
        user_id: {
            type: 'string',
            format: 'uuid',
            title: 'User ID'
        },
        quota_used_messages: {
            type: 'integer',
            title: 'Quota Used Messages'
        },
        quota_used_tokens: {
            type: 'integer',
            title: 'Quota Used Tokens'
        },
        reset_at: {
            type: 'string',
            format: 'date-time',
            title: 'Reset At'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'user_id', 'quota_used_messages', 'quota_used_tokens', 'reset_at', 'created_at'],
    title: 'UsageQuotaResponse',
    description: 'Response schema for usage quota information.'
} as const;

export const UsageStatisticsSchema = {
    properties: {
        input_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Input Tokens'
        },
        output_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Output Tokens'
        },
        total_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Total Tokens'
        },
        quota_limit: {
            type: 'integer',
            minimum: 0,
            title: 'Quota Limit'
        },
        quota_used: {
            type: 'integer',
            minimum: 0,
            title: 'Quota Used'
        },
        quota_remaining: {
            type: 'integer',
            minimum: 0,
            title: 'Quota Remaining'
        },
        usage_percentage: {
            type: 'number',
            minimum: 0,
            title: 'Usage Percentage'
        },
        daily_token_usage: {
            items: {
                '$ref': '#/components/schemas/DailyTokenUsage'
            },
            type: 'array',
            title: 'Daily Token Usage'
        },
        agent_type_stats: {
            items: {
                '$ref': '#/components/schemas/AgentTypeUsage'
            },
            type: 'array',
            title: 'Agent Type Stats'
        }
    },
    type: 'object',
    required: ['input_tokens', 'output_tokens', 'total_tokens', 'quota_limit', 'quota_used', 'quota_remaining', 'usage_percentage', 'daily_token_usage', 'agent_type_stats'],
    title: 'UsageStatistics',
    description: 'Response schema for usage statistics.'
} as const;

export const UserCreateSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'Password'
        }
    },
    type: 'object',
    required: ['email', 'password'],
    title: 'UserCreate'
} as const;

export const UserDetailSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspaces: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/Workspace'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspaces'
        },
        own_workspaces: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/Workspace'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Own Workspaces'
        },
        items: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/Item'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Items'
        }
    },
    type: 'object',
    required: ['email', 'id'],
    title: 'UserDetail'
} as const;

export const UserPublicSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        }
    },
    type: 'object',
    required: ['email', 'id'],
    title: 'UserPublic'
} as const;

export const UserRegisterSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'Password'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        }
    },
    type: 'object',
    required: ['email', 'password'],
    title: 'UserRegister'
} as const;

export const UserUpdateSchema = {
    properties: {
        email: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    format: 'email'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        password: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 40,
                    minLength: 8
                },
                {
                    type: 'null'
                }
            ],
            title: 'Password'
        }
    },
    type: 'object',
    title: 'UserUpdate'
} as const;

export const UserUpdateMeSchema = {
    properties: {
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        email: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    format: 'email'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Email'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        }
    },
    type: 'object',
    title: 'UserUpdateMe'
} as const;

export const UsersPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/UserPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'UsersPublic'
} as const;

export const ValidationErrorSchema = {
    properties: {
        loc: {
            items: {
                anyOf: [
                    {
                        type: 'string'
                    },
                    {
                        type: 'integer'
                    }
                ]
            },
            type: 'array',
            title: 'Location'
        },
        msg: {
            type: 'string',
            title: 'Message'
        },
        type: {
            type: 'string',
            title: 'Error Type'
        }
    },
    type: 'object',
    required: ['loc', 'msg', 'type'],
    title: 'ValidationError'
} as const;

export const WorkflowCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        }
    },
    type: 'object',
    required: ['name', 'workspace_id'],
    title: 'WorkflowCreate'
} as const;

export const WorkflowNodeCreateSchema = {
    properties: {
        type: {
            '$ref': '#/components/schemas/NodeType'
        },
        name: {
            type: 'string',
            maxLength: 255,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        position: {
            type: 'integer',
            title: 'Position'
        },
        data: {
            type: 'object',
            title: 'Data',
            default: {}
        },
        workflow_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workflow Id'
        },
        parent_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Parent Id'
        }
    },
    type: 'object',
    required: ['type', 'name', 'position', 'workflow_id'],
    title: 'WorkflowNodeCreate'
} as const;

export const WorkflowNodePublicSchema = {
    properties: {
        type: {
            '$ref': '#/components/schemas/NodeType'
        },
        name: {
            type: 'string',
            maxLength: 255,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        position: {
            type: 'integer',
            title: 'Position'
        },
        data: {
            type: 'object',
            title: 'Data',
            default: {}
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        status: {
            '$ref': '#/components/schemas/WorkflowStatus'
        }
    },
    type: 'object',
    required: ['type', 'name', 'position', 'id', 'status'],
    title: 'WorkflowNodePublic'
} as const;

export const WorkflowNodeUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        position: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Position'
        }
    },
    type: 'object',
    title: 'WorkflowNodeUpdate'
} as const;

export const WorkflowPublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        nodes: {
            items: {
                '$ref': '#/components/schemas/WorkflowNodePublic'
            },
            type: 'array',
            title: 'Nodes'
        },
        status: {
            '$ref': '#/components/schemas/WorkflowStatus',
            default: 'created'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['name', 'workspace_id', 'id', 'nodes'],
    title: 'WorkflowPublic'
} as const;

export const WorkflowStatusSchema = {
    type: 'string',
    enum: ['created', 'unvalidated', 'running', 'pending', 'completed', 'error'],
    title: 'WorkflowStatus'
} as const;

export const WorkflowUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        workspace_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspace Id'
        }
    },
    type: 'object',
    title: 'WorkflowUpdate'
} as const;

export const WorkflowsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/WorkflowPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'WorkflowsPublic'
} as const;

export const WorkspaceSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default',
            default: false
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        }
    },
    type: 'object',
    required: ['name', 'owner_id'],
    title: 'Workspace'
} as const;

export const WorkspaceCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        owner_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Owner Id'
        }
    },
    type: 'object',
    required: ['name'],
    title: 'WorkspaceCreate'
} as const;

export const WorkspaceDetailSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        aws_account: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/AWSAccountDetail'
                },
                {
                    type: 'null'
                }
            ]
        },
        settings: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/WorkspaceSetting'
                },
                {
                    type: 'null'
                }
            ]
        },
        provider_settings: {
            '$ref': '#/components/schemas/Setting'
        }
    },
    type: 'object',
    required: ['name', 'id', 'is_default', 'settings', 'provider_settings'],
    title: 'WorkspaceDetail'
} as const;

export const WorkspacePublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['name', 'id', 'is_default'],
    title: 'WorkspacePublic'
} as const;

export const WorkspaceSettingSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        regions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Regions',
            default: []
        },
        types: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Types',
            default: []
        },
        cron_pattern: {
            type: 'string',
            maxLength: 250,
            title: 'Cron Pattern'
        }
    },
    type: 'object',
    required: ['workspace_id', 'cron_pattern'],
    title: 'WorkspaceSetting',
    description: 'Settings for a workspace'
} as const;

export const WorkspaceUpdateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        }
    },
    type: 'object',
    required: ['name'],
    title: 'WorkspaceUpdate'
} as const;

export const WorkspacesPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/WorkspacePublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'WorkspacesPublic'
} as const;
