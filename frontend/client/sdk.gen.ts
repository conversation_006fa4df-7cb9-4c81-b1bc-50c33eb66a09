// This file is auto-generated by @hey-api/openapi-ts

import type { CancelablePromise } from './core/CancelablePromise';
import { OpenAPI } from './core/OpenAPI';
import { request as __request } from './core/request';
import type { CreateAgentConnectorData, CreateAgentConnectorResponse, GetAgentConnectorData, GetAgentConnectorResponse, UpdateAgentConnectorData, UpdateAgentConnectorResponse, DeleteAgentConnectorData, DeleteAgentConnectorResponse, GetAgentConnectorsByWorkspaceIdData, GetAgentConnectorsByWorkspaceIdResponse, UpdateAgentContextData, UpdateAgentContextResponse, GetAgentContextData, GetAgentContextResponse, GetAgentContextsData, GetAgentContextsResponse, ReadAgentsData, ReadAgentsResponse, CreateAgentData, CreateAgentResponse, ReadAgentData, ReadAgentResponse, UpdateAgentData, UpdateAgentResponse, DeleteAgentData, DeleteAgentResponse, InitDefaultAgentsData, InitDefaultAgentsResponse, GetAlertStatusSummaryResponse, CreateAlertData, CreateAlertResponse, ListAlertsData, ListAlertsResponse, GetAlertData, GetAlertResponse, UpdateAlertData, UpdateAlertResponse, DeleteAlertData, DeleteAlertResponse, UpdateAlertStatusData, UpdateAlertStatusResponse, RegisterData, RegisterResponse, ActivateAccountData, ActivateAccountResponse, ResendActivationData, ResendActivationResponse, CreateConversationData, CreateConversationResponse, GetConversationsData, GetConversationsResponse, GetMessagesHistoryData, GetMessagesHistoryResponse, ChatStreamData, ChatStreamResponse, RenameConversationData, RenameConversationResponse, DeleteConversationData, DeleteConversationResponse, ReadAwsAccountsData, ReadAwsAccountsResponse, CreateAwsAccountData, CreateAwsAccountResponse, ReadAwsAccountData, ReadAwsAccountResponse, UpdateAwsAccountData, UpdateAwsAccountResponse, DeleteAwsAccountData, DeleteAwsAccountResponse, ListWorkspaceConnectorsData, ListWorkspaceConnectorsResponse, UpdateConnectorForWorkspaceData, UpdateConnectorForWorkspaceResponse, UpdateConnectorPermissionData, UpdateConnectorPermissionResponse, CreateConnectorData, CreateConnectorResponse, ListConnectorsData, ListConnectorsResponse, GetConnectorData, GetConnectorResponse, UpdateConnectorData, UpdateConnectorResponse, DeleteConnectorData, DeleteConnectorResponse, CreateUploadUrlData, CreateUploadUrlResponse, CheckUploadStatusData, CheckUploadStatusResponse, GoogleLoginResponse, GoogleCallbackResponse, ReadItemsData, ReadItemsResponse, CreateItemData, CreateItemResponse, ReadItemData, ReadItemResponse, UpdateItemData, UpdateItemResponse, DeleteItemData, DeleteItemResponse, CreateKbData, CreateKbResponse, GetKbsData, GetKbsResponse, GetAvailableUsersResponse, GetKbByIdData, GetKbByIdResponse, UpdateKbData, UpdateKbResponse, DeleteKbData, DeleteKbResponse, GeneratePresignedUrlsData, GeneratePresignedUrlsResponse, ConfirmFileUploadsData, ConfirmFileUploadsResponse, UploadUrlsData, UploadUrlsResponse, ListDocumentsData, ListDocumentsResponse, GetDocumentContentData, GetDocumentContentResponse, DeleteDocumentData, DeleteDocumentResponse, GetTaskStatusData, GetTaskStatusResponse, SearchData, SearchResponse2, SummarizeData, SummarizeResponse, LoginAccessTokenData, LoginAccessTokenResponse, TestTokenResponse, RecoverPasswordData, RecoverPasswordResponse, ResetPasswordData, ResetPasswordResponse, RecoverPasswordHtmlContentData, RecoverPasswordHtmlContentResponse, GetMcpConfigData, GetMcpConfigResponse, CreateMcpConfigData, CreateMcpConfigResponse, UpdateMcpConfigData, UpdateMcpConfigResponse, DeleteMcpConfigData, DeleteMcpConfigResponse, GetMcpServersData, GetMcpServersResponse, ToggleMcpServerActiveStateData, ToggleMcpServerActiveStateResponse, RefreshMcpServerStatusData, RefreshMcpServerStatusResponse, AddServerToolPermissionData, AddServerToolPermissionResponse, RemoveServerToolPermissionData, RemoveServerToolPermissionResponse, GetMemoryData, GetMemoryResponse, DeleteMemoryData, DeleteMemoryResponse, UpdateMemoryData, UpdateMemoryResponse, GetMessageFeedbackData, GetMessageFeedbackResponse, UpdateMessageFeedbackData, UpdateMessageFeedbackResponse, DeleteMessageFeedbackData, DeleteMessageFeedbackResponse, CreateMessageFeedbackData, CreateMessageFeedbackResponse, ReadMetricsData, ReadMetricsResponse, CreateMetricData, CreateMetricResponse, ReadMetricData, ReadMetricResponse, UpdateMetricData, UpdateMetricResponse, DeleteMetricData, DeleteMetricResponse, GetModuleSettingsResponse, ListNotificationsData, ListNotificationsResponse, MarkNotificationReadData, MarkNotificationReadResponse, MarkAllNotificationsReadData, MarkAllNotificationsReadResponse, CreateUsageData, CreateUsageResponse, CreateUsageQuotaData, CreateUsageQuotaResponse, GetUsageQuotaData, GetUsageQuotaResponse, ResetUserQuotaData, ResetUserQuotaResponse, GetUsageStatisticsData, GetUsageStatisticsResponse, GetMessagesStatisticsData, GetMessagesStatisticsResponse, GetQuotaInfoData, GetQuotaInfoResponse, GetRecomendationOveralResponse, ReadRecommendationsData, ReadRecommendationsResponse, CreateRecommendationData, CreateRecommendationResponse, ReadRecommendationData, ReadRecommendationResponse, UpdateRecommendationData, UpdateRecommendationResponse, DeleteRecommendationData, DeleteRecommendationResponse, UpdateRecommendationStatusData, UpdateRecommendationStatusResponse, GetSavingsSummaryData, GetSavingsSummaryResponse, GetSavingsByResourceData, GetSavingsByResourceResponse, GetTopPotentialSavingsData, GetTopPotentialSavingsResponse, GetSavingsByServiceData, GetSavingsByServiceResponse, ReadResourcesData, ReadResourcesResponse, CreateResourceData, CreateResourceResponse, ReadResourceData, ReadResourceResponse, UpdateResourceData, UpdateResourceResponse, DeleteResourceData, DeleteResourceResponse, CreateSampleResourcesData, CreateSampleResourcesResponse, CreateSampleMetricsData, CreateSampleMetricsResponse, CreateSampleRecommendationsData, CreateSampleRecommendationsResponse, CreateShareLinkData, CreateShareLinkResponse, RevokeShareLinkData, RevokeShareLinkResponse, GetShareLinkData, GetShareLinkResponse, GetSharedConversationData, GetSharedConversationResponse, GetAvailablePlansResponse, GetUserSubscriptionStatusResponse, GetWorkspaceSubscriptionStatusData, GetWorkspaceSubscriptionStatusResponse, CreateCheckoutSessionData, CreateCheckoutSessionResponse, GetUserPaymentMethodsData, GetUserPaymentMethodsResponse, GetUserInvoicesData, GetUserInvoicesResponse, SubmitEnterpriseEnquiryData, SubmitEnterpriseEnquiryResponse, SubmitPlanChangeRequestData, SubmitPlanChangeRequestResponse, WebhookResponse, CancelSubscriptionResponse, CreateTaskData, CreateTaskResponse, ListTasksData, ListTasksResponse, GetTaskData, GetTaskResponse, UpdateTaskData, UpdateTaskResponse, DeleteTaskData, DeleteTaskResponse, UpdateTaskEnableData, UpdateTaskEnableResponse, StopTaskExecutionData, StopTaskExecutionResponse, GetTaskProgressData, GetTaskProgressResponse, GenerateData, GenerateResponse, CreateTemplateData, CreateTemplateResponse, ListTemplatesData, ListTemplatesResponse, GetTemplateData, GetTemplateResponse, UpdateTemplateData, UpdateTemplateResponse, DeleteTemplateData, DeleteTemplateResponse, ScriptExecutionData, ScriptExecutionResponse2, ReadUsersData, ReadUsersResponse, CreateUserData, CreateUserResponse, ReadUserMeResponse, DeleteUserMeResponse, UpdateUserMeData, UpdateUserMeResponse, UpdatePasswordMeData, UpdatePasswordMeResponse, ReadUserByIdData, ReadUserByIdResponse, UpdateUserData, UpdateUserResponse, DeleteUserData, DeleteUserResponse, SwitchWorkspaceData, SwitchWorkspaceResponse, TestEmailData, TestEmailResponse, HealthCheckResponse, PublishMessageData, PublishMessageResponse, EnqueueMessageData, EnqueueMessageResponse, ReadWorkflowsData, ReadWorkflowsResponse, CreateWorkflowData, CreateWorkflowResponse, ReadWorkflowData, ReadWorkflowResponse, UpdateWorkflowData, UpdateWorkflowResponse, DeleteWorkflowData, DeleteWorkflowResponse, CreateWorkflowFromTemplateData, CreateWorkflowFromTemplateResponse, CreateWorkflowNodeData, CreateWorkflowNodeResponse, ReadWorkflowNodesData, ReadWorkflowNodesResponse, ReadWorkflowNodeData, ReadWorkflowNodeResponse, UpdateWorkflowNodeData, UpdateWorkflowNodeResponse, DeleteWorkflowNodeData, DeleteWorkflowNodeResponse, RunWorkflowNodeData, RunWorkflowNodeResponse, RunWorkflowData, RunWorkflowResponse, ReadWorkspacesData, ReadWorkspacesResponse, CreateWorkspaceData, CreateWorkspaceResponse, ReadWorkspaceData, ReadWorkspaceResponse, UpdateWorkspaceData, UpdateWorkspaceResponse, DeleteWorkspaceData, DeleteWorkspaceResponse } from './types.gen';

export class AgentConnectorsService {
    /**
     * Create Agent Connector
     * Create a new agent connector.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static createAgentConnector(data: CreateAgentConnectorData): CancelablePromise<CreateAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agent-connectors',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Connector
     * Get an agent connector by agent ID.
     * @param data The data for the request.
     * @param data.agentId
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static getAgentConnector(data: GetAgentConnectorData): CancelablePromise<GetAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-connectors/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Agent Connector
     * Update an existing agent connector.
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static updateAgentConnector(data: UpdateAgentConnectorData): CancelablePromise<UpdateAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/agent-connectors/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Agent Connector
     * Delete an agent connector.
     * @param data The data for the request.
     * @param data.agentId
     * @returns void Successful Response
     * @throws ApiError
     */
    public static deleteAgentConnector(data: DeleteAgentConnectorData): CancelablePromise<DeleteAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/agent-connectors/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Connectors By Workspace Id
     * Get all agent connectors by workspace ID.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static getAgentConnectorsByWorkspaceId(data: GetAgentConnectorsByWorkspaceIdData): CancelablePromise<GetAgentConnectorsByWorkspaceIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-connectors/workspace/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AgentContextService {
    /**
     * Update Agent Context
     * Update agent context
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentContextRead Successful Response
     * @throws ApiError
     */
    public static updateAgentContext(data: UpdateAgentContextData): CancelablePromise<UpdateAgentContextResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/agent-context/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Context
     * Get context based on agent id
     * @param data The data for the request.
     * @param data.agentId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getAgentContext(data: GetAgentContextData): CancelablePromise<GetAgentContextResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-context/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Contexts
     * Get contexts based on a list of agent ids, for each agent id, get the latest none deleted context
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AgentContextListResponse Successful Response
     * @throws ApiError
     */
    public static getAgentContexts(data: GetAgentContextsData): CancelablePromise<GetAgentContextsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-context/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AgentsService {
    /**
     * Read Agents
     * Retrieve Agents for the current workspace.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns AgentsPublic Successful Response
     * @throws ApiError
     */
    public static readAgents(data: ReadAgentsData = {}): CancelablePromise<ReadAgentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Agent
     * Create new Agent.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static createAgent(data: CreateAgentData): CancelablePromise<CreateAgentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agents/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Agent
     * Get Agent by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static readAgent(data: ReadAgentData): CancelablePromise<ReadAgentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Agent
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static updateAgent(data: UpdateAgentData): CancelablePromise<UpdateAgentResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/agents/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Agent
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteAgent(data: DeleteAgentData): CancelablePromise<DeleteAgentResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/agents/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Init Default Agents
     * Initialize default agents for a workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static initDefaultAgents(data: InitDefaultAgentsData): CancelablePromise<InitDefaultAgentsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agents/init-default/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AlertsService {
    /**
     * Get Alert Status Summary
     * Get a summary of alerts by status for the last 30 days.
     * @returns AlertStatusSummary Successful Response
     * @throws ApiError
     */
    public static getAlertStatusSummary(): CancelablePromise<GetAlertStatusSummaryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/summary/status'
        });
    }

    /**
     * Create Alert
     * Create new alert.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static createAlert(data: CreateAlertData): CancelablePromise<CreateAlertResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/alerts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Alerts
     * List alerts with optional filters.
     * @param data The data for the request.
     * @param data.severity
     * @param data.status
     * @param data.sortBy Field to sort by
     * @param data.sortDesc Sort in descending order
     * @param data.skip
     * @param data.limit
     * @returns AlertList Successful Response
     * @throws ApiError
     */
    public static listAlerts(data: ListAlertsData = {}): CancelablePromise<ListAlertsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/',
            query: {
                severity: data.severity,
                status: data.status,
                sort_by: data.sortBy,
                sort_desc: data.sortDesc,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Alert
     * Get alert by ID.
     * @param data The data for the request.
     * @param data.alertId
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static getAlert(data: GetAlertData): CancelablePromise<GetAlertResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Alert
     * Update alert.
     * @param data The data for the request.
     * @param data.alertId
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static updateAlert(data: UpdateAlertData): CancelablePromise<UpdateAlertResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Alert
     * Delete alert.
     * @param data The data for the request.
     * @param data.alertId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteAlert(data: DeleteAlertData): CancelablePromise<DeleteAlertResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Alert Status
     * Update alert status.
     * @param data The data for the request.
     * @param data.alertId
     * @param data.status
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static updateAlertStatus(data: UpdateAlertStatusData): CancelablePromise<UpdateAlertStatusResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/alerts/{alert_id}/status',
            path: {
                alert_id: data.alertId
            },
            query: {
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AuthService {
    /**
     * Register
     * Register new user and send activation email.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ActivationResponse Successful Response
     * @throws ApiError
     */
    public static register(data: RegisterData): CancelablePromise<RegisterResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/signup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Activate Account
     * Activate a user account using the activation token.
     * @param data The data for the request.
     * @param data.token
     * @returns ActivationResult Successful Response
     * @throws ApiError
     */
    public static activateAccount(data: ActivateAccountData): CancelablePromise<ActivateAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/activate/{token}',
            path: {
                token: data.token
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Resend Activation
     * Resend activation email for unactivated accounts with reCAPTCHA v3 validation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ActivationResponse Successful Response
     * @throws ApiError
     */
    public static resendActivation(data: ResendActivationData): CancelablePromise<ResendActivationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/resend-activation',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AutonomousAgentsService {
    /**
     * Create Conversation
     * Create a new conversation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ConversationPublic Successful Response
     * @throws ApiError
     */
    public static createConversation(data: CreateConversationData): CancelablePromise<CreateConversationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/autonomous-agents/conversations',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Conversations
     * Get list of conversations with filtering and pagination.
     *
     * Args:
     * current_user: The authenticated user
     * session: Database session
     * agent_id: Optional agent ID to filter by
     * resource_id: Optional resource ID to filter by
     * model_provider: Model provider to use (defaults to 'bedrock')
     * skip: Number of records to skip for pagination
     * limit: Maximum number of records to return (1-100)
     *
     * Returns:
     * ConversationsPublic: Paginated list of conversations
     *
     * Raises:
     * HTTPException: If conversation not found or other error occurs
     * @param data The data for the request.
     * @param data.agentId
     * @param data.resourceId
     * @param data.modelProvider
     * @param data.skip Number of records to skip for pagination
     * @param data.limit Maximum number of records to return
     * @returns ConversationsPublic Successful Response
     * @throws ApiError
     */
    public static getConversations(data: GetConversationsData = {}): CancelablePromise<GetConversationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/conversations',
            query: {
                agent_id: data.agentId,
                resource_id: data.resourceId,
                model_provider: data.modelProvider,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Messages History
     * Get message history for a conversation.
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.limit
     * @returns MessageHistoryPublic Successful Response
     * @throws ApiError
     */
    public static getMessagesHistory(data: GetMessagesHistoryData): CancelablePromise<GetMessagesHistoryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/messages/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            query: {
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Chat Stream
     * Stream chat responses from the agent.
     *
     * Args:
     * conversation_id: ID of the conversation
     * message: User message with content and resume flag
     * session: Database session
     *
     * Returns:
     * StreamingResponse: Server-sent events stream of agent responses
     *
     * Raises:
     * HTTPException: If conversation not found (404) or no previous message found (404)
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.requestBody
     * @returns StreamResponse Successful Response
     * @throws ApiError
     */
    public static chatStream(data: ChatStreamData): CancelablePromise<ChatStreamResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/autonomous-agents/chat/{conversation_id}/stream',
            path: {
                conversation_id: data.conversationId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Rename Conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.name
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static renameConversation(data: RenameConversationData): CancelablePromise<RenameConversationResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/autonomous-agents/conversations/{conversation_id}/name',
            path: {
                conversation_id: data.conversationId
            },
            query: {
                name: data.name
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Conversation
     * Delete a conversation and its associated LangGraph thread data.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteConversation(data: DeleteConversationData): CancelablePromise<DeleteConversationResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/autonomous-agents/conversations/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AwsAccountsService {
    /**
     * Read Aws Accounts
     * Retrieve AWS accounts.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns AWSAccountsPublic Successful Response
     * @throws ApiError
     */
    public static readAwsAccounts(data: ReadAwsAccountsData = {}): CancelablePromise<ReadAwsAccountsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/aws-accounts/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Aws Account
     * Create new AWS account.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static createAwsAccount(data: CreateAwsAccountData): CancelablePromise<CreateAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/aws-accounts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Aws Account
     * Get AWS account by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static readAwsAccount(data: ReadAwsAccountData): CancelablePromise<ReadAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/aws-accounts/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Aws Account
     * Update an AWS account.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static updateAwsAccount(data: UpdateAwsAccountData): CancelablePromise<UpdateAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/aws-accounts/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Aws Account
     * Delete an AWS account.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteAwsAccount(data: DeleteAwsAccountData): CancelablePromise<DeleteAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/aws-accounts/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class BuiltinConnectorsService {
    /**
     * List Workspace Connectors
     * List all built-in connectors for a workspace
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.skip
     * @param data.limit
     * @param data.activeOnly
     * @returns ConnectorWithStatusResponse Successful Response
     * @throws ApiError
     */
    public static listWorkspaceConnectors(data: ListWorkspaceConnectorsData): CancelablePromise<ListWorkspaceConnectorsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/builtin-connectors/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            query: {
                skip: data.skip,
                limit: data.limit,
                active_only: data.activeOnly
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Connector For Workspace
     * Update the active status of a built-in connector for a workspace
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.connectorId
     * @param data.isActive
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static updateConnectorForWorkspace(data: UpdateConnectorForWorkspaceData): CancelablePromise<UpdateConnectorForWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/builtin-connectors/{workspace_id}/{connector_id}/',
            path: {
                workspace_id: data.workspaceId,
                connector_id: data.connectorId
            },
            query: {
                is_active: data.isActive
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Connector Permission
     * Update whether a tool requires human approval before execution.
     *
     * Args:
     * workspace_id: ID of the workspace
     * connector_id: ID of the connector
     * required_permission: Whether to require human approval for this tool
     * session: Database session
     *
     * Returns:
     * bool: True if the permission requirement was updated successfully
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.connectorId
     * @param data.requiredPermission
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static updateConnectorPermission(data: UpdateConnectorPermissionData): CancelablePromise<UpdateConnectorPermissionResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/builtin-connectors/{workspace_id}/{connector_id}/permission',
            path: {
                workspace_id: data.workspaceId,
                connector_id: data.connectorId
            },
            query: {
                required_permission: data.requiredPermission
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ConnectorsService {
    /**
     * Create Connector
     * Create new knowledge base.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ConnectorResponse Successful Response
     * @throws ApiError
     */
    public static createConnector(data: CreateConnectorData): CancelablePromise<CreateConnectorResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/connectors/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Connectors
     * List knowledge bases.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns ConnectorList Successful Response
     * @throws ApiError
     */
    public static listConnectors(data: ListConnectorsData = {}): CancelablePromise<ListConnectorsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/connectors/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Connector
     * Get knowledge base by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ConnectorResponse Successful Response
     * @throws ApiError
     */
    public static getConnector(data: GetConnectorData): CancelablePromise<GetConnectorResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/connectors/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Connector
     * Update knowledge base.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ConnectorResponse Successful Response
     * @throws ApiError
     */
    public static updateConnector(data: UpdateConnectorData): CancelablePromise<UpdateConnectorResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/connectors/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Connector
     * Delete an item.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteConnector(data: DeleteConnectorData): CancelablePromise<DeleteConnectorResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/connectors/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class FilesService {
    /**
     * Create Upload Url
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UploadResponse Successful Response
     * @throws ApiError
     */
    public static createUploadUrl(data: CreateUploadUrlData): CancelablePromise<CreateUploadUrlResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/files/create-url',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Check Upload Status
     * @param data The data for the request.
     * @param data.id
     * @returns UploadPublic Successful Response
     * @throws ApiError
     */
    public static checkUploadStatus(data: CheckUploadStatusData): CancelablePromise<CheckUploadStatusResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/files/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class GoogleService {
    /**
     * Google Login
     * Initiate Google OAuth login flow
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static login(): CancelablePromise<GoogleLoginResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/google/login'
        });
    }

    /**
     * Google Callback
     * Handle Google OAuth callback and login/create user
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static callback(): CancelablePromise<GoogleCallbackResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/google/callback'
        });
    }

}

export class ItemsService {
    /**
     * Read Items
     * Retrieve items.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns ItemsPublic Successful Response
     * @throws ApiError
     */
    public static readItems(data: ReadItemsData = {}): CancelablePromise<ReadItemsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/items/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Item
     * Create new item.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static createItem(data: CreateItemData): CancelablePromise<CreateItemResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/items/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Item
     * Get item by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static readItem(data: ReadItemData): CancelablePromise<ReadItemResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Item
     * Update an item.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static updateItem(data: UpdateItemData): CancelablePromise<UpdateItemResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Item
     * Delete an item.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteItem(data: DeleteItemData): CancelablePromise<DeleteItemResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class KnowledgeBaseService {
    /**
     * Create Kb
     * @param data The data for the request.
     * @param data.requestBody
     * @returns KBRead Successful Response
     * @throws ApiError
     */
    public static createKb(data: CreateKbData): CancelablePromise<CreateKbResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Kbs
     * Get all knowledge bases for the current user
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns KBsRead Successful Response
     * @throws ApiError
     */
    public static getKbs(data: GetKbsData = {}): CancelablePromise<GetKbsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Available Users
     * Get a list of users available for sharing knowledge bases within the current workspace.
     * Returns users that are in the same workspace as the current user.
     * @returns AvailableUsersCurrentWorkspace Successful Response
     * @throws ApiError
     */
    public static getAvailableUsers(): CancelablePromise<GetAvailableUsersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/available-users'
        });
    }

    /**
     * Get Kb By Id
     * Get a specific knowledge base by ID
     * @param data The data for the request.
     * @param data.kbId
     * @returns KBRead Successful Response
     * @throws ApiError
     */
    public static getKbById(data: GetKbByIdData): CancelablePromise<GetKbByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Kb
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns KBRead Successful Response
     * @throws ApiError
     */
    public static updateKb(data: UpdateKbData): CancelablePromise<UpdateKbResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Kb
     * @param data The data for the request.
     * @param data.kbId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteKb(data: DeleteKbData): CancelablePromise<DeleteKbResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Generate Presigned Urls
     * Generate presigned URLs for file uploads.
     *
     * This endpoint generates presigned URLs that clients can use to upload files
     * directly to S3, bypassing the backend for better performance and scalability.
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns PresignedUrlResponse Successful Response
     * @throws ApiError
     */
    public static generatePresignedUrls(data: GeneratePresignedUrlsData): CancelablePromise<GeneratePresignedUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Confirm File Uploads
     * Confirm file uploads and start ingestion process.
     *
     * This endpoint should be called after files have been successfully uploaded
     * using the presigned URLs to start the document ingestion process.
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static confirmFileUploads(data: ConfirmFileUploadsData): CancelablePromise<ConfirmFileUploadsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/confirm-uploads',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Upload Urls
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static uploadUrls(data: UploadUrlsData): CancelablePromise<UploadUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Documents
     * List documents in a knowledge base.
     *
     * User must have access to the knowledge base (owner for personal knowledge bases,
     * workspace member for workspace knowledge bases).
     * @param data The data for the request.
     * @param data.kbId
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @returns DocumentsKBRead Successful Response
     * @throws ApiError
     */
    public static listDocuments(data: ListDocumentsData): CancelablePromise<ListDocumentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents',
            path: {
                kb_id: data.kbId
            },
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Document Content
     * @param data The data for the request.
     * @param data.kbId
     * @param data.objectName
     * @returns string Successful Response
     * @throws ApiError
     */
    public static getDocumentContent(data: GetDocumentContentData): CancelablePromise<GetDocumentContentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/content',
            path: {
                kb_id: data.kbId
            },
            query: {
                object_name: data.objectName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Document
     * @param data The data for the request.
     * @param data.kbId
     * @param data.documentId
     * @param data.objectName
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteDocument(data: DeleteDocumentData): CancelablePromise<DeleteDocumentResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}',
            path: {
                kb_id: data.kbId,
                document_id: data.documentId
            },
            query: {
                object_name: data.objectName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Task Status
     * Get the status of an asynchronous task.
     *
     * This endpoint returns the current status and progress of a Celery task,
     * such as document ingestion.
     * @param data The data for the request.
     * @param data.taskId
     * @returns TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static getTaskStatus(data: GetTaskStatusData): CancelablePromise<GetTaskStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class KnowledgeBaseRuntimeService {
    /**
     * Search knowledge base
     * Search the knowledge base with various modes and filters
     * @param data The data for the request.
     * @param data.query
     * @param data.kbId
     * @param data.requestBody
     * @returns SearchResponse Successful Response
     * @throws ApiError
     */
    public static search(data: SearchData): CancelablePromise<SearchResponse2> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base_runtime/search',
            query: {
                query: data.query,
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                400: 'Bad Request',
                422: 'Validation Error',
                500: 'Internal Server Error'
            }
        });
    }

    /**
     * Generate summary from knowledge base
     * Search and summarize content from knowledge base
     * @param data The data for the request.
     * @param data.query
     * @param data.kbId
     * @param data.requestBody
     * @returns SummaryResponse Successful Response
     * @throws ApiError
     */
    public static summarize(data: SummarizeData): CancelablePromise<SummarizeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base_runtime/summarize',
            query: {
                query: data.query,
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                400: 'Bad Request',
                422: 'Validation Error',
                500: 'Internal Server Error'
            }
        });
    }

}

export class LoginService {
    /**
     * Login Access Token
     * OAuth2 compatible token login, get an access token for future requests
     * @param data The data for the request.
     * @param data.formData
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static accessToken(data: LoginAccessTokenData): CancelablePromise<LoginAccessTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/access-token',
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Test Token
     * Test access token
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static testToken(): CancelablePromise<TestTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/test-token'
        });
    }

    /**
     * Recover Password
     * Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static recoverPassword(data: RecoverPasswordData): CancelablePromise<RecoverPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Reset Password
     * Reset password
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static resetPassword(data: ResetPasswordData): CancelablePromise<ResetPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/reset-password/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Recover Password Html Content
     * HTML Content for Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns string Successful Response
     * @throws ApiError
     */
    public static recoverPasswordHtmlContent(data: RecoverPasswordHtmlContentData): CancelablePromise<RecoverPasswordHtmlContentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery-html-content/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class McpConfigService {
    /**
     * Get Mcp Config
     * Get MCP configuration for a specific workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns MCPConfigResponse Successful Response
     * @throws ApiError
     */
    public static getMcpConfig(data: GetMcpConfigData): CancelablePromise<GetMcpConfigResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/mcp-config/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Mcp Config
     * Create a new MCP configuration for a workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.requestBody
     * @returns MCPConfigResponse Successful Response
     * @throws ApiError
     */
    public static createMcpConfig(data: CreateMcpConfigData): CancelablePromise<CreateMcpConfigResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-config/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Mcp Config
     * Update an existing MCP configuration.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.requestBody
     * @returns MCPConfigResponse Successful Response
     * @throws ApiError
     */
    public static updateMcpConfig(data: UpdateMcpConfigData): CancelablePromise<UpdateMcpConfigResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/mcp-config/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Mcp Config
     * Delete an MCP configuration.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteMcpConfig(data: DeleteMcpConfigData): CancelablePromise<DeleteMcpConfigResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/mcp-config/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Mcp Servers
     * Get MCP server configurations with connection status.
     *
     * Returns information about all configured MCP servers for the workspace,
     * including their connection status, available tools, and configuration details.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns MCPServerListResponse Successful Response
     * @throws ApiError
     */
    public static getMcpServers(data: GetMcpServersData): CancelablePromise<GetMcpServersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/mcp-config/{workspace_id}/servers',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Toggle Mcp Server Active State
     * Toggle the active state of an MCP server (activate/deactivate).
     *
     * This endpoint allows activating or deactivating a specific MCP server without
     * removing it from the configuration. Inactive servers won't attempt connections.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.serverName
     * @returns MCPConfigResponse Successful Response
     * @throws ApiError
     */
    public static toggleMcpServerActiveState(data: ToggleMcpServerActiveStateData): CancelablePromise<ToggleMcpServerActiveStateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-config/{workspace_id}/servers/{server_name}/toggle',
            path: {
                workspace_id: data.workspaceId,
                server_name: data.serverName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Refresh Mcp Server Status
     * Refresh the connection status of a specific MCP server.
     *
     * This endpoint checks the current connection status of a specific server
     * without changing its configuration. It's useful for getting real-time status
     * of the server without refreshing all servers at once.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.serverName
     * @returns MCPServerInfo Successful Response
     * @throws ApiError
     */
    public static refreshMcpServerStatus(data: RefreshMcpServerStatusData): CancelablePromise<RefreshMcpServerStatusResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-config/{workspace_id}/servers/{server_name}/refresh',
            path: {
                workspace_id: data.workspaceId,
                server_name: data.serverName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Add Server Tool Permission
     * Add a tool permission to an MCP server.
     *
     * This endpoint allows adding a new permission to the server's tools_permissions list.
     * Permissions control what tools the server is allowed to use.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.serverName
     * @param data.requestBody
     * @returns MCPConfigResponse Successful Response
     * @throws ApiError
     */
    public static addServerToolPermission(data: AddServerToolPermissionData): CancelablePromise<AddServerToolPermissionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-config/{workspace_id}/servers/{server_name}/permissions/add',
            path: {
                workspace_id: data.workspaceId,
                server_name: data.serverName
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Remove Server Tool Permission
     * Remove a tool permission from an MCP server.
     *
     * This endpoint allows removing a permission from the server's tools_permissions list.
     * Removing a permission may restrict what tools the server can use.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.serverName
     * @param data.requestBody
     * @returns MCPConfigResponse Successful Response
     * @throws ApiError
     */
    public static removeServerToolPermission(data: RemoveServerToolPermissionData): CancelablePromise<RemoveServerToolPermissionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-config/{workspace_id}/servers/{server_name}/permissions/remove',
            path: {
                workspace_id: data.workspaceId,
                server_name: data.serverName
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class MemoryService {
    /**
     * Get Memory
     * Get all memories for given agent roles. If agent roles are not provided, get all memories for all agent roles.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MemorysRead Successful Response
     * @throws ApiError
     */
    public static getMemory(data: GetMemoryData): CancelablePromise<GetMemoryResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/memory/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Memory
     * @param data The data for the request.
     * @param data.id
     * @param data.agentRole
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteMemory(data: DeleteMemoryData): CancelablePromise<DeleteMemoryResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/memory/',
            query: {
                id: data.id,
                agent_role: data.agentRole
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Memory
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static updateMemory(data: UpdateMemoryData): CancelablePromise<UpdateMemoryResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/memory/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class MessageFeedbackService {
    /**
     * Get Message Feedback
     * Get feedback for a specific message.
     * @param data The data for the request.
     * @param data.messageId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getMessageFeedback(data: GetMessageFeedbackData): CancelablePromise<GetMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Message Feedback
     * Update feedback for a message.
     * @param data The data for the request.
     * @param data.messageId
     * @param data.requestBody
     * @returns MessageFeedbackPublic Successful Response
     * @throws ApiError
     */
    public static updateMessageFeedback(data: UpdateMessageFeedbackData): CancelablePromise<UpdateMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Message Feedback
     * Delete feedback for a message.
     * @param data The data for the request.
     * @param data.messageId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteMessageFeedback(data: DeleteMessageFeedbackData): CancelablePromise<DeleteMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Message Feedback
     * Create feedback for a message.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MessageFeedbackPublic Successful Response
     * @throws ApiError
     */
    public static createMessageFeedback(data: CreateMessageFeedbackData): CancelablePromise<CreateMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/message-feedback/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class MetricsService {
    /**
     * Read Metrics
     * Retrieve metrics.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.resourceId
     * @param data.startDate
     * @param data.endDate
     * @returns MetricsPublic Successful Response
     * @throws ApiError
     */
    public static readMetrics(data: ReadMetricsData = {}): CancelablePromise<ReadMetricsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/metrics/',
            query: {
                skip: data.skip,
                limit: data.limit,
                resource_id: data.resourceId,
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Metric
     * Create new metric.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MetricPublic Successful Response
     * @throws ApiError
     */
    public static createMetric(data: CreateMetricData): CancelablePromise<CreateMetricResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/metrics/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Metric
     * Get metric by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns MetricPublic Successful Response
     * @throws ApiError
     */
    public static readMetric(data: ReadMetricData): CancelablePromise<ReadMetricResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/metrics/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Metric
     * Update a metric.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns MetricPublic Successful Response
     * @throws ApiError
     */
    public static updateMetric(data: UpdateMetricData): CancelablePromise<UpdateMetricResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/metrics/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Metric
     * Delete a metric.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteMetric(data: DeleteMetricData): CancelablePromise<DeleteMetricResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/metrics/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ModuleSettingService {
    /**
     * Get Module Settings
     * Retrieve all module settings.
     * @returns ModuleSetting Successful Response
     * @throws ApiError
     */
    public static getModuleSettings(): CancelablePromise<GetModuleSettingsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/module_setting/'
        });
    }

}

export class NotificationsService {
    /**
     * List Notifications
     * @param data The data for the request.
     * @param data.requiresAction
     * @param data.timeframe
     * @param data.skip
     * @param data.limit
     * @param data.requestBody
     * @returns NotificationList Successful Response
     * @throws ApiError
     */
    public static listNotifications(data: ListNotificationsData = {}): CancelablePromise<ListNotificationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/notifications/',
            query: {
                requires_action: data.requiresAction,
                timeframe: data.timeframe,
                skip: data.skip,
                limit: data.limit
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Mark Notification Read
     * @param data The data for the request.
     * @param data.notificationId
     * @returns NotificationResponse Successful Response
     * @throws ApiError
     */
    public static markNotificationRead(data: MarkNotificationReadData): CancelablePromise<MarkNotificationReadResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/notifications/{notification_id}/mark-read',
            path: {
                notification_id: data.notificationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Mark All Notifications Read
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static markAllNotificationsRead(data: MarkAllNotificationsReadData = {}): CancelablePromise<MarkAllNotificationsReadResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/notifications/mark-all-read',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class QuotasService {
    /**
     * Create Usage
     * @param data The data for the request.
     * @param data.requestBody
     * @returns TokenUsageResponse Successful Response
     * @throws ApiError
     */
    public static createUsage(data: CreateUsageData): CancelablePromise<CreateUsageResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/quotas/usage',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Usage Quota
     * @param data The data for the request.
     * @param data.userId
     * @returns UsageQuotaResponse Successful Response
     * @throws ApiError
     */
    public static createUsageQuota(data: CreateUsageQuotaData): CancelablePromise<CreateUsageQuotaResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/quotas/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Usage Quota
     * Get usage quota for a specific workspace.
     *
     * Args:
     * user_id: ID of the user
     *
     * Returns:
     * Usage quota details
     * @param data The data for the request.
     * @param data.userId
     * @returns UsageQuotaResponse Successful Response
     * @throws ApiError
     */
    public static getUsageQuota(data: GetUsageQuotaData): CancelablePromise<GetUsageQuotaResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Reset User Quota
     * Reset usage quota for a user.
     *
     * Args:
     * user_id: ID of the user
     *
     * Returns:
     * Reset usage quota details
     * @param data The data for the request.
     * @param data.userId
     * @returns UsageQuotaResponse Successful Response
     * @throws ApiError
     */
    public static resetUserQuota(data: ResetUserQuotaData): CancelablePromise<ResetUserQuotaResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/quotas/{user_id}/reset',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Usage Statistics
     * Get token usage statistics for a workspace.
     *
     * Args:
     * user_id: ID of the user
     * start_date: Optional start date for filtering
     * end_date: Optional end date for filtering
     *
     * Returns:
     * Usage statistics
     * @param data The data for the request.
     * @param data.userId
     * @param data.startDate
     * @param data.endDate
     * @returns UsageStatistics Successful Response
     * @throws ApiError
     */
    public static getUsageStatistics(data: GetUsageStatisticsData): CancelablePromise<GetUsageStatisticsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/statistics',
            path: {
                user_id: data.userId
            },
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Messages Statistics
     * Get message usage statistics for a workspace.
     *
     * Args:
     * workspace_id: ID of the workspace
     * start_date: Optional start date for filtering
     * end_date: Optional end date for filtering
     *
     * Returns:
     * Message statistics including:
     * - Total messages and month-over-month change
     * - Average response time and month-over-month change
     * - Success rate and month-over-month change
     * - Average tokens per message (input/output)
     * - Daily message volume (30-day trend)
     * - Token distribution by message length
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.startDate
     * @param data.endDate
     * @returns MessageStatistics Successful Response
     * @throws ApiError
     */
    public static getMessagesStatistics(data: GetMessagesStatisticsData): CancelablePromise<GetMessagesStatisticsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{workspace_id}/message-statistics',
            path: {
                workspace_id: data.workspaceId
            },
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Quota Info
     * Get quota information for a user.
     *
     * Args:
     * user_id: ID of the user
     * @param data The data for the request.
     * @param data.userId
     * @returns QuotaInfo Successful Response
     * @throws ApiError
     */
    public static getQuotaInfo(data: GetQuotaInfoData): CancelablePromise<GetQuotaInfoResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/quota-info',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class RecommendationsService {
    /**
     * Get Recomendation Overal
     * Get overal recommendation statistics.
     * @returns RecommendationOveralPublic Successful Response
     * @throws ApiError
     */
    public static getRecomendationOveral(): CancelablePromise<GetRecomendationOveralResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/overal'
        });
    }

    /**
     * Read Recommendations
     * Retrieve recommendations.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @param data.resourceId
     * @param data.resourceType
     * @param data.recommendationType
     * @param data.status
     * @param data.startDate
     * @param data.endDate
     * @param data.orderBy
     * @param data.orderDirection
     * @returns RecommendationsPublic Successful Response
     * @throws ApiError
     */
    public static readRecommendations(data: ReadRecommendationsData = {}): CancelablePromise<ReadRecommendationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/',
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search,
                resource_id: data.resourceId,
                resource_type: data.resourceType,
                recommendation_type: data.recommendationType,
                status: data.status,
                start_date: data.startDate,
                end_date: data.endDate,
                order_by: data.orderBy,
                order_direction: data.orderDirection
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Recommendation
     * Create new recommendation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static createRecommendation(data: CreateRecommendationData): CancelablePromise<CreateRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/recommendations/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Recommendation
     * Get recommendation by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static readRecommendation(data: ReadRecommendationData): CancelablePromise<ReadRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Recommendation
     * Update a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static updateRecommendation(data: UpdateRecommendationData): CancelablePromise<UpdateRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Recommendation
     * Delete a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteRecommendation(data: DeleteRecommendationData): CancelablePromise<DeleteRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Recommendation Status
     * Update the status of a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @param data.status
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static updateRecommendationStatus(data: UpdateRecommendationStatusData): CancelablePromise<UpdateRecommendationStatusResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/recommendations/{id}/status',
            path: {
                id: data.id
            },
            query: {
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ReportsService {
    /**
     * Get Savings Summary
     * Get total potential savings for the workspace with comparison between two periods.
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @param data.previousStartDate
     * @param data.previousEndDate
     * @returns SavingSummaryReport Successful Response
     * @throws ApiError
     */
    public static getSavingsSummary(data: GetSavingsSummaryData = {}): CancelablePromise<GetSavingsSummaryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/saving-summary',
            query: {
                start_date: data.startDate,
                end_date: data.endDate,
                previous_start_date: data.previousStartDate,
                previous_end_date: data.previousEndDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Savings By Resource
     * Get savings data grouped by date for RDS and EC2 resources.
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @returns ResourceSavingsReport Successful Response
     * @throws ApiError
     */
    public static getSavingsByResource(data: GetSavingsByResourceData = {}): CancelablePromise<GetSavingsByResourceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/resource-saving',
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Top Potential Savings
     * Get top N recommendations with highest potential savings that are in PENDING status
     * for the current workspace within the specified date range.
     * @param data The data for the request.
     * @param data.limit
     * @param data.startDate Start date in ISO format
     * @param data.endDate End date in ISO format
     * @returns TopSavingsReport Successful Response
     * @throws ApiError
     */
    public static getTopPotentialSavings(data: GetTopPotentialSavingsData = {}): CancelablePromise<GetTopPotentialSavingsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/top-potential-savings',
            query: {
                limit: data.limit,
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Savings By Service
     * Get savings data grouped by service type for pie chart visualization.
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @returns ServiceSavingsReport Successful Response
     * @throws ApiError
     */
    public static getSavingsByService(data: GetSavingsByServiceData = {}): CancelablePromise<GetSavingsByServiceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/service-savings',
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ResourcesService {
    /**
     * Read Resources
     * Retrieve resources.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.name
     * @param data.resourceType
     * @param data.status
     * @param data.region
     * @returns ResourcesPublic Successful Response
     * @throws ApiError
     */
    public static readResources(data: ReadResourcesData = {}): CancelablePromise<ReadResourcesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/',
            query: {
                skip: data.skip,
                limit: data.limit,
                name: data.name,
                resource_type: data.resourceType,
                status: data.status,
                region: data.region
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Resource
     * Create new resource.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ResourcePublic Successful Response
     * @throws ApiError
     */
    public static createResource(data: CreateResourceData): CancelablePromise<CreateResourceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/resources/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Resource
     * Get resource by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ResourceRead Successful Response
     * @throws ApiError
     */
    public static readResource(data: ReadResourceData): CancelablePromise<ReadResourceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Resource
     * Update a resource.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ResourcePublic Successful Response
     * @throws ApiError
     */
    public static updateResource(data: UpdateResourceData): CancelablePromise<UpdateResourceResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/resources/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Resource
     * Delete a resource.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteResource(data: DeleteResourceData): CancelablePromise<DeleteResourceResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/resources/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class SampleDataService {
    /**
     * Create Sample Resources
     * Generate sample resources and metrics for testing.
     *
     * Args:
     * resource_type: Type of resource (EC2, RDS, etc.)
     * resource_count: Number of resources to generate
     * metrics_per_resource: Number of metric points per resource
     * days_back: Number of days to generate metrics for
     * @param data The data for the request.
     * @param data.resourceType
     * @param data.resourceCount
     * @param data.metricsPerResource
     * @param data.daysBack
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static createSampleResources(data: CreateSampleResourcesData): CancelablePromise<CreateSampleResourcesResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/resources/{resource_type}',
            path: {
                resource_type: data.resourceType
            },
            query: {
                resource_count: data.resourceCount,
                metrics_per_resource: data.metricsPerResource,
                days_back: data.daysBack
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Sample Metrics
     * Create sample metrics for specified resource type.
     *
     * Parameters:
     * - resource_type: Type of resource (EC2, RDS, etc.)
     * - num_points: Number of data points to generate per metric
     * - days_back: Number of days to generate data for
     * @param data The data for the request.
     * @param data.resourceType
     * @param data.numPoints
     * @param data.daysBack
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static createSampleMetrics(data: CreateSampleMetricsData): CancelablePromise<CreateSampleMetricsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/metrics/{resource_type}',
            path: {
                resource_type: data.resourceType
            },
            query: {
                num_points: data.numPoints,
                days_back: data.daysBack
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Sample Recommendations
     * @param data The data for the request.
     * @param data.totalRecord
     * @returns RecommendationsPublic Successful Response
     * @throws ApiError
     */
    public static createSampleRecommendations(data: CreateSampleRecommendationsData = {}): CancelablePromise<CreateSampleRecommendationsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/recommendations',
            query: {
                total_record: data.totalRecord
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ShareChatService {
    /**
     * Create Share Link
     * Create a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns ShareResponse Successful Response
     * @throws ApiError
     */
    public static createShareLink(data: CreateShareLinkData): CancelablePromise<CreateShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Revoke Share Link
     * Revoke a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static revokeShareLink(data: RevokeShareLinkData): CancelablePromise<RevokeShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Share Link
     * Get a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns ShareResponse Successful Response
     * @throws ApiError
     */
    public static getShareLink(data: GetShareLinkData): CancelablePromise<GetShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Shared Conversation
     * Get message history for a shared conversation by share ID (no authentication required)
     * @param data The data for the request.
     * @param data.shareId
     * @param data.limit
     * @returns MessageHistoryPublic Successful Response
     * @throws ApiError
     */
    public static getSharedConversation(data: GetSharedConversationData): CancelablePromise<GetSharedConversationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/share-chat/conversations/shared/{share_id}',
            path: {
                share_id: data.shareId
            },
            query: {
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class SubscriptionsService {
    /**
     * Get Available Plans
     * Get available plans
     * @returns ProductResponse Successful Response
     * @throws ApiError
     */
    public static getAvailablePlans(): CancelablePromise<GetAvailablePlansResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/plans'
        });
    }

    /**
     * Get User Subscription Status
     * @returns SubscriptionStatus Successful Response
     * @throws ApiError
     */
    public static getUserSubscriptionStatus(): CancelablePromise<GetUserSubscriptionStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/status'
        });
    }

    /**
     * Get Workspace Subscription Status
     * Get subscription status for a workspace
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns SubscriptionStatus Successful Response
     * @throws ApiError
     */
    public static getWorkspaceSubscriptionStatus(data: GetWorkspaceSubscriptionStatusData): CancelablePromise<GetWorkspaceSubscriptionStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/workspace/{workspace_id}/status',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Checkout Session
     * Create a checkout session for subscription
     * @param data The data for the request.
     * @param data.priceId
     * @returns CheckoutSessionResponse Successful Response
     * @throws ApiError
     */
    public static createCheckoutSession(data: CreateCheckoutSessionData): CancelablePromise<CreateCheckoutSessionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/checkout',
            query: {
                price_id: data.priceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get User Payment Methods
     * Get current user's payment methods
     * @param data The data for the request.
     * @param data.paymentType
     * @returns PaymentMethodResponse Successful Response
     * @throws ApiError
     */
    public static getUserPaymentMethods(data: GetUserPaymentMethodsData = {}): CancelablePromise<GetUserPaymentMethodsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/payment-methods',
            query: {
                payment_type: data.paymentType
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get User Invoices
     * Get current user's invoices
     * @param data The data for the request.
     * @param data.limit
     * @param data.status
     * @returns InvoiceResponse Successful Response
     * @throws ApiError
     */
    public static getUserInvoices(data: GetUserInvoicesData = {}): CancelablePromise<GetUserInvoicesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/invoices',
            query: {
                limit: data.limit,
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Submit Enterprise Enquiry
     * Submit an enterprise plan enquiry
     * @param data The data for the request.
     * @param data.requestBody
     * @returns EnterpriseEnquiryMessageResponse Successful Response
     * @throws ApiError
     */
    public static submitEnterpriseEnquiry(data: SubmitEnterpriseEnquiryData): CancelablePromise<SubmitEnterpriseEnquiryResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/enterprise-enquiry',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Submit Plan Change Request
     * Submit a plan change request
     * @param data The data for the request.
     * @param data.requestBody
     * @returns PlanChangeRequestResponse Successful Response
     * @throws ApiError
     */
    public static submitPlanChangeRequest(data: SubmitPlanChangeRequestData): CancelablePromise<SubmitPlanChangeRequestResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/plan-change',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Webhook
     * Handle webhook events from payment provider
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static webhook(): CancelablePromise<WebhookResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/webhook'
        });
    }

    /**
     * Cancel Subscription
     * Cancel subscription for the current user
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static cancelSubscription(): CancelablePromise<CancelSubscriptionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/cancel'
        });
    }

}

export class TasksService {
    /**
     * Create Task
     * Create new task.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static createTask(data: CreateTaskData): CancelablePromise<CreateTaskResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Tasks
     * List tasks with filters.
     * @param data The data for the request.
     * @param data.executionStatus
     * @param data.skip
     * @param data.limit
     * @param data.includeHistory
     * @param data.historyLimit
     * @returns TaskList Successful Response
     * @throws ApiError
     */
    public static listTasks(data: ListTasksData = {}): CancelablePromise<ListTasksResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/',
            query: {
                execution_status: data.executionStatus,
                skip: data.skip,
                limit: data.limit,
                include_history: data.includeHistory,
                history_limit: data.historyLimit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Task
     * Get task by ID.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.includeHistory
     * @param data.historyLimit
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static getTask(data: GetTaskData): CancelablePromise<GetTaskResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            query: {
                include_history: data.includeHistory,
                history_limit: data.historyLimit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Task
     * Update task.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.requestBody
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static updateTask(data: UpdateTaskData): CancelablePromise<UpdateTaskResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Task
     * Delete task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns TaskDeleteResponse Successful Response
     * @throws ApiError
     */
    public static deleteTask(data: DeleteTaskData): CancelablePromise<DeleteTaskResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Task Enable
     * Update task enable.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.enable
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static updateTaskEnable(data: UpdateTaskEnableData): CancelablePromise<UpdateTaskEnableResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/tasks/{task_id}/enable',
            path: {
                task_id: data.taskId
            },
            query: {
                enable: data.enable
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Stop Task Execution
     * Stop an executing task.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.conversationId
     * @returns TaskStopResponse Successful Response
     * @throws ApiError
     */
    public static stopTaskExecution(data: StopTaskExecutionData): CancelablePromise<StopTaskExecutionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/{task_id}/stop',
            path: {
                task_id: data.taskId
            },
            query: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Task Progress
     * Get task execution progress by conversation id.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.conversationId
     * @returns TaskExecutionStatus Successful Response
     * @throws ApiError
     */
    public static getTaskProgress(data: GetTaskProgressData): CancelablePromise<GetTaskProgressResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}/progress',
            path: {
                task_id: data.taskId
            },
            query: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class TaskTemplatesService {
    /**
     * Generate
     * Generate the task template based on user's input
     * @param data The data for the request.
     * @param data.input
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static generate(data: GenerateData): CancelablePromise<GenerateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/task_templates/generate',
            query: {
                input: data.input
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Template
     * Create new task template.
     * @param data The data for the request.
     * @param data.requestBody
     * @param data.isDefault
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static createTemplate(data: CreateTemplateData): CancelablePromise<CreateTemplateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/task_templates/',
            query: {
                is_default: data.isDefault
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Templates
     * List task templates with optional category and service filters.
     * @param data The data for the request.
     * @param data.category
     * @param data.services
     * @param data.includeDefaults
     * @param data.skip
     * @param data.limit
     * @param data.searchQuery
     * @returns TaskTemplateList Successful Response
     * @throws ApiError
     */
    public static listTemplates(data: ListTemplatesData = {}): CancelablePromise<ListTemplatesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/task_templates/',
            query: {
                category: data.category,
                services: data.services,
                include_defaults: data.includeDefaults,
                skip: data.skip,
                limit: data.limit,
                search_query: data.searchQuery
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Template
     * Get template by ID.
     * @param data The data for the request.
     * @param data.templateId
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static getTemplate(data: GetTemplateData): CancelablePromise<GetTemplateResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Template
     * Update template.
     * @param data The data for the request.
     * @param data.templateId
     * @param data.requestBody
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static updateTemplate(data: UpdateTemplateData): CancelablePromise<UpdateTemplateResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Template
     * Delete template.
     * @param data The data for the request.
     * @param data.templateId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteTemplate(data: DeleteTemplateData): CancelablePromise<DeleteTemplateResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ToolsService {
    /**
     * Script Execution
     * Script execution to the bash environment.
     * This method is mainly purpose to execute aws-cli tools to interacting with aws resources.
     * Example script: aws s3 ls --output table --region ap-southeast-1
     * @param data The data for the request.
     * @param data.script
     * @returns ScriptExecutionResponse Successful Response
     * @throws ApiError
     */
    public static scriptExecution(data: ScriptExecutionData): CancelablePromise<ScriptExecutionResponse2> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tools/run',
            query: {
                script: data.script
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class UsersService {
    /**
     * Read Users
     * Retrieve users based on workspace relationship.
     * Only returns users that belong to the current user's active workspace.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns UsersPublic Successful Response
     * @throws ApiError
     */
    public static readUsers(data: ReadUsersData = {}): CancelablePromise<ReadUsersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create User
     * Create new user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static createUser(data: CreateUserData): CancelablePromise<CreateUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/users/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read User Me
     * Get current user.
     * @returns UserDetail Successful Response
     * @throws ApiError
     */
    public static readUserMe(): CancelablePromise<ReadUserMeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/me'
        });
    }

    /**
     * Delete User Me
     * Delete own user.
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteUserMe(): CancelablePromise<DeleteUserMeResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/me'
        });
    }

    /**
     * Update User Me
     * Update own user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static updateUserMe(data: UpdateUserMeData): CancelablePromise<UpdateUserMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Password Me
     * Update own password.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static updatePasswordMe(data: UpdatePasswordMeData): CancelablePromise<UpdatePasswordMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me/password',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read User By Id
     * Get a specific user by id.
     * @param data The data for the request.
     * @param data.userId
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static readUserById(data: ReadUserByIdData): CancelablePromise<ReadUserByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update User
     * Update a user.
     * @param data The data for the request.
     * @param data.userId
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static updateUser(data: UpdateUserData): CancelablePromise<UpdateUserResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete User
     * Delete a user.
     * @param data The data for the request.
     * @param data.userId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteUser(data: DeleteUserData): CancelablePromise<DeleteUserResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Switch Workspace
     * Allow user to get new token for a different workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static switchWorkspace(data: SwitchWorkspaceData): CancelablePromise<SwitchWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/switch-workspace/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class UtilsService {
    /**
     * Test Email
     * Test emails.
     * @param data The data for the request.
     * @param data.emailTo
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static testEmail(data: TestEmailData): CancelablePromise<TestEmailResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/test-email/',
            query: {
                email_to: data.emailTo
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Health Check
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static healthCheck(): CancelablePromise<HealthCheckResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/health-check/'
        });
    }

    /**
     * Publish Message
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static publishMessage(data: PublishMessageData): CancelablePromise<PublishMessageResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/publish/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Enqueue Message
     * @param data The data for the request.
     * @param data.taskName
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static enqueueMessage(data: EnqueueMessageData): CancelablePromise<EnqueueMessageResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/enqueue/',
            query: {
                task_name: data.taskName
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class WorkflowsService {
    /**
     * Read Workflows
     * Retrieve workflows.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns WorkflowsPublic Successful Response
     * @throws ApiError
     */
    public static readWorkflows(data: ReadWorkflowsData = {}): CancelablePromise<ReadWorkflowsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workflow
     * Create new workflow.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static createWorkflow(data: CreateWorkflowData): CancelablePromise<CreateWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workflow
     * Get workflow by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static readWorkflow(data: ReadWorkflowData): CancelablePromise<ReadWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Workflow
     * Update a workflow.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static updateWorkflow(data: UpdateWorkflowData): CancelablePromise<UpdateWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workflows/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Workflow
     * Delete a workflow.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteWorkflow(data: DeleteWorkflowData): CancelablePromise<DeleteWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workflows/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workflow From Template
     * Create new workflow from YAML template.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static createWorkflowFromTemplate(data: CreateWorkflowFromTemplateData): CancelablePromise<CreateWorkflowFromTemplateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/template',
            query: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workflow Node
     * Create new workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.requestBody
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static createWorkflowNode(data: CreateWorkflowNodeData): CancelablePromise<CreateWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/{workflow_id}/nodes/',
            path: {
                workflow_id: data.workflowId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workflow Nodes
     * Retrieve workflow nodes.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.skip
     * @param data.limit
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static readWorkflowNodes(data: ReadWorkflowNodesData): CancelablePromise<ReadWorkflowNodesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/{workflow_id}/nodes/',
            path: {
                workflow_id: data.workflowId
            },
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workflow Node
     * Get workflow node by ID.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static readWorkflowNode(data: ReadWorkflowNodeData): CancelablePromise<ReadWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Workflow Node
     * Update a workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @param data.requestBody
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static updateWorkflowNode(data: UpdateWorkflowNodeData): CancelablePromise<UpdateWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Workflow Node
     * Delete a workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteWorkflowNode(data: DeleteWorkflowNodeData): CancelablePromise<DeleteWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Run Workflow Node
     * Run a specific workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static runWorkflowNode(data: RunWorkflowNodeData): CancelablePromise<RunWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}/run',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Run Workflow
     * Run an entire workflow.
     * @param data The data for the request.
     * @param data.workflowId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static runWorkflow(data: RunWorkflowData): CancelablePromise<RunWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/{workflow_id}/run',
            path: {
                workflow_id: data.workflowId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class WorkspacesService {
    /**
     * Read Workspaces
     * Retrieve workspaces - both owned and invited (non-deleted only).
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns WorkspacesPublic Successful Response
     * @throws ApiError
     */
    public static readWorkspaces(data: ReadWorkspacesData = {}): CancelablePromise<ReadWorkspacesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workspaces/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workspace
     * Create new workspace. Only users who already own workspaces or superusers can create new ones.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static createWorkspace(data: CreateWorkspaceData): CancelablePromise<CreateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workspaces/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workspace
     * Get Workspace by ID. Accessible by workspace owner and invited users.
     * @param data The data for the request.
     * @param data.id
     * @returns WorkspaceDetail Successful Response
     * @throws ApiError
     */
    public static readWorkspace(data: ReadWorkspaceData): CancelablePromise<ReadWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workspaces/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Workspace
     * Update a workspace. Only workspace owners can perform this action.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static updateWorkspace(data: UpdateWorkspaceData): CancelablePromise<UpdateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workspaces/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Workspace
     * Delete a workspace. Only workspace owners can perform this action.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteWorkspace(data: DeleteWorkspaceData): CancelablePromise<DeleteWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workspaces/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}
