export enum TaskCategory {
  COST_OPTIMIZE = 'COST_OPTIMIZE',
  OPERATIONAL = 'OPERATIONAL',
  SCALABILITY = 'SCALABILITY',
  SECURITY = 'SECURITY',
  OPERATIONAL_EFFICIENCY = "OPERATIONAL_EFFICIENCY",
  OTHER = "OTHER"  // Default category for uncategorized tasks
}

export enum TaskService {
  OTHER = 'OTHER',
  ALL = "ALL",
  COMPUTE = "COMPUTE",
  STORAGE = "STORAGE",
  SERVERLESS = "SERVERLESS",
  DATABASE = "DATABASE",
  NETWORK = "NETWORK",
  MESSAGING = "MESSAGING",
  MANAGEMENT = "MANAGEMENT",
  BILLING = "BILLING",
  CROSS_SERVICE = "CROSS_SERVICE",
  MONITORING = "MONITORING",
  STREAMING = "STREAMING",
  SECURITY = "SECURITY",
}

export enum RunModeEnum {
  AUTONOMOUS = 'AUTONOMOUS',
  AGENT = 'AGENT',
} 