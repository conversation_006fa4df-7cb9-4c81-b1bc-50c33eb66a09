import { TaskCategory, TaskService } from '@/constants/task';
import {
  DollarSign,
  Settings,
  TrendingUp,
  Shield,
  HelpCircle,
  Server,
  Database,
  Zap,
  HardDrive,
  Network,
  type LucideIcon,
  Video,
  Monitor,
  MessageCircle
} from 'lucide-react';

export const getCategoryLabel = (category: string): string => {
  const labels: Record<string, string> = {
    'COST_OPTIMIZE': 'Cost Optimization',
    'OPERATIONAL': 'Operational',
    'SCALABILITY': 'Scalability',
    'SECURITY': 'Security',
    'OPERATIONAL_EFFICIENCY': 'Operational Efficiency',
    'OTHER': 'Other',
    'ALL': 'All',
  };
  return labels[category] || category;
};

export const getServiceLabel = (service: string): string => {
  const labels: Record<string, string> = {
    'COMPUTE': 'Compute',
    'STORAGE': 'Storage',
    'SERVERLESS': 'Serverless',
    'DATABASE': 'Database',
    'NETWORK': 'Network',
    'MESSAGING': 'Messaging',
    'MANAGEMENT': 'Management',
    'BILLING': 'Billing',
    'CROSS_SERVICE': 'Cross Service',
    'MONITORING': 'Monitoring',
    'STREAMING': 'Streaming',
    'SECURITY': 'Security',
    'OTHER': 'Other',
    'ALL': 'All',
  };
  return labels[service] || service;
};

export const getRunModeLabel = (runMode: string): string => {
  const labels: Record<string, string> = {
    'AUTONOMOUS': 'Autonomous',
    'AGENT': 'Agent',
    'autonomous': 'Autonomous',
    'agent': 'Agent',
  };
  return labels[runMode] || runMode;
};

export const getCategoryIcon = (category: string): LucideIcon => {
  const icons: Record<string, LucideIcon> = {
    'COST_OPTIMIZE': DollarSign,
    'OPERATIONAL': Settings,
    'SCALABILITY': TrendingUp,
    'SECURITY': Shield,
    'OPERATIONAL_EFFICIENCY': Settings,
    'OTHER': HelpCircle,
    'ALL': Server,
  };
  return icons[category] || HelpCircle;
};

export const getServiceIcon = (service: string): LucideIcon => {
  const icons: Record<string, LucideIcon> = {
    'COMPUTE': Server,
    'STORAGE': HardDrive,
    'SERVERLESS': Zap,
    'DATABASE': Database,
    'NETWORK': Network,
    'MESSAGING': MessageCircle,
    'MANAGEMENT': Settings,
    'BILLING': DollarSign,
    'CROSS_SERVICE': Server,
    'MONITORING': Monitor,
    'STREAMING': Video,
    'SECURITY': Shield,
    'OTHER': HelpCircle,
    'ALL': Server,
  };
  return icons[service] || HelpCircle;
}; 