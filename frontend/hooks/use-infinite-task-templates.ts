import { useInfiniteQuery } from '@tanstack/react-query';
import { TaskTemplatesService, ListTemplatesData, ListTemplatesResponse } from '@/client';
import { CacheKey } from '@/components/utils/cache-key';

const DEFAULT_PAGE_SIZE = 20;

export interface UseInfiniteTaskTemplatesParams extends Omit<ListTemplatesData, 'skip' | 'limit'> {
  pageSize?: number;
}

/**
 * Custom hook to fetch task templates with infinite scrolling support.
 * 
 * @param params - Parameters for filtering and searching task templates
 * @returns The infinite query result with task templates data
 */
export function useInfiniteTaskTemplates(params: UseInfiniteTaskTemplatesParams = {}) {
  const pageSize = params.pageSize ?? DEFAULT_PAGE_SIZE;

  return useInfiniteQuery({
    queryKey: [CacheKey.TaskTemplates, 'infinite', params] as const,
    queryFn: async ({ pageParam = 0 }) => {
      const response = await TaskTemplatesService.listTemplates({
        ...params,
        skip: pageParam * pageSize,
        limit: pageSize,
      });
      return response;
    },
    getNextPageParam: (lastPage, allPages) => {
      // If we have less items than the page size, we've reached the end
      if (lastPage.data.length < pageSize) {
        return undefined;
      }
      // Return the next page number
      return allPages.length;
    },
    initialPageParam: 0,
  });
} 