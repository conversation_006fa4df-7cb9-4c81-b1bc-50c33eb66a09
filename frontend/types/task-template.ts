import { TaskCategoryEnum, TaskService<PERSON>num, RunModeEnum, TaskCouldEnum } from '@/client';

export interface TaskTemplate {
  id: string;
  task: string;
  category: TaskCategoryEnum;
  service: TaskServiceEnum;
  service_name: string;
  cloud: TaskCouldEnum;
  run_mode: RunModeEnum;
  schedule: string | null;
  context: string;
  is_default: boolean;
  created_at: string;
  updated_at: string | null;
}

export interface TaskTemplateCreate {
  task: string;
  category: TaskCategoryEnum;
  service: TaskServiceEnum;
  service_name: string;
  cloud: TaskCouldEnum;
  run_mode: RunModeEnum;
  schedule?: string;
  context: string;
}

export interface TaskTemplateUpdate {
  task?: string;
  category?: TaskCategoryEnum;
  service?: TaskServiceEnum;
  service_name?: string;
  cloud?: TaskCouldEnum;
  run_mode?: RunModeEnum;
  schedule?: string;
  context?: string;
}

export interface TaskTemplateList {
  data: TaskTemplate[];
  total: number;
}

export interface TaskTemplateFilters {
  category?: TaskCategoryEnum;
  service?: TaskServiceEnum;
  include_defaults?: boolean;
  skip?: number;
  limit?: number;
} 