{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "\n", "\n", "import nest_asyncio\n", "from crawl4ai import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Browser<PERSON>onfig,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    CrawlerRun<PERSON>onfig,\n", "    RateLimiter,\n", ")\n", "from crawl4ai.async_dispatcher import SemaphoreDispatcher\n", "from crawl4ai.content_filter_strategy import PruningContentFilter\n", "from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy\n", "from crawl4ai.deep_crawling import BestFirstCrawlingStrategy\n", "from crawl4ai.deep_crawling.filters import (\n", "    ContentTypeFilter,\n", "    Domain<PERSON>ilter,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    URL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator\n", "from crawl4ai.deep_crawling import BFSDeepCrawlStrategy\n", "\n", "from crawl4ai.models import CrawlResult\n", "\n", "from app.core.config import settings\n", "\n", "nest_asyncio.apply()\n", "from crawl4ai import URLFilter\n", "\n", "class FirstTimeURLFilter(URLFilter):\n", "    \"\"\"Filter that accepts a URL the first time it is seen and rejects subsequent occurrences.\"\"\"\n", "\n", "    __slots__ = (\"seen_urls\",)\n", "\n", "    def __init__(self):\n", "        super().__init__(name=\"FirstTimeURLFilter\")\n", "        self.seen_urls = set()\n", "\n", "    def apply(self, url: str) -> bool:\n", "        if url in self.seen_urls:\n", "            # print(f\"❌  URL already seen: {url}\")\n", "            self._update_stats(False)\n", "            return False\n", "        else:\n", "            # print(f\"✅ URL not seen: {url}\")\n", "            self.seen_urls.add(url)\n", "            self._update_stats(True)\n", "            return True\n", "\n", "\n", "def get_crawling_config(deep_crawl: bool = True) -> CrawlerRunConfig:\n", "    config = CrawlerRunConfig(\n", "        markdown_generator=DefaultMarkdownGenerator(\n", "            content_filter=PruningContentFilter(\n", "                threshold_type=\"dynamic\",\n", "            )\n", "        ),\n", "        scraping_strategy=LXMLWebScrapingStrategy(),\n", "        scan_full_page=True,\n", "        scroll_delay=0.5,\n", "        verbose=False,\n", "        # Performance optimizations\n", "        cache_mode=CacheMode.BYPASS,  # Use caching for better performance\n", "        wait_for_images=True,  # Don't wait for images to load\n", "        check_robots_txt=True,  # Check robots.txt for compliance\n", "        mean_delay=0.1,  # Average delay between requests\n", "        max_range=0.1,  # Random variation in delay\n", "        remove_overlay_elements=True,  # Remove any overlay elements that might block content\n", "        # # Content filtering\n", "        exclude_internal_links=False,\n", "        exclude_external_links=True,\n", "        exclude_social_media_links=True,\n", "        remove_forms=True,\n", "        exclude_external_images=True,\n", "        magic=True,\n", "        page_timeout=60000,  # Timeout for a page in milliseconds\n", "        screenshot=False,\n", "        pdf=False,\n", "        excluded_tags=['form', 'header', 'img'],\n", "        stream=False,\n", "    )\n", "\n", "    # Only add deep crawling strategy if enabled\n", "    if deep_crawl:\n", "        config.deep_crawl_strategy = BestFirstCrawlingStrategy(\n", "            max_depth=settings.MAX_DEPTH,\n", "            include_external=False,\n", "            max_pages=settings.MAX_PAGES,\n", "            # score_threshold=0.3,\n", "            # filter_chain=FilterChain(\n", "            #     filters=[\n", "            #         FirstTimeURLFilter(),\n", "            #     ]\n", "            # )\n", "        )\n", "\n", "    return config\n", "\n", "\n", "_browser_config = BrowserConfig(headless=True, use_managed_browser=True, browser_type=\"chromium\")\n", "_dispatcher = SemaphoreDispatcher(\n", "    max_session_permit=20,\n", "    rate_limiter=RateLimiter(base_delay=(0.5, 1.0), max_delay=5.0),\n", ")\n", "\n", "\n", "async def main():\n", "    urls = [\n", "        \"https://langchain-ai.github.io/langmem/\",\n", "        # \"https://docs.aws.amazon.com/cli/latest/reference/pi/\"\n", "        # \"https://docs.cloudthinker.io/how-to-guide/empowering-your-ai-agents-with-company-knowledge-cloudthinker-knowledge-base-guide/\"\n", "    ]\n", "    deep_crawl = True\n", "\n", "    async with AsyncWebCrawler(config=_browser_config) as crawler:\n", "        res_all_urls: list[CrawlResult] = await crawler.arun_many(\n", "            urls=urls,\n", "            config=get_crawling_config(deep_crawl=deep_crawl),\n", "            dispatcher=_dispatcher,\n", "        )\n", "\n", "        return res_all_urls\n", "\n", "\n", "res_all_urls = asyncio.run(main())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from rich import print\n", "\n", "print(len(res_all_urls))\n", "\n", "for i in res_all_urls:\n", "    print(i.url)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# print(res_all_urls[0].markdown.fit_markdown)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import os\n", "from base64 import b64decode\n", "\n", "# Create a directory to store screenshots\n", "os.makedirs(\"crawled_screenshots\", exist_ok=True)\n", "\n", "# Loop through each result and save screenshots\n", "for idx, result in enumerate(res_all_urls):\n", "    screenshot_filename = os.path.join(\"crawled_screenshots\", f\"screenshot_{idx}.png\")\n", "    with open(screenshot_filename, \"wb\") as f:\n", "        f.write(b64decode(result.screenshot))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import os\n", "from base64 import b64decode\n", "\n", "# Create a directory to store PDFs\n", "os.makedirs(\"crawled_pdfs\", exist_ok=True)\n", "\n", "# Loop through each result and save PDFs\n", "for idx, result in enumerate(res_all_urls):\n", "    pdf_filename = os.path.join(\"crawled_pdfs\", f\"page_{idx}.pdf\")\n", "    with open(pdf_filename, \"wb\") as f:\n", "        f.write(result.pdf)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import json\n", "import os\n", "import xml.etree.ElementTree as ET\n", "from urllib.parse import urljoin, urlparse\n", "import aiohttp\n", "from aiofiles import open as aio_open\n", "from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode\n", "from crawl4ai.content_filter_strategy import PruningContentFilter\n", "from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator\n", "\n", "# Configuration\n", "SITEMAP_URL = \"https://langchain-ai.github.io/langmem/sitemap.xml\"  # Change this to your sitemap URL\n", "MAX_DEPTH = 2  # Limit recursion depth\n", "BATCH_SIZE = 4  # Number of concurrent crawls\n", "REQUEST_DELAY = 1  # Delay between requests (seconds)\n", "MAX_FILE_SIZE_MB = 20  # Max file size before creating a new one\n", "OUTPUT_DIR = \"cnn\"  # Directory to store multiple output files\n", "RETRY_LIMIT = 1  # Retry failed URLs once\n", "LOG_FILE = os.path.join(OUTPUT_DIR, \"crawler_log.txt\")  # Log file for general logging\n", "ERROR_LOG_FILE = os.path.join(OUTPUT_DIR, \"logfile.txt\")  # Log file for failed URLs\n", "\n", "# Ensure output directory exists\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "async def log_message(message, file_path=LOG_FILE):\n", "    \"\"\"Log messages to a log file and print them to the console.\"\"\"\n", "    async with aio_open(file_path, \"a\", encoding=\"utf-8\") as f:\n", "        await f.write(message + \"\\n\")\n", "    print(message)\n", "\n", "async def fetch_sitemap(sitemap_url):\n", "    \"\"\"Fetch and parse sitemap.xml to extract all URLs.\"\"\"\n", "    try:\n", "        async with aiohttp.ClientSession() as session:\n", "            async with session.get(sitemap_url) as response:\n", "                if response.status == 200:\n", "                    xml_content = await response.text()\n", "                    root = ET.fromstring(xml_content)\n", "                    urls = [elem.text for elem in root.findall(\".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc\")]\n", "\n", "                    if not urls:\n", "                        await log_message(\"❌ No URLs found in the sitemap.\")\n", "                    return urls\n", "                else:\n", "                    await log_message(f\"❌ Failed to fetch sitemap: HTTP {response.status}\")\n", "                    return []\n", "    except Exception as e:\n", "        await log_message(f\"❌ Error fetching sitemap: {str(e)}\")\n", "        return []\n", "\n", "async def get_file_size(file_path):\n", "    \"\"\"Returns the file size in MB.\"\"\"\n", "    if os.path.exists(file_path):\n", "        return os.path.getsize(file_path) / (1024 * 1024)  # Convert bytes to MB\n", "    return 0\n", "\n", "async def get_new_file_path(file_prefix, extension):\n", "    \"\"\"Generates a new file path when the current file exceeds the max size.\"\"\"\n", "    index = 1\n", "    while True:\n", "        file_path = os.path.join(OUTPUT_DIR, f\"{file_prefix}_{index}.{extension}\")\n", "        if not os.path.exists(file_path) or await get_file_size(file_path) < MAX_FILE_SIZE_MB:\n", "            return file_path\n", "        index += 1\n", "\n", "async def write_to_file(data, file_prefix, extension):\n", "    \"\"\"Writes a single JSON object as a line to a file, ensuring size limit.\"\"\"\n", "    file_path = await get_new_file_path(file_prefix, extension)\n", "    async with aio_open(file_path, \"a\", encoding=\"utf-8\") as f:\n", "        await f.write(json.dumps(data, ensure_ascii=False) + \"\\n\")\n", "\n", "async def write_to_txt(data, file_prefix):\n", "    \"\"\"Writes extracted content to a TXT file while managing file size.\"\"\"\n", "    file_path = await get_new_file_path(file_prefix, \"txt\")\n", "    async with aio_open(file_path, \"a\", encoding=\"utf-8\") as f:\n", "        await f.write(f\"URL: {data['url']}\\nTitle: {data['title']}\\nContent:\\n{data['content']}\\n\\n{'='*80}\\n\\n\")\n", "\n", "async def write_failed_url(url):\n", "    \"\"\"Logs failed URLs to a separate error log file.\"\"\"\n", "    async with aio_open(ERROR_LOG_FILE, \"a\", encoding=\"utf-8\") as f:\n", "        await f.write(url + \"\\n\")\n", "\n", "async def crawl_url(url, depth, semaphore, visited_urls, queue, total_urls, completed_urls, retry_count=0):\n", "    \"\"\"Crawls a single URL, handles retries, logs failed URLs, and extracts child links.\"\"\"\n", "    async with semaphore:\n", "        await asyncio.sleep(REQUEST_DELAY)  # Rate limiting\n", "        run_config = CrawlerRunConfig(\n", "            cache_mode=CacheMode.BYPASS,\n", "            markdown_generator=DefaultMarkdownGenerator(\n", "                content_filter=PruningContentFilter(threshold=0.5, threshold_type=\"fixed\")\n", "            ),\n", "            stream=True,\n", "            remove_overlay_elements=True,\n", "            exclude_social_media_links=True,\n", "            process_iframes=True,\n", "        )\n", "\n", "        async with AsyncWebCrawler() as crawler:\n", "            try:\n", "                result = await crawler.arun(url=url, config=run_config)\n", "                if result.success:\n", "                    data = {\n", "                        \"url\": result.url,\n", "                        \"title\": result.markdown.raw_markdown.split(\"\\n\")[0] if result.markdown.raw_markdown else \"No Title\",\n", "                        \"content\": result.markdown.fit_markdown,\n", "                    }\n", "\n", "                    # Save extracted data\n", "                    await write_to_file(data, \"sitemap_data\", \"jsonl\")\n", "                    await write_to_txt(data, \"sitemap_data\")\n", "\n", "                    completed_urls[0] += 1  # Increment completed count\n", "                    await log_message(f\"✅ {completed_urls[0]}/{total_urls} - Successfully crawled: {url}\")\n", "\n", "                    # Extract and queue child pages\n", "                    for link in result.links.get(\"internal\", []):\n", "                        href = link[\"href\"]\n", "                        absolute_url = urljoin(url, href)  # Convert to absolute URL\n", "                        if absolute_url not in visited_urls:\n", "                            queue.append((absolute_url, depth + 1))\n", "                else:\n", "                    await log_message(f\"⚠️ Failed to extract content from: {url}\")\n", "\n", "            except Exception as e:\n", "                if retry_count < RETRY_LIMIT:\n", "                    await log_message(f\"🔄 Retrying {url} (Attempt {retry_count + 1}/{RETRY_LIMIT}) due to error: {str(e)}\")\n", "                    await crawl_url(url, depth, semaphore, visited_urls, queue, total_urls, completed_urls, retry_count + 1)\n", "                else:\n", "                    await log_message(f\"❌ Skipping {url} after {RETRY_LIMIT} failed attempts.\")\n", "                    await write_failed_url(url)\n", "\n", "async def crawl_sitemap_urls(urls, max_depth=MAX_DEPTH, batch_size=BATCH_SIZE):\n", "    \"\"\"Crawls all URLs from the sitemap and follows child links up to max depth.\"\"\"\n", "    if not urls:\n", "        await log_message(\"❌ No URLs to crawl. Exiting.\")\n", "        return\n", "\n", "    total_urls = len(urls)  # Total number of URLs to process\n", "    completed_urls = [0]  # Mutable count of completed URLs\n", "    visited_urls = set()\n", "    queue = [(url, 0) for url in urls]\n", "    semaphore = asyncio.Semaphore(batch_size)  # Concurrency control\n", "\n", "    while queue:\n", "        tasks = []\n", "        batch = queue[:batch_size]\n", "        queue = queue[batch_size:]\n", "\n", "        for url, depth in batch:\n", "            if url in visited_urls or depth >= max_depth:\n", "                continue\n", "            visited_urls.add(url)\n", "            tasks.append(crawl_url(url, depth, semaphore, visited_urls, queue, total_urls, completed_urls))\n", "\n", "        await asyncio.gather(*tasks)\n", "\n", "async def main():\n", "    # Clear previous logs\n", "    async with aio_open(LOG_FILE, \"w\") as f:\n", "        await f.write(\"\")\n", "    async with aio_open(ERROR_LOG_FILE, \"w\") as f:\n", "        await f.write(\"\")\n", "\n", "    # Fetch URLs from the sitemap\n", "    urls = await fetch_sitemap(SITEMAP_URL)\n", "\n", "    if not urls:\n", "        await log_message(\"❌ Exiting: No valid URLs found in the sitemap.\")\n", "        return\n", "\n", "    await log_message(f\"✅ Found {len(urls)} pages in the sitemap. Starting crawl...\")\n", "\n", "    # Start crawling\n", "    await crawl_sitemap_urls(urls)\n", "\n", "    await log_message(f\"✅ Crawling complete! Files stored in {OUTPUT_DIR}\")\n", "\n", "# Execute\n", "asyncio.run(main())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}