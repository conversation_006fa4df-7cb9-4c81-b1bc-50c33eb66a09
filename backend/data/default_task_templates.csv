#,Task,Description,Primary Agent,Category,Cloud,Service,Service Name,Priority,Impact
1,📊 EC2 Instance Right-sizing,@alex please analyze EC2 instances with <20% CPU utilization over past 14 days. #recommend right-sizing options and #alert with potential cost savings from downsizing.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
2,🔒 EC2 Security Group Audit,@olivier please review all security groups for overly permissive rules (0.0.0.0/0). #alert instances with unrestricted access on sensitive ports and #recommend secure configurations.,@olivier,SECURITY,AWS,COMPUTE,EC2,3,3
3,💤 Idle EC2 Instance Detection,@alex please identify EC2 instances with <5% average CPU utilization over 30 days. #chart utilization patterns and #recommend termination or scheduling for non-production resources.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
4,🔄 Auto Scaling Group Optimization,@alex please analyze EC2 Auto Scaling groups for proper utilization thresholds. #recommend adjustments to scaling policies based on actual usage patterns to prevent over-provisioning.,@alex,SCALABILITY,AWS,COMPUTE,EC2,3,3
5,🛡️ EC2 IMDSv2 Compliance Check,@olivier please identify EC2 instances not configured for IMDSv2 (metadata service). #alert on any instances using IMDSv1 and #recommend remediation steps to enforce IMDSv2.,@olivier,SECURITY,AWS,COMPUTE,EC2,3,3
6,🔄 EC2 Reserved Instance Coverage,@alex please analyze EC2 usage patterns and identify opportunities for Reserved Instance purchases. #recommend optimal RI configurations to reduce on-demand costs.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
7,🔒 EC2 Patch Compliance,@olivier please check EC2 instances for missing critical security patches. #alert on instances with outdated security patches and #recommend remediation steps.,@olivier,SECURITY,AWS,COMPUTE,EC2,3,3
8,📈 EC2 Instance Performance Analysis,@alex please identify EC2 instances experiencing >80% sustained CPU utilization. #recommend scaling options or instance type changes to improve performance.,@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EC2,3,3
9,🔒 EC2 IAM Role Audit,@olivier please review IAM roles attached to EC2 instances for excessive permissions. #recommend least-privilege adjustments and #alert on instances with admin policies.,@olivier,SECURITY,AWS,COMPUTE,EC2,3,3
10,🛡️ EC2 Vulnerability Scanning,@olivier please coordinate vulnerability scans on EC2 instances using Inspector. #alert on critical findings and #recommend remediation priorities.,@olivier,SECURITY,AWS,COMPUTE,EC2,3,3
11,🔄 EC2 Savings Plan Opportunity,@alex please analyze EC2 usage patterns and #recommend Savings Plan commitments that would optimize costs compared to on-demand or RIs.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
12,📊 RDS Performance Analysis,@tony please analyze RDS instances with consistently high CPU/memory usage (>80%). #chart resource utilization trends and #recommend optimization actions to prevent performance bottlenecks.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,RDS,3,3
13,💰 RDS Cost Optimization,"@alex please identify overprovisioned RDS instances (CPU <20%, memory <30% for 14+ days). #recommend right-sizing options and potential savings from downsizing or switching instance types.",@alex,COST_OPTIMIZE,AWS,DATABASE,RDS,3,3
14,🔒 RDS Security Audit,"@olivier please audit RDS security configurations for public accessibility, encryption status, and SSL enforcement. #alert on instances with security gaps and #recommend remediation steps.",@olivier,SECURITY,AWS,DATABASE,RDS,3,3
15,🔄 RDS Backup Verification,@tony please verify automated backups are properly configured for all production RDS instances and test restoration procedures. #alert on instances with missing or failed backups.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,RDS,3,3
16,📈 CloudFront Cache Hit Ratio Optimization,@alex please analyze CloudFront distributions with cache hit ratios below 80%. #recommend cache behavior adjustments including TTL settings and cache key modifications to improve content delivery efficiency.,@alex,OPERATIONAL_EFFICIENCY,AWS,NETWORK,CloudFront,3,3
17,🔒 CloudFront Security Headers Audit,"@olivier please review CloudFront distributions for missing security headers (HSTS, X-XSS-Protection, Content-Security-Policy). #recommend header configurations to implement via Lambda@Edge or origin response headers.",@olivier,SECURITY,AWS,NETWORK,CloudFront,3,3
18,💰 CloudFront Cost Optimization Analysis,@alex please identify high-traffic CloudFront distributions and analyze data transfer patterns. #recommend price class adjustments and usage strategy to optimize costs without sacrificing performance.,@alex,COST_OPTIMIZE,AWS,NETWORK,CloudFront,3,3
19,🔐 CloudFront SSL/TLS Certificate Expiration Check,@olivier please review custom SSL certificates attached to CloudFront distributions and identify any expiring within 30 days. #alert team of upcoming expirations and #recommend renewal process.,@olivier,SECURITY,AWS,NETWORK,CloudFront,3,3
20,🛡️ WAF Integration Review for CloudFront,@olivier please audit CloudFront distributions for proper AWS WAF integration and rule configurations. #recommend WAF rule adjustments and identify distributions needing additional protection.,@olivier,SECURITY,AWS,NETWORK,CloudFront,3,3
21,🔒 HTTPS Enforcement Check,@olivier please verify all CloudFront distributions enforce HTTPS and use appropriate security policies. #recommend updates for distributions using outdated TLS versions or allowing HTTP.,@olivier,SECURITY,AWS,NETWORK,CloudFront,3,3
22,🔒 DDoS Protection Assessment,@olivier please review CloudFront distributions for proper AWS Shield integration and DDoS mitigation configurations. #recommend security improvements for high-risk distributions.,@olivier,SECURITY,AWS,NETWORK,CloudFront,3,3
23,🔔 Missing Critical Alarms Audit,@alex please identify production resources missing standard CloudWatch alarms for critical metrics. #recommend alarm templates for each unmonitored service and #alert team leads of coverage gaps.,@alex,OPERATIONAL_EFFICIENCY,AWS,MONITORING,CloudWatch,3,3
24,🚨 Critical Service SLA Monitoring,@alex please configure composite alarms for our customer-facing APIs to track SLA compliance. #recommend dashboards to visualize performance against SLAs.,@alex,OPERATIONAL_EFFICIENCY,AWS,MONITORING,CloudWatch,3,3
25,🔐 Security Event Monitoring,@olivier please configure CloudWatch alarms for suspicious API calls and login attempts. #recommend integration with our security response workflow.,@olivier,SECURITY,AWS,MONITORING,CloudWatch,3,3
26,🔒 GuardDuty Integration,@olivier please integrate GuardDuty findings with CloudWatch for automated alerting. #recommend severity-based notification workflows.,@olivier,SECURITY,AWS,MONITORING,CloudWatch,3,3
27,📊 CloudWatch Operational Excellence Audit,"@anna please conduct a comprehensive review of our CloudWatch implementation. #plan improvements to coverage, alerting efficiency, and dashboard usability.",@anna,OPERATIONAL_EFFICIENCY,AWS,MONITORING,CloudWatch,3,3
28,🔒 S3 Public Access Audit,@olivier please scan all S3 buckets for public access settings and configurations. #alert on any buckets with public read/write permissions that don't align with documented requirements.,@olivier,SECURITY,AWS,STORAGE,S3,3,3
29,💰 S3 Storage Class Optimization,@alex please analyze objects in S3 buckets larger than 1GB that haven't been accessed in 30+ days. #recommend appropriate storage class transitions to reduce costs.,@alex,COST_OPTIMIZE,AWS,STORAGE,S3,3,3
30,🔐 S3 Encryption Compliance,"@olivier please verify all S3 buckets have default encryption enabled and match company policy for encryption types (SSE-S3, SSE-KMS, etc). #alert on non-compliant buckets.",@olivier,SECURITY,AWS,STORAGE,S3,3,3
31,🛑 S3 Bucket Policy Audit,@olivier please review all bucket policies against security best practices. #alert on policies with overly permissive access or potential security risks.,@olivier,SECURITY,AWS,STORAGE,S3,3,3
32,🚫 S3 Block Public Access Enforcement,@olivier please verify account-level S3 Block Public Access settings and #alert on individual bucket overrides that may create exposure.,@olivier,SECURITY,AWS,STORAGE,S3,3,3
33,💾 EBS Snapshot Lifecycle Management,@alex please audit and optimize EBS snapshot retention policies. #recommend a tiered approach based on volume criticality and usage patterns.,@alex,COST_OPTIMIZE,AWS,STORAGE,EBS,3,3
34,💰 Unused EBS Volume Detection,@alex please identify unattached EBS volumes older than 30 days. #alert with cost analysis and #recommend deletion candidates.,@alex,COST_OPTIMIZE,AWS,STORAGE,EBS,3,3
35,🔄 EBS Volume Type Optimization,"@alex please analyze IO patterns and identify volumes that should be migrated between gp2, gp3, io1, io2 types to optimize cost. #recommend specific conversions with projected savings.",@alex,COST_OPTIMIZE,AWS,STORAGE,EBS,3,3
36,🛡️ EBS Encryption Compliance Audit,"@olivier please identify all unencrypted EBS volumes, especially those with sensitive data classifications. #alert security team about non-compliant volumes.",@olivier,SECURITY,AWS,STORAGE,EBS,3,3
37,🔒 EBS Snapshot Permission Audit,@olivier please review all public and shared EBS snapshots to ensure no confidential data is exposed. #alert on any potentially insecure configurations.,@olivier,SECURITY,AWS,STORAGE,EBS,3,3
38,📊 DynamoDB Capacity Analysis,@tony please analyze provisioned vs actual consumed capacity for all DynamoDB tables over the last 30 days. #recommend tables suitable for switching between on-demand and provisioned capacity models.,@tony,COST_OPTIMIZE,AWS,DATABASE,DynamoDB,3,3
39,🔒 DynamoDB Access Pattern Review,@tony please review query patterns from CloudTrail logs to identify frequently accessed data. #recommend optimal partition key strategy for high-traffic tables.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,DynamoDB,3,3
40,📈 DynamoDB Auto-Scaling Configuration,@tony please audit DynamoDB tables using provisioned capacity and #recommend auto-scaling settings based on usage patterns over the last 14 days.,@tony,SCALABILITY,AWS,DATABASE,DynamoDB,3,3
41,🔍 DynamoDB Hot Partition Detection,@tony please identify tables experiencing throttling events in the last 7 days. #chart partition access patterns and #recommend key distribution improvements.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,DynamoDB,3,3
42,💰 DynamoDB Reserved Capacity Analysis,@tony please analyze DynamoDB usage patterns and #recommend reserved capacity purchase options with ROI calculations for tables with stable workloads.,@tony,COST_OPTIMIZE,AWS,DATABASE,DynamoDB,3,3
43,🛡️ DynamoDB IAM Permission Audit,@olivier please review all IAM policies with DynamoDB access and #alert on overly permissive policies. #recommend least-privilege policy templates.,@olivier,SECURITY,AWS,DATABASE,DynamoDB,3,3
44,🔒 VPC Flow Logs Audit,@olivier please review VPC flow logs for unusual traffic patterns or unauthorized access attempts. #alert on suspicious activities. #recommend security group rule updates based on findings.,@olivier,SECURITY,AWS,NETWORK,VPC,3,3
45,🔄 Subnet CIDR Utilization Assessment,@alex please analyze subnet IP address utilization across all VPCs. #chart utilization percentages and #recommend subnet resizing where utilization exceeds 80% to prevent address exhaustion.,@alex,OPERATIONAL_EFFICIENCY,AWS,NETWORK,VPC,3,3
46,🛡️ Security Group Rule Review,"@olivier please identify overly permissive security group rules (e.g., 0.0.0.0/0) and redundant rules. #recommend specific changes to follow least privilege principles.",@olivier,SECURITY,AWS,NETWORK,VPC,3,3
47,🧪 VPC Endpoint Assessment,@alex please identify services using public endpoints that could use VPC endpoints instead. #recommend Gateway or Interface endpoints to enhance security and reduce NAT Gateway costs.,@alex,COST_OPTIMIZE,AWS,NETWORK,VPC Endpoints,3,3
48,🧱 Network Firewall Rule Assessment,@olivier please review AWS Network Firewall rules for effectiveness and potential optimizations. #recommend rule updates based on actual traffic patterns.,@olivier,SECURITY,AWS,NETWORK,Network Firewall,3,3
49,🚧 Network Isolation Verification,"@olivier please verify proper isolation between production, development, and test VPCs. #alert on any unauthorized cross-environment access paths.",@olivier,SECURITY,AWS,NETWORK,VPC,3,3
51,🔄 Route Propagation Verification,"@alex please verify proper route propagation between Transit Gateway, VPN connections, and Direct Connect. #alert on missing or incorrect routes.",@alex,OPERATIONAL_EFFICIENCY,AWS,NETWORK,Routing,3,3
52,🚦 Cross-Region Traffic Cost Analysis,@alex please analyze cross-region data transfer costs. #recommend architectural changes to reduce expensive cross-region traffic.,@alex,COST_OPTIMIZE,AWS,NETWORK,Data Transfer,3,3
54,🛡️ AWS Network Access Analyzer Assessment,@olivier please run Network Access Analyzer on all VPCs. #alert on unintended network paths and potential security issues.,@olivier,SECURITY,AWS,NETWORK,VPC,3,3
55,📱 Disaster Recovery Network Readiness Assessment,@anna please evaluate network configurations for disaster recovery scenarios and #plan improvements to ensure rapid failover capabilities.,@anna,OPERATIONAL_EFFICIENCY,AWS,NETWORK,VPC,3,3
56,⚡ Lambda Cold Start Analysis,@alex please identify Lambda functions with high cold start times (>1s). #recommend runtime optimization strategies and memory adjustments to reduce latency.,@alex,OPERATIONAL_EFFICIENCY,AWS,SERVERLESS,Lambda,3,3
57,💰 Lambda Cost Optimization,@alex please identify Lambda functions with excessive timeout settings (>10s) or over-provisioned memory. #recommend right-sizing based on CloudWatch metrics to reduce costs.,@alex,COST_OPTIMIZE,AWS,SERVERLESS,Lambda,3,3
58,🔒 Lambda Security Role Review,@olivier please audit IAM roles attached to Lambda functions for excessive permissions. #recommend least-privilege role adjustments to enhance security posture.,@olivier,SECURITY,AWS,SERVERLESS,Lambda,3,3
59,🧪 Lambda Error Monitoring,@alex please review CloudWatch logs for Lambda functions with error rates >1% in the past 7 days. #alert team with detailed error patterns and suggested fixes.,@alex,OPERATIONAL_EFFICIENCY,AWS,SERVERLESS,Lambda,3,3
60,🔍 Lambda Throttling Detection,@alex please identify functions experiencing throttling events in the past week and #recommend concurrency settings or architecture changes.,@alex,SCALABILITY,AWS,SERVERLESS,Lambda,3,3
61,🔁 Lambda Recursive Call Detection,@alex please identify Lambda functions with potential recursive invocation patterns that could cause runaway executions. #alert team with remediation steps.,@alex,OPERATIONAL_EFFICIENCY,AWS,SERVERLESS,Lambda,3,3
62,🔄 CloudFormation Drift Detection,@alex please scan all CloudFormation stacks for drift. #alert on resources that have been modified outside IaC process. Document drifted resources and recommend remediation plan.,@alex,OPERATIONAL_EFFICIENCY,AWS,MANAGEMENT,CloudFormation,3,3
63,🔒 Template Security Scan,"@olivier please audit CloudFormation templates for security vulnerabilities. #recommend improvements based on AWS security best practices. Focus on IAM permissions, encryption settings, and network access controls.",@olivier,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
64,📊 Cost Optimization Analysis,@alex please analyze CloudFormation stacks to identify redundant or oversized resources. #recommend changes to reduce costs while maintaining performance. #chart cost savings potential by stack.,@alex,COST_OPTIMIZE,AWS,MANAGEMENT,CloudFormation,3,3
65,🔑 IAM Policy Optimization,@olivier please review IAM policies in CloudFormation templates. #recommend least-privilege adjustments following AWS best practices and security standards.,@olivier,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
66,🔒 Network Security Review,"@olivier please audit network configurations in CloudFormation templates. #alert on overly permissive security groups, NACLS and #recommend security improvements.",@olivier,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
67,🔒 Secrets Management Strategy,"@olivier please audit handling of secrets in CloudFormation templates. #recommend secure approaches for managing database passwords, API keys, and other sensitive data.",@olivier,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
68,🔒 VPC Configuration Review,"@olivier please audit VPC configurations in CloudFormation templates. #recommend security improvements for subnetting, routing, and network access controls.",@olivier,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
69,🔒 RDS Configuration Audit,"@tony please review RDS database configurations in CloudFormation templates. #recommend security improvements for encryption, network access, and parameter groups.",@tony,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
70,🔒 S3 Bucket Configuration Review,"@olivier please audit S3 bucket configurations in CloudFormation templates. #recommend security improvements for access policies, encryption, and public access blocks.",@olivier,SECURITY,AWS,MANAGEMENT,CloudFormation,3,3
71,🔄 Deletion Policy Audit,@alex please review deletion policies for critical resources in CloudFormation. #recommend appropriate Retain/Snapshot settings to prevent data loss during stack deletion.,@alex,OPERATIONAL_EFFICIENCY,AWS,MANAGEMENT,CloudFormation,3,3
72,🔄 Stack Update Strategy,@anna please develop comprehensive strategy for safely updating critical stacks. #plan implementation of blue-green updates for zero-downtime changes when possible.,@anna,OPERATIONAL_EFFICIENCY,AWS,MANAGEMENT,CloudFormation,3,3
73,📊 SQS Dead-Letter Queue Analysis,@alex please analyze dead-letter queues for messages >24 hours old. #recommend retry strategy improvements and #alert on queues exceeding 1000 dead messages.,@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,SQS,3,3
74,🔒 SQS Queue Security Audit,@olivier please review SQS queue access policies for overly permissive settings. #recommend least privilege access policies and #alert on queues with public access or '*' permissions.,@olivier,SECURITY,AWS,COMPUTE,SQS,3,3
75,📉 SQS Queue Throttling Detection,@alex please analyze SendMessage and ReceiveMessage throttling metrics. #alert on queues experiencing frequent throttling events.,@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,SQS,3,3
76,📊 SQS Consumer Scaling Analysis,@kai please analyze queue depth patterns to determine optimal consumer scaling parameters. #recommend auto-scaling group or ECS service configurations.,@kai,SCALABILITY,AWS,COMPUTE,SQS,3,3
77,🔍 SQS Dead-Letter Queue Processing Strategy,@alex please evaluate DLQ reprocessing mechanisms. #recommend automated handling of failed messages based on error patterns.,@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,SQS,3,3
78,🚀 SQS Lambda Integration Optimization,@kai please evaluate Lambda concurrency settings for SQS event sources. #recommend adjustments to prevent throttling during traffic spikes.,@kai,SCALABILITY,AWS,COMPUTE,SQS,3,3
79,🔒 SQS Sensitive Data Audit,@olivier please scan messages for PII or sensitive data patterns. #alert on queues potentially containing unencrypted sensitive information.,@olivier,SECURITY,AWS,COMPUTE,SQS,3,3
80,🔒 IAM Unused Access Keys,@olivier please identify IAM access keys not used in 90+ days. #recommend rotating or deleting unused keys. #alert for keys older than 180 days.,@olivier,SECURITY,AWS,SECURITY,IAM,3,3
81,🔑 Root Account MFA Check,@olivier please verify all root accounts have MFA enabled. #alert immediately for any accounts without MFA protection. #recommend implementing physical MFA devices for root accounts.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
82,👥 Privileged Access Review,@olivier please audit all users with admin/owner permissions across all environments. #recommend privilege reduction for accounts not requiring elevated access.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
83,📊 IAM Policy Analysis,"@olivier please analyze IAM policies for overly permissive settings (e.g., ""*"" permissions). #recommend least-privilege alternatives. #chart permission distribution by department.",@olivier,SECURITY,AWS,SECURITY,IAM,3,3
84,🏢 RBAC Implementation Check,@olivier please verify Role-Based Access Control implementation across Kubernetes clusters. #recommend adjustments to align with least privilege.,@olivier,SECURITY,ALL,SECURITY,IAM/K8s,3,3
85,📊 Access Patterns Analysis,@olivier please analyze IAM CloudTrail logs to identify unusual access patterns. #alert on potential security issues. #recommend adjustments to policies.,@olivier,SECURITY,AWS,SECURITY,IAM/CloudTrail,3,3
86,🔒 Service Control Policy Review,@olivier please audit organization SCPs for effectiveness and coverage. #recommend improvements to enforce security baselines.,@olivier,SECURITY,AWS,SECURITY,IAM/Organizations,3,3
87,🔐 Break Glass Account Review,@olivier please audit emergency access accounts for proper security controls and documentation. #alert if improper controls found.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
88,📱 IAM Access Analyzer Findings,@olivier please review and categorize all IAM Access Analyzer findings. #recommend remediation actions for external access issues.,@olivier,SECURITY,AWS,SECURITY,IAM,3,3
89,🏛️ Compliance Policy Evaluation,"@olivier please check IAM configurations against compliance frameworks (SOC2, HIPAA, etc.). #chart compliance gaps. #recommend remediation steps.",@olivier,SECURITY,ALL,SECURITY,IAM,3,3
90,🛡️ IAM Permissions Guardian,@anna please coordinate quarterly permissions review with all team leads. #plan implementation of least-privilege policy updates across all accounts.,@anna,SECURITY,ALL,SECURITY,IAM,3,3
91,🔐 Secrets Access Audit,@olivier please review IAM permissions for secrets/credentials stores. #recommend implementing stricter access controls.,@olivier,SECURITY,ALL,SECURITY,IAM/Secrets,3,3
92,🔐 IAM Drift Detection,@olivier please implement continuous monitoring for unauthorized IAM policy changes. #alert on unapproved modifications to critical roles.,@olivier,SECURITY,AWS,SECURITY,IAM,3,3
93,🔑 Customer Managed Key Access,@olivier please review IAM permissions to KMS customer managed keys. #recommend rotating keys with overly broad access.,@olivier,SECURITY,AWS,SECURITY,IAM/KMS,3,3
94,🔍 Third-Party Access Review,@olivier please audit all third-party integrations with IAM access. #recommend implementing more restrictive scopes where possible.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
95,🛡️ IAM Emergency Access Process,@olivier please review and test emergency access procedures. #recommend improvements for break-glass accounts.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
96,🔑 Root Account Activity Monitor,@olivier please implement monitoring for root account usage. #alert immediately on any root account activity.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
98,🔐 Permission Guardrails Review,@olivier please audit guardrail implementation via SCPs and permission boundaries. #recommend additional guardrails for high-risk actions.,@olivier,SECURITY,AWS,SECURITY,IAM/Organizations,3,3
99,📋 Cloud IAM Admin Inventory,@olivier please identify all users with IAM admin capabilities. #recommend reducing admin count and implementing approval workflows.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
100,🔑 Account Takeover Protection,@olivier please implement additional security controls for privileged accounts. #recommend MFA enforcement and suspicious activity alerts.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
101,📊 IAM Risk Assessment,@olivier please perform risk scoring on current IAM configurations. #chart risk scores by account/department. #recommend addressing highest risks.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
102,🔑 Privileged Identity Management,@olivier please implement just-in-time privileged access workflows. #recommend PIM solutions for temporary elevated access.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
103,📋 IAM Access Review Process,@anna please design and #plan quarterly access review process. Coordinate with department heads to validate permissions alignment.,@anna,SECURITY,ALL,SECURITY,IAM,3,3
104,🔐 IAM Anomaly Detection,@olivier please implement baseline behavioral analysis for IAM usage patterns. #alert on anomalous access attempts or permission changes.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
105,🔍 Zero Trust IAM Implementation,@olivier please evaluate current IAM model against zero trust principles. #recommend steps toward zero-trust architecture implementation.,@olivier,SECURITY,ALL,SECURITY,IAM,3,3
106,💰 Monthly Cost Trend Analysis,@anna please analyze monthly cost trends over the past 6 months. #chart spending patterns by service to identify growth areas and #recommend mitigation strategies for unexpected increases.,@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
107,💸 Idle Resource Detection,@alex please identify unused or idle resources across all regions. #alert on resources with less than 1% utilization over 14 days and #recommend immediate action items for cost savings.,@alex,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
108,📊 RI Coverage Analysis,@anna please analyze current Reserved Instance coverage and utilization. #chart service categories that would benefit from additional RI purchases and #recommend optimal RI types based on usage patterns.,@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
109,🔍 Service-Specific Cost Spike Analysis,@anna please investigate any service showing >20% cost increase from previous month. #chart the top contributing factors and #recommend immediate actions to control spending.,@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
110,🔎 Right-sizing Opportunities,@alex please identify EC2 instances with <20% CPU utilization over 14 days. #recommend right-sizing options with projected savings amounts for each recommendation.,@alex,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
111,📝 Savings Plan Purchase Recommendations,"@anna please analyze compute usage patterns and #recommend optimal Savings Plan purchases. Include commitment amounts, terms, and projected savings percentages.",@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
112,🔎 Reserved Instance Purchase Analysis,"@anna please analyze on-demand usage patterns and #recommend strategic RI purchases. Include instance types, terms, and projected ROI for each recommendation.",@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
113,🔍 Unused Reserved Instance Analysis,@anna please identify underutilized Reserved Instances. #chart utilization percentages and #recommend instance type modifications or exchange opportunities.,@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
114,📝 Savings Plan Exchange Opportunities,@anna please analyze current Savings Plans for exchange opportunities. #recommend potential exchanges to align with changing workload characteristics.,@anna,COST_OPTIMIZE,AWS,MANAGEMENT,Cost Explorer,3,3
116,📊 AWS Budget Dashboard,@alex please configure AWS Budgets dashboard showing monthly spend by service with YoY comparison. #chart monthly trends and #recommend visualization improvements.,@alex,COST_OPTIMIZE,AWS,CROSS_SERVICE,Budgets,3,3
120,💰 Cost Anomaly Detection Setup,@alex please configure cost anomaly detection for all accounts to identify unusual spending patterns. #alert when costs exceed 20% of forecasted budgets.,@alex,COST_OPTIMIZE,AWS,,AWS Cost Explorer,3,3
121,📊 Monthly Billing Dashboard,"@anna please create a comprehensive monthly billing dashboard showing cost breakdowns by service, team, and project. #chart key spending trends quarter-over-quarter.",@anna,COST_OPTIMIZE,AWS,,AWS Cost Explorer,3,3
122,💸 Reserved Instance Utilization Report,@alex please analyze RI/Savings Plans utilization and coverage rates. #recommend adjustments to improve commitment discount utilization above 90%.,@alex,COST_OPTIMIZE,AWS,,AWS Cost Explorer,3,3
123,⚠️ Budget Alert Configuration,"@alex please establish budget thresholds with 70%, 85%, and 100% spending alerts for all cost centers. #alert relevant teams when thresholds are crossed.",@alex,COST_OPTIMIZE,AWS,,AWS Budgets,3,3
124,📈 Savings Plan Purchase Analysis,@alex please analyze current and forecasted usage to #recommend optimal Savings Plan purchases for the next commitment period.,@alex,COST_OPTIMIZE,AWS,,AWS Savings Plans,3,3
125,💡 Cost Optimization Recommendations,@alex please analyze AWS Cost Explorer recommendations and #recommend top 10 actions with highest ROI for implementation.,@alex,COST_OPTIMIZE,AWS,,AWS Cost Explorer,3,3
126,🔄 Rightsizing Recommendation Engine,@alex please implement automated rightsizing recommendation engine using AWS Compute Optimizer data. #recommend monthly instance adjustments.,@alex,COST_OPTIMIZE,AWS,,AWS Compute Optimizer,3,3
127,💼 Financial Governance Framework,@anna please develop a cloud financial governance framework with #plan for implementation across departments.,@anna,COST_OPTIMIZE,ALL,,Multi-Cloud,3,3
128,🧠 ML Cost Optimization Engine,@anna please research implementation of machine learning for cost prediction and #plan deployment of automated cost optimization engine.,@anna,COST_OPTIMIZE,AWS,,AWS Cost Explorer,3,3
131,📝 Cost-Based Architectural Review,@alex please analyze highest-cost applications and #recommend architectural changes to improve cost efficiency by 20%+.,@alex,COST_OPTIMIZE,AWS,,AWS Cost Explorer,3,3
132,💻 Enterprise Discount Program Review,@anna please evaluate eligibility for enhanced enterprise discount programs and #plan negotiation strategy with cloud providers.,@anna,COST_OPTIMIZE,ALL,,Multi-Cloud,3,3
133,📅 Fiscal Year Budget Planning,@anna please prepare cloud budget forecasts for next fiscal year and #plan allocation strategy by department.,@anna,COST_OPTIMIZE,ALL,,Multi-Cloud,3,3
134,📊 Executive Cost Dashboard,@anna please create executive dashboard summarizing key cost metrics and optimization initiatives. #chart progress toward cost optimization goals quarterly.,@anna,COST_OPTIMIZE,ALL,,Multi-Cloud,3,3
135,📱 SNS Topic Permission Audit,@olivier please review all SNS topic access policies for overly permissive settings. #recommend security improvements and #alert on any topics with public access.,@olivier,SECURITY,AWS,,SNS,3,3
136,📊 SNS Cost Analysis,@alex please analyze SNS message delivery costs across regions. #chart monthly spending trends and #recommend cost optimization strategies for high-volume topics.,@alex,COST_OPTIMIZE,AWS,,SNS,3,3
137,🔔 Dead-Letter Queue Setup,@alex please identify SNS topics without configured DLQs. #recommend appropriate SQS dead-letter queues for each topic to capture failed message deliveries.,@alex,OPERATIONAL_EFFICIENCY,AWS,,SNS,3,3
138,🛡️ SNS Encryption Verification,@olivier please check all SNS topics for server-side encryption. #alert on unencrypted topics and #recommend encryption implementation using KMS.,@olivier,SECURITY,AWS,,SNS,3,3
139,⚠️ SNS Delivery Status Monitoring,@alex please set up delivery status logging for critical SNS topics. #recommend CloudWatch alarms for failed deliveries exceeding acceptable thresholds.,@alex,OPERATIONAL_EFFICIENCY,AWS,,SNS,3,3
140,🔐 SNS IAM Policy Review,@olivier please audit IAM policies granting SNS publish/subscribe permissions. #alert on overly permissive policies and #recommend least-privilege alternatives.,@olivier,SECURITY,AWS,,SNS,3,3
141,📊 EMR Cluster Right-Sizing,@alex please analyze EMR clusters to identify oversized instances. #recommend optimal instance types based on historical Hadoop/Spark job metrics and memory/CPU utilization patterns.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EMR,3,3
142,🔄 EMR Spot Instance Strategy,@alex please evaluate current EMR clusters for Spot instance opportunities. #recommend configuration changes to incorporate Spot instances for task nodes while maintaining core nodes on On-Demand for stability.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EMR,3,3
143,🛡️ EMR Security Configuration Audit,"@olivier please review EMR security configurations for encryption settings, EMRFS S3 access, and IAM roles. #alert on clusters with at-rest or in-transit encryption disabled and #recommend security best practices.",@olivier,SECURITY,AWS,COMPUTE,EMR,3,3
144,📈 EMR Cluster Autoscaling Setup,@kai please configure autoscaling policies for EMR clusters based on YARN memory usage and pending containers. #recommend appropriate min/max bounds and scaling rules to optimize for workload patterns.,@kai,SCALABILITY,AWS,COMPUTE,EMR,3,3
145,⏱️ EMR Job Runtime Optimization,"@alex please analyze EMR job execution metrics to identify long-running or failing jobs. #recommend configuration changes to partition size, executor memory, or parallelism settings to improve performance.",@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EMR,3,3
146,🔍 EMR Cluster Idle Detection,@alex please identify EMR clusters with no active jobs for >2 hours. #alert on potentially forgotten running clusters and #recommend automatic termination policies for non-production environments.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EMR,3,3
147,🔒 EMR Network Security Audit,"@olivier please review EMR subnet configurations, security groups, and private link settings. #alert on clusters with public IPs or overly permissive security group rules.",@olivier,SECURITY,AWS,NETWORK,EMR,3,3
148,🔧 EMR Instance Type Assessment,"@alex please analyze EMR workload patterns. #recommend instance families (compute-optimized, memory-optimized, general purpose) based on job profiles and resource utilization.",@alex,COST_OPTIMIZE,AWS,COMPUTE,EMR,3,3
149,🔧 EMR Configuration Optimization,"@alex please audit EMR configurations for Spark, Hadoop, and Hive. #recommend parameter adjustments for memory allocation, parallelism, and IO settings based on workload patterns.",@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EMR,3,3
150,🔍 Aurora CPU Utilization Analysis,@tony please analyze Aurora cluster CPU utilization patterns over the past 14 days. #chart the usage patterns and #recommend instance size adjustments where average utilization is below 30% or above 70%.,@tony,COST_OPTIMIZE,AWS,DATABASE,Aurora,3,3
151,🛡️ Aurora Security Group Audit,@olivier please review all security groups attached to Aurora instances. #alert on any overly permissive rules (0.0.0.0/0) and #recommend least-privilege configurations.,@olivier,SECURITY,AWS,DATABASE,Aurora,3,3
152,📊 Aurora Performance Insights Review,@tony please analyze Performance Insights data for our Aurora clusters. #recommend query optimizations for top 5 resource-consuming queries and #chart performance trends.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,Aurora,3,3
153,💾 Aurora Backup Verification,@tony please verify Aurora automated backups are occurring successfully and #alert on any failures in the last 7 days. Also #recommend adjustments to retention periods based on our compliance requirements.,@tony,SECURITY,AWS,DATABASE,Aurora,3,3
154,⚡ Aurora Query Performance Tuning,@tony please identify the top 10 slowest queries in our Aurora instances using Performance Insights. #recommend optimization strategies for each and estimate potential performance improvements.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,Aurora,3,3
155,🔒 Aurora Encryption Compliance Check,@olivier please verify that all Aurora instances have encryption enabled. #alert on any unencrypted instances and #plan remediation steps for compliance.,@olivier,SECURITY,AWS,DATABASE,Aurora,3,3
156,🔄 Aurora Multi-AZ Configuration Review,@alex please audit Aurora clusters to ensure production databases have Multi-AZ enabled. #alert on any critical databases without proper redundancy and #recommend configuration changes.,@alex,OPERATIONAL_EFFICIENCY,AWS,DATABASE,Aurora,3,3
157,🔒 Aurora Network Access Control Review,@olivier please review network access paths to Aurora instances. #alert on any instances accessible from the public internet and #recommend secure access patterns.,@olivier,SECURITY,AWS,DATABASE,Aurora,3,3
158,📊 Redshift Query Optimization,@tony please analyze the top 10 longest-running queries from the past week. #recommend specific query optimization strategies for each. Focus on reducing execution time and resource consumption.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,Redshift,3,3
159,🔍 Redshift Cluster Sizing Review,@alex please evaluate current node type and count against actual usage patterns. #recommend if we should scale up/down or change node types based on workload analysis and cost projections.,@alex,COST_OPTIMIZE,AWS,DATABASE,Redshift,3,3
160,🔒 Redshift Security Audit,"@olivier please audit Redshift security settings including encryption, network access controls, and IAM permissions. #alert on any non-compliant configurations and #recommend necessary changes.",@olivier,SECURITY,AWS,DATABASE,Redshift,3,3
161,💰 Rightsizing for Reserved Node Purchases,@alex please analyze cluster usage to determine optimal node configuration before making Reserved Node purchases. #recommend specific RI type and count.,@alex,COST_OPTIMIZE,AWS,DATABASE,Redshift,3,3
162,📊 OpenSearch Cluster Health Check,"@alex please monitor cluster health status across all domains. #alert if any clusters are in yellow or red state. Check shard allocation, node status, and recovery progress.",@alex,OPERATIONAL_EFFICIENCY,AWS,DATABASE,OpenSearch,3,3
163,🔍 OpenSearch Query Performance Tuning,@tony please analyze slow query logs to identify inefficient queries. #recommend query optimizations and index improvements to reduce latency and resource consumption.,@tony,OPERATIONAL_EFFICIENCY,AWS,DATABASE,OpenSearch,3,3
164,🛡️ OpenSearch Security Audit,"@olivier please verify fine-grained access controls, encryption settings, and network policies. #alert on any open public access and #recommend security best practices.",@olivier,SECURITY,AWS,DATABASE,OpenSearch,3,3
165,🔒 OpenSearch Authentication Review,"@olivier please audit IAM roles, SAML integration, and basic authentication methods. #alert on insecure authentication setups and #recommend least-privilege access controls.",@olivier,SECURITY,AWS,DATABASE,OpenSearch,3,3
166,🌡️ OpenSearch JVM Heap Monitoring,@alex please check JVM heap usage across all domains. #alert when heap usage consistently exceeds 75% and #recommend right-sizing or JVM options adjustments.,@alex,OPERATIONAL_EFFICIENCY,AWS,DATABASE,OpenSearch,3,3
167,🛡️ OpenSearch Field-Level Security Audit,@olivier please verify document and field-level security configurations. #alert on misconfigured permissions that could expose sensitive data.,@olivier,SECURITY,AWS,DATABASE,OpenSearch,3,3
168,🔄 OpenSearch Multi-AZ Configuration Verification,@alex please verify multi-AZ deployments for production domains. #alert on single-AZ configurations and #recommend resiliency improvements.,@alex,OPERATIONAL_EFFICIENCY,AWS,DATABASE,OpenSearch,3,3
169,🛡️ OpenSearch Log4j Vulnerability Scan,@olivier please check OpenSearch versions for Log4j vulnerabilities. #alert on vulnerable versions and #recommend upgrade paths.,@olivier,SECURITY,AWS,DATABASE,OpenSearch,3,3
170,🔄 EKS Version Update Assessment,@kai please evaluate all clusters for EKS version currency. #recommend upgrade paths for clusters >2 versions behind latest and #plan for minimal downtime migrations.,@kai,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EKS,3,3
171,🔒 EKS Security Group Audit,@olivier please review security groups attached to EKS clusters and nodes. #alert on overly permissive inbound rules and #recommend least-privilege configurations.,@olivier,SECURITY,AWS,NETWORK,EKS,3,3
172,💰 Node Group Right-Sizing,@alex please analyze CPU/memory utilization across node groups over last 14 days. #recommend optimal instance types and counts based on actual workload patterns.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EKS,3,3
173,🛡️ EKS Control Plane Logging Verification,@olivier please check that all clusters have appropriate control plane logging enabled. #alert for clusters with insufficient audit/API logs and #recommend complete logging configuration.,@olivier,SECURITY,AWS,COMPUTE,EKS,3,3
174,🚨 EKS Cluster Health Check,@kai please examine cluster health metrics including control plane response times and API latency. #alert on clusters showing degraded performance patterns.,@kai,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EKS,3,3
175,🗺️ EKS Network Policy Implementation,@olivier please audit current network policies and #recommend Calico or AWS VPC CNI policies to enforce pod-to-pod communication security.,@olivier,SECURITY,AWS,NETWORK,EKS,3,3
176,🔐 RBAC Permission Audit,@olivier please review RBAC configurations across all clusters. #alert on overly permissive ClusterRoles and identify users/serviceaccounts with excessive permissions.,@olivier,SECURITY,AWS,COMPUTE,EKS,3,3
177,💾 etcd Backup Verification,@kai please confirm etcd snapshots are being properly created and stored. #alert on clusters without recent successful backups and #recommend backup schedule improvements.,@kai,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EKS,3,3
178,📵 Pod Security Standards Implementation,@olivier please audit namespaces for Pod Security Standards enforcement. #recommend appropriate PSS levels (restricted/baseline) for each namespace based on workload requirements.,@olivier,SECURITY,AWS,COMPUTE,EKS,3,3
179,💼 Reserved Instance Coverage Analysis,@alex please analyze EKS node usage patterns and #recommend appropriate Reserved Instances or Savings Plans for predictable node groups to optimize costs.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EKS,3,3
180,🔍 Image Vulnerability Scanning Integration,@olivier please evaluate ECR image scanning results for EKS workloads. #alert on critical vulnerabilities and #recommend remediation priorities.,@olivier,SECURITY,AWS,COMPUTE,EKS,3,3
181,🔍 Spot Instance Viability Assessment,@alex please analyze workloads for Spot instance compatibility. #recommend node groups that could use spot instances safely with appropriate tolerations and affinities.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EKS,3,3
182,🧪 Cluster Disaster Recovery Testing,@kai please #plan and execute EKS disaster recovery test procedures. Verify ability to restore workloads from backups and #recommend improvements to recovery processes.,@kai,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,EKS,3,3
184,🔒 ECS Task Definition Security Scan,@olivier please audit ECS task definitions for security vulnerabilities and container image CVEs. #alert on critical findings and #recommend remediation steps for each issue.,@olivier,SECURITY,AWS,COMPUTE,ECS,3,3
185,⚖️ ECS Auto Scaling Configuration Review,@alex please evaluate ECS service auto scaling settings. #recommend optimal target tracking metrics and thresholds based on historical usage patterns to improve responsiveness.,@alex,SCALABILITY,AWS,COMPUTE,ECS,3,3
186,🚀 ECS Fargate vs EC2 Cost Analysis,@alex please compare current ECS deployment costs between Fargate and EC2 launch types. #chart cost differences and #recommend optimal mix based on workload patterns.,@alex,COST_OPTIMIZE,AWS,COMPUTE,ECS,3,3
187,🔐 ECS IAM Role Permission Review,@olivier please audit IAM roles attached to ECS tasks and services for least privilege compliance. #alert on overpermissioned roles and #recommend tighter permission boundaries.,@olivier,SECURITY,AWS,COMPUTE,ECS,3,3
189,🔧 ECS Capacity Provider Strategy Review,"@kai please evaluate existing capacity provider strategies. #recommend optimal mix of Fargate, Fargate Spot, and EC2 for different workload types.",@kai,COST_OPTIMIZE,AWS,COMPUTE,ECS,3,3
190,🔒 ECS Network Security Review,"@olivier please audit security groups, NACLs, and VPC settings for ECS tasks. #recommend security improvements and #alert on exposed services.",@olivier,SECURITY,AWS,NETWORK,ECS,3,3
191,🔍 Spot Instance Usage for ECS,@alex please evaluate workloads suitable for Spot instances in ECS. #recommend spot instance strategy for non-critical workloads with estimated cost savings.,@alex,COST_OPTIMIZE,AWS,COMPUTE,ECS,3,3
192,🔒 ECS Container Runtime Security,@olivier please evaluate runtime security controls for ECS containers. #recommend implementation of AWS ECR image scanning and runtime monitoring.,@olivier,SECURITY,AWS,COMPUTE,ECS,3,3
193,⚡ ECS Service Performance Optimization,@kai please identify performance bottlenecks in ECS services. #recommend configuration changes to improve throughput and reduce latency.,@kai,PERFORMANCE,AWS,COMPUTE,ECS,3,3
194,🔍 ECS Container Image Vulnerability Scan,@olivier please scan container images used in ECS for security vulnerabilities. #alert on critical findings and #recommend remediation steps.,@olivier,SECURITY,AWS,COMPUTE,ECS,3,3
195,💰 Savings Plan Analysis for ECS,@alex please analyze ECS compute usage patterns and #recommend optimal Savings Plan commitment for Fargate and EC2 instances.,@alex,COST_OPTIMIZE,AWS,COMPUTE,ECS,3,3
196,🎛️ ECS Service Auto Scaling Tuning,@kai please fine-tune auto scaling policies for ECS services. #recommend appropriate metrics and thresholds based on application characteristics.,@kai,SCALABILITY,AWS,COMPUTE,ECS,3,3
197,🛡️ ECS Task Defense-in-Depth Review,"@olivier please analyze security layers for ECS workloads. #recommend additional security controls including WAF, GuardDuty, and network segmentation.",@olivier,SECURITY,AWS,COMPUTE,ECS,3,3
198,💰 ECS Compute Savings Plan Evaluation,@alex please evaluate compute savings plan utilization for ECS workloads. #recommend adjustments to savings plan commitments based on actual usage.,@alex,COST_OPTIMIZE,AWS,COMPUTE,ECS,3,3
199,⚡ ECS Spot Instance Integration,@alex please evaluate workloads suitable for Spot instance usage. #recommend implementation approach with appropriate task placement strategies.,@alex,COST_OPTIMIZE,AWS,COMPUTE,ECS,3,3
200,📊 ElastiCache Memory Utilization Analysis,@alex please analyze ElastiCache clusters with >85% memory utilization over the past week. #recommend scaling options and #alert on clusters at risk of memory exhaustion.,@alex,OPERATIONAL_EFFICIENCY,AWS,DATABASE,ElastiCache,3,3
201,💰 ElastiCache Instance Right-sizing,@alex please identify ElastiCache nodes with <30% memory utilization for 14+ days. #recommend downsizing options with projected monthly savings.,@alex,COST_OPTIMIZE,AWS,DATABASE,ElastiCache,3,3
202,🔒 ElastiCache Security Group Audit,@olivier please review all ElastiCache security groups for overly permissive access (0.0.0.0/0). #alert on any security groups allowing public access.,@olivier,SECURITY,AWS,DATABASE,ElastiCache,3,3
203,🔒 API Gateway Auth Audit,"@olivier please review all API Gateway endpoints to identify those missing proper authentication. #alert on unprotected endpoints exposed to public internet. Verify OAuth2, API keys, or IAM auth is implemented correctly.",@olivier,SECURITY,AWS,NETWORK,API Gateway,3,3
204,📊 API Gateway Throttling Setup,@alex please configure appropriate throttling limits for high-traffic API endpoints to prevent service disruption. #recommend rate limits based on current usage patterns and #plan implementation with minimal impact.,@alex,OPERATIONAL_EFFICIENCY,AWS,NETWORK,API Gateway,3,3
205,🔍 API Gateway Cost Analysis,@anna please analyze API Gateway costs over the past 30 days. #chart usage by endpoint and identify opportunities to reduce costs through caching or consolidation. Focus on endpoints with highest request counts.,@anna,COST_OPTIMIZE,AWS,NETWORK,API Gateway,3,3
206,🛡️ WAF Integration Check,@olivier please verify WAF integration with API Gateway endpoints. #recommend appropriate rule sets for common attack patterns and #alert on endpoints missing WAF protection.,@olivier,SECURITY,AWS,NETWORK,API Gateway,3,3
207,📈 API Gateway PCI Compliance Audit,@olivier please audit API Gateway configurations for PCI-DSS compliance. #alert on non-compliant endpoints handling payment data and #recommend remediation.,@olivier,SECURITY,AWS,NETWORK,API Gateway,3,3
208,🔒 API Gateway Sensitive Data Handling,@olivier please audit API Gateway for potential exposure of sensitive data. #alert on endpoints returning PII or credentials in responses and #recommend data masking.,@olivier,SECURITY,AWS,NETWORK,API Gateway,3,3
209,🚨 WAF Rules Audit,@olivier please review all WAF rules for comprehensive coverage against OWASP Top 10 threats. #recommend any missing rule sets to strengthen protection.,@olivier,SECURITY,ALL,SECURITY,WAF,3,3
210,📊 WAF Blocking Analysis,@olivier please analyze last 7 days of blocked requests to identify attack patterns. #chart frequency of rule triggers and #recommend adjustments to reduce false positives.,@olivier,SECURITY,ALL,SECURITY,WAF,3,3
211,🔍 WAF False Positive Review,@olivier please investigate user-reported WAF false positives from the last 48 hours. #recommend rule adjustments to allow legitimate traffic while maintaining security.,@olivier,SECURITY,ALL,SECURITY,WAF,3,3
212,🛡️ Zero-Day Protection Update,@olivier please evaluate and implement emerging CVE protections into WAF rule sets. #search for latest WAF mitigations for newly published vulnerabilities.,@olivier,SECURITY,ALL,SECURITY,WAF,3,3
213,📈 WAF Performance Impact,@alex please analyze application response times before and after WAF rule changes. #chart performance metrics and #recommend optimizations if latency exceeds 50ms.,@alex,OPERATIONAL_EFFICIENCY,ALL,NETWORK,WAF,3,3
214,📈 Kinesis Data Streams Capacity Analysis,@alex please analyze shard utilization across all Kinesis Data Streams. #chart throughput patterns and #recommend optimal shard count to balance cost and performance.,@alex,COST_OPTIMIZE,AWS,,Kinesis Data Streams,3,3
215,🔐 Kinesis Security Audit,@olivier please audit IAM policies and encryption settings for all Kinesis services. #alert on any streams without server-side encryption or with overly permissive access.,@olivier,SECURITY,AWS,,Kinesis,3,3
216,🔄 Consumer Application Lag Analysis,@alex please identify consumer applications with increasing lag behind Kinesis streams. #alert on applications approaching record expiration risk.,@alex,OPERATIONAL_EFFICIENCY,AWS,,Kinesis Data Streams,3,3
217,📊 Glue Job Runtime Optimization,"@alex please analyze AWS Glue jobs with runtime exceeding 30 minutes. #recommend configuration adjustments for worker type, number of workers, and memory allocation to reduce costs and improve performance.",@alex,COST_OPTIMIZE,AWS,COMPUTE,Glue,3,3
218,🔒 Glue Security Configuration Audit,@olivier please audit all Glue security configurations for proper encryption settings and S3 security compliance. #alert on any jobs using plaintext passwords or missing encryption. #recommend security best practices for non-compliant configurations.,@olivier,SECURITY,AWS,COMPUTE,Glue,3,3
219,📈 Glue Job Failure Analysis,@alex please investigate any Glue jobs with failure rates >5% in the past 7 days. #recommend remediation steps and error handling improvements to increase success rate.,@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,Glue,3,3
220,🔍 AWS Config Rule Compliance Check,@olivier please audit AWS Config rule compliance across all regions. #alert on any non-compliant resources and #recommend remediation steps for the top 5 most critical violations.,@olivier,SECURITY,AWS,MANAGEMENT,AWS Config,3,3
221,📊 Cloud Resource Configuration Drift,@alex please identify infrastructure resources that have drifted from their approved configurations. #chart the trend of config drift by resource type over the past 30 days.,@alex,OPERATIONAL_EFFICIENCY,AWS,MANAGEMENT,AWS Config,3,3
222,⚠️ Security Group Rule Audit,"@olivier please analyze all security groups for overly permissive rules (0.0.0.0/0). #alert on open ports for SSH (22), RDP (3389), and database services.",@olivier,SECURITY,AWS,NETWORK,Security Groups,3,3
223,🏗️ IAM Role Permission Review,@olivier please review IAM roles for least-privilege violations. #recommend permission boundary adjustments for roles with excessive privileges.,@olivier,SECURITY,AWS,IDENTITY,IAM,3,3
226,🔒 S3 Bucket Configuration Audit,"@olivier please verify all S3 buckets have appropriate encryption, access policies, and logging enabled. #alert on publicly accessible buckets.",@olivier,SECURITY,AWS,STORAGE,S3,3,3
227,🔐 Database Configuration Security Review,"@tony please audit RDS/Aurora instances for configuration risks (SSL enforcement, encryption, public access). #alert on high-risk configurations.",@tony,SECURITY,AWS,DATABASE,RDS,3,3
228,🔒 Kubernetes Cluster Configuration Validation,@kai please audit Kubernetes cluster configurations against CIS benchmarks. #alert on critical misconfigurations and #recommend remediation steps.,@kai,SECURITY,AWS,COMPUTE,EKS,3,3
229,🛡️ WAF Rule Configuration Assessment,@olivier please analyze WAF rules and rule groups across all web applications. #alert on inadequate protection and #recommend additional rules based on threat landscape.,@olivier,SECURITY,AWS,SECURITY,WAF,3,3
231,📊 AWS Organization SCP Analysis,@olivier please review Service Control Policies for policy gaps. #recommend additional guardrails to improve security posture across the organization.,@olivier,SECURITY,AWS,MANAGEMENT,Organizations,3,3
232,🏗️ CloudTrail Configuration Validation,"@olivier please verify CloudTrail configurations across all regions for multi-region trails, log file validation, and S3 bucket settings. #alert on logging gaps.",@olivier,SECURITY,AWS,MANAGEMENT,CloudTrail,3,3
233,🔍 GuardDuty Configuration Validation,@olivier please verify GuardDuty enablement and findings configuration across regions. #alert on accounts or regions with suboptimal protection settings.,@olivier,SECURITY,AWS,SECURITY,GuardDuty,3,3
235,📊 AWS Config Conformance Pack Review,@olivier please evaluate deployed conformance packs against compliance requirements. #recommend additional packs to close compliance gaps.,@olivier,SECURITY,AWS,MANAGEMENT,AWS Config,3,3
236,📊 Fargate Task Utilization Analysis,@alex please analyze Fargate tasks to identify those with <30% CPU/memory utilization over past 14 days. #recommend right-sizing options to reduce costs while maintaining performance margins.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Fargate,3,3
237,🔒 Fargate Security Group Audit,@olivier please review all Fargate security groups for overly permissive rules (0.0.0.0/0). #alert on any exposed ports that should be restricted and #recommend secure configurations.,@olivier,SECURITY,AWS,COMPUTE,Fargate,3,3
238,🔄 Fargate Auto Scaling Configuration,@kai please analyze and optimize Fargate service auto-scaling configurations based on actual workload patterns. #recommend appropriate scaling policies and thresholds for improved efficiency.,@kai,SCALABILITY,AWS,COMPUTE,Fargate,3,3
239,💰 Fargate Spot Implementation,@alex please identify Fargate services suitable for Fargate Spot to reduce costs. #recommend implementation plan for non-critical workloads with 50-70% potential savings.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Fargate,3,3
240,🔍 Secret Management Audit,@olivier please audit how secrets are managed within Fargate tasks. #recommend migration to AWS Secrets Manager or SSM Parameter Store for credentials currently hardcoded in task definitions.,@olivier,SECURITY,AWS,COMPUTE,Fargate,3,3
241,🔒 IAM Role Permission Review,@olivier please audit IAM roles attached to Fargate tasks for excessive permissions. #recommend least-privilege roles based on actual access patterns observed in CloudTrail.,@olivier,SECURITY,AWS,COMPUTE,Fargate,3,3
242,💰 Compute Savings Plan Analysis,@alex please analyze Fargate usage patterns and #recommend optimal Compute Savings Plan commitment for 30-40% cost reduction. #chart projected savings over 1-year term.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Fargate,3,3
243,🔒 Container Vulnerability Scanning,@olivier please implement automated vulnerability scanning for container images using ECR scanning. #alert on critical vulnerabilities and #recommend remediation steps.,@olivier,SECURITY,AWS,COMPUTE,ECR/Fargate,3,3
244,📊 EFS Cost Optimization,@alex please analyze EFS usage patterns and identify filesystems with low utilization (<10% of provisioned throughput). #recommend switching from Provisioned to Bursting mode for eligible systems.,@alex,COST_OPTIMIZE,AWS,STORAGE,EFS,3,3
245,🔐 EFS Security Audit,@olivier please review all EFS access policies and identify any that allow broad public access. #alert on filesystems with overly permissive settings and #recommend least-privilege policy templates.,@olivier,SECURITY,AWS,STORAGE,EFS,3,3
246,🔄 EFS Lifecycle Policy Implementation,@alex please identify EFS filesystems without lifecycle policies and #recommend appropriate policies to move infrequently accessed files to IA storage class after 30 days.,@alex,COST_OPTIMIZE,AWS,STORAGE,EFS,3,3
247,🔒 EFS Encryption Check,@olivier please identify all EFS filesystems without encryption enabled. #alert on non-compliant resources and #plan implementation of encryption for existing data.,@olivier,SECURITY,AWS,STORAGE,EFS,3,3
248,🔄 EFS Backup Validation,@alex please verify AWS Backup is properly configured for all critical EFS filesystems. #alert on any without daily backups and #recommend appropriate backup schedules based on data criticality.,@alex,OPERATIONAL_EFFICIENCY,AWS,STORAGE,EFS,3,3
249,🔄 EFS Cross-Region Replication Setup,@alex please identify critical EFS filesystems without cross-region replication. #recommend appropriate DR strategy and #plan implementation steps.,@alex,OPERATIONAL_EFFICIENCY,AWS,STORAGE,EFS,3,3
250,🔄 Patch Compliance Audit,@alex please verify patch compliance across EC2 fleet using SSM Patch Manager. #alert on systems with critical patches missing >7 days. Generate compliance report for security team.,@alex,SECURITY,AWS,COMPUTE,Systems Manager,3,3
251,🔐 SecureString Parameter Review,@olivier please audit SecureString parameters in Parameter Store. #alert on parameters using default KMS key instead of customer-managed keys.,@olivier,SECURITY,AWS,MANAGEMENT,Systems Manager,3,3
252,🛡️ Session Manager Security Review,"@olivier please verify Session Manager settings enforce encryption, logging, and secure access methods. #alert on non-compliant configurations.",@olivier,SECURITY,AWS,MANAGEMENT,Systems Manager,3,3
253,🔑 KMS Key Rotation Audit,"@olivier please verify all KMS keys have automatic rotation enabled. #alert on keys with rotation disabled, and #recommend implementing AWS-recommended 90-day rotation policy.",@olivier,SECURITY,AWS,SECURITY,KMS,3,3
254,🛡️ KMS Access Policy Review,@olivier please analyze all KMS key policies for overly permissive access. #recommend policy improvements that follow least privilege principles and #alert on any policies allowing anonymous access.,@olivier,SECURITY,AWS,SECURITY,KMS,3,3
255,⚙️ KMS API Call Monitoring,@olivier please set up enhanced monitoring for suspicious KMS API calls. #recommend CloudTrail alarm patterns to detect potential security incidents involving cryptographic operations.,@olivier,SECURITY,AWS,SECURITY,KMS,3,3
256,🔄 Key Material Import Review,@olivier please analyze imported key material expiration settings. #alert on keys with material expiring within 30 days and #recommend renewal strategy.,@olivier,SECURITY,AWS,SECURITY,KMS,3,3
257,🔍 KMS CloudTrail Audit,@olivier please analyze CloudTrail logs for suspicious KMS key usage patterns. #alert on anomalous decrypt operations or policy modifications outside change windows.,@olivier,SECURITY,AWS,SECURITY,KMS,3,3
258,⚠️ KMS-Related Compliance Checks,"@olivier please validate that KMS configuration meets compliance requirements (PCI-DSS, HIPAA, etc.). #alert on compliance gaps and #recommend remediation steps.",@olivier,SECURITY,AWS,SECURITY,KMS,3,3
259,🔍 KMS Key Deletion Prevention,@olivier please implement safeguards against accidental KMS key deletion. #recommend multi-factor authentication deletion strategy and longer waiting periods for key deletion.,@olivier,SECURITY,AWS,SECURITY,KMS,3,3
260,🔐 External Key Store Connection,@olivier please validate AWS External Key Store (XKS) connectivity and performance. #alert on latency issues and #recommend optimization for external key manager integration.,@olivier,SECURITY,AWS,SECURITY,KMS,3,3
261,🔑 KMS Key Backup Strategy,"@olivier please review backup procedures for KMS key material, especially for imported keys. #recommend secure backup strategy that maintains key security while preventing loss.",@olivier,SECURITY,AWS,SECURITY,KMS,3,3
262,🔄 SSL Certificate Expiry Monitor,@olivier please scan for SSL certificates expiring in the next 30 days. #alert team members with certificate details and expiration dates to prevent service disruptions.,@olivier,SECURITY,AWS,SECURITY,Certificate Manager,3,3
265,🔍 CloudTrail Baseline Verification,@olivier please verify CloudTrail is enabled across all regions with multi-region trails. #alert on any regions without active trails. Ensure logs are delivered to centralized S3 bucket with encryption.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
266,🛡️ CloudTrail Log Integrity Validation,@olivier please validate CloudTrail log file integrity has not been compromised. #alert on any modification or deletion of log files. Run validation check against digest files in the past 7 days.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
267,📊 CloudTrail S3 Bucket Permission Audit,@olivier please analyze all CloudTrail S3 bucket permissions for public access or overly permissive policies. #recommend remediation actions and #alert on any critical findings.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
268,🔐 CloudTrail KMS Encryption Verification,@olivier please verify that all CloudTrail trails are encrypted with KMS. #alert on any trails using default encryption. Check KMS key permissions for proper access controls.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
269,📈 Unusual API Activity Detection,"@olivier please analyze CloudTrail logs for unusual API patterns in the last 24 hours. #alert on suspicious activities like multiple failed logins, unusual regions, or sensitive API calls.",@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
270,🛑 Admin Privilege Usage Audit,@olivier please review CloudTrail logs for administrative privilege usage in the last 7 days. #alert on any unexpected root account activity or suspicious admin actions.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
271,🛠️ Cross-Account CloudTrail Analysis,@olivier please implement CloudTrail log analysis across multiple accounts in the organization. #plan integration with centralized SIEM solution for comprehensive visibility.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
272,🔍 IAM Policy Change Detection,@olivier please create CloudWatch alarm for IAM policy changes detected in CloudTrail. #alert on any modifications to critical role permissions or trust policies.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
273,🛡️ Network Configuration Change Detection,"@olivier please implement monitoring of VPC, subnet, and security group changes via CloudTrail. #alert on any unauthorized or unexpected network configuration modifications.",@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
274,🔐 Multi-Factor Authentication Enforcement,@olivier please analyze CloudTrail logs for console logins without MFA in the past 30 days. #alert on users consistently bypassing MFA requirements.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
275,📝 CloudTrail Organization Trail Audit,@olivier please verify organization trail settings capture activity across all member accounts. #alert on any accounts that have disabled organization trail inheritance.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
276,🔄 CloudTrail Log File Validation Check,@olivier please verify log file validation is enabled on all trails. #alert on trails with validation disabled and #recommend enabling this critical security feature.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
277,🛡️ Security Group Modification Monitoring,@olivier please create alerts for security group modifications detected in CloudTrail. #alert on any changes to production security groups outside change windows.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
278,🔐 Console Password Policy Changes,@olivier please monitor CloudTrail for modifications to account password policies. #alert on any weakening of password complexity requirements or MFA settings.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
279,📊 CloudTrail-to-SIEM Integration,@olivier please verify CloudTrail logs are properly flowing to the SIEM solution. #alert on any log delivery failures or parsing errors affecting security visibility.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
280,🔍 Public Resource Exposure Tracking,"@olivier please analyze CloudTrail for actions that made resources public in the past 30 days. #alert on any S3 buckets, RDS instances, or other resources exposed publicly.",@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
281,🚨 Permission Escalation Detection,@olivier please analyze CloudTrail for signs of permission escalation or privilege abuse. #alert on suspicious patterns of policy attachments or role assumptions.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
282,🔏 Account Lockdown Detection,@olivier please monitor CloudTrail for mass policy changes that could indicate an account takeover attempt. #alert on rapid changes affecting multiple IAM principals.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
283,🔄 Trail Configuration Drift Detection,"@olivier please implement monitoring for CloudTrail configuration changes. #alert if trails are disabled, reconfigured, or logging parameters are modified.",@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
284,🔍 Root Account Usage Detection,@olivier please monitor CloudTrail for any root account activity. #alert immediately on any root account usage with context of actions performed.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
285,🚫 Failed Login Attempt Analysis,@olivier please analyze CloudTrail for patterns of failed authentication attempts. #alert on potential brute force attacks or credential stuffing attempts.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
286,🛡️ Data Exfiltration Detection,@olivier please create CloudTrail monitoring for potential data exfiltration activities. #alert on unusual data transfer patterns or sensitive S3 bucket access.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
287,🛡️ Security Group Rule Monitoring,@olivier please create alerting for security group rule changes via CloudTrail. #alert on any rules allowing broad internet access to sensitive services.,@olivier,SECURITY,AWS,SECURITY,CloudTrail,3,3
288,💰 Monthly CUR Spend Analysis,@anna please analyze this month's Cost & Usage Report to identify top 10 spending categories. #chart spending trends comparing to previous month and #alert on any services with >20% cost increase.,@anna,COST_OPTIMIZE,AWS,ALL,Cost & Usage Report,3,3
289,📊 Idle Resource Detection,@alex please review CUR data to identify EC2 instances with <10% CPU utilization but high hourly costs. #recommend list of instances for downsizing or termination based on usage patterns.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
290,🔄 Reserved Instance Utilization,@anna please review CUR to calculate RI/Savings Plan utilization rates and coverage. #recommend opportunities for additional RI/SP purchases based on steady-state instances.,@anna,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
291,💲 Savings Plan Recommendation,@anna please analyze CUR compute usage patterns and #recommend optimal Savings Plan commitment levels based on consistent usage. #chart potential savings compared to on-demand.,@anna,COST_OPTIMIZE,AWS,COMPUTE,All,3,3
292,💰 EC2 Instance Purchasing Option Analysis,"@anna please analyze CUR to compare costs across On-Demand, Spot, Reserved, and Savings Plans. #chart optimal purchasing mix based on workload stability.",@anna,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
293,🎯 Annual Cost Optimization Planning,@anna please analyze year-to-date CUR data to identify top cost saving opportunities for next fiscal year. #plan comprehensive optimization roadmap with #chart showing projected savings.,@anna,COST_OPTIMIZE,AWS,ALL,Cost & Usage Report,3,3
294,📊 Savings Plans Utilization Review,@alex please analyze current Savings Plans utilization rates. #chart the monthly trend and #recommend actions for plans with <80% utilization.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
295,💵 Compute Savings Plan Opportunity Assessment,@alex please identify accounts with predictable compute usage not covered by Savings Plans. #recommend appropriate 1 or 3-year Compute Savings Plan commitments based on last 30 days usage.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
296,💹 EC2 Instance Savings Plans ROI Analysis,@alex please calculate ROI for converting On-Demand EC2 instances to EC2 Instance Savings Plans. #chart payback periods for 1 vs 3-year terms and #recommend optimal commitment.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
297,📅 Expiring Savings Plans Alert,@alex please identify Savings Plans expiring in the next 60 days. #alert the account owners and #recommend renewal strategies to prevent cost increases.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
298,📈 Savings Plans Coverage Gap Analysis,@anna please coordinate a review of workloads with no Savings Plans coverage. #plan implementation of appropriate plans for consistent workloads to achieve minimum 30% cost reduction.,@anna,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
299,💰 Savings Plans Purchase Recommendation,@alex please analyze the last 90 days of compute usage and #recommend optimal mix of Compute and EC2 Instance Savings Plans to maximize savings.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
300,🚨 Underutilized Savings Plans Identification,@alex please identify Savings Plans with <70% utilization and #recommend workload adjustments to improve utilization rates.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
301,📈 Savings Plans Commitment Increase Analysis,@alex please analyze if additional Savings Plans commitments would be beneficial based on current on-demand spend. #recommend optimal incremental commitment amount.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
302,📊 Annual Savings Plans Strategy Review,"@anna please #plan comprehensive annual Savings Plans strategy review analyzing all commitments, utilization rates, and financial benefits. Schedule with executive stakeholders.",@anna,COST_OPTIMIZE,AWS,COMPUTE,Savings Plans,3,3
303,📊 RI Coverage Gap Analysis,@alex please analyze our current RI coverage vs. actual usage across all accounts. #chart showing coverage by service type and #recommend opportunities to increase RI coverage for consistently running resources.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2/RDS,3,3
304,💰 RI Utilization Report,@alex please generate a report of all RIs with <80% utilization over the past 30 days. #alert for any RIs under 50% utilization and #recommend modifications for better alignment with actual usage.,@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
305,📊 RI Purchase Recommendation Engine,"@alex please analyze the last 60 days of on-demand usage and #recommend optimal new RI purchases based on consistent usage patterns, with estimated ROI timeframes.",@alex,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
306,📝 RI vs. Savings Plans Comparison,@anna please analyze whether our current RIs would be more cost-effective as Savings Plans. #chart comparing costs and #recommend optimal mix of commitment types.,@anna,COST_OPTIMIZE,AWS,COMPUTE,EC2,3,3
310,📋 Service Quota Utilization Report,@alex please generate a weekly report showing service quotas that exceed 70% utilization across all regions. #chart utilization trends and #alert for any quotas above 85%.,@alex,OPERATIONAL_EFFICIENCY,AWS,MANAGEMENT,Service Quotas,3,3
311,🔄 Automated Quota Increase Requests,@anna please implement a workflow to automatically request quota increases when utilization exceeds 80% for critical services. #plan the approval process and escalation pathways.,@anna,SCALABILITY,AWS,MANAGEMENT,Service Quotas,3,3
312,🔒 Security Service Quota Review,"@olivier please evaluate quotas for security services (WAF, Shield, GuardDuty) to ensure sufficient capacity for protection. #recommend adjustments based on threat profiles.",@olivier,SECURITY,AWS,SECURITY,Multiple,3,3
313,🛡️ Security Hub Compliance Dashboard,@olivier please create a compliance dashboard summarizing Security Hub findings across all accounts. #chart to visualize compliance status by standard and severity.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
314,🔍 Critical Security Hub Findings Triage,@olivier please review all critical severity findings in Security Hub from the past 24 hours. #alert on any findings requiring immediate attention and #recommend remediation steps.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
315,⚙️ Security Hub Integration Check,"@olivier please verify Security Hub integrations with GuardDuty, Inspector, and Config are properly configured across all accounts. #alert on any disconnected services.",@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
316,🔒 CIS Benchmark Compliance Analysis,@olivier please analyze Security Hub findings against CIS AWS Foundations Benchmark. #recommend remediation for controls with <80% compliance rate.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
317,🚨 Failed Controls Remediation Plan,"@anna please #plan remediation roadmap for failed Security Hub controls, prioritizing those with critical/high severity across multiple accounts.",@anna,SECURITY,AWS,SECURITY,Security Hub,3,3
318,🔄 Security Hub Auto-Remediation Setup,@alex please implement auto-remediation workflows for common Security Hub findings using EventBridge and Lambda. #recommend which findings are safe for auto-remediation.,@alex,OPERATIONAL_EFFICIENCY,AWS,SECURITY,Security Hub,3,3
319,🔏 PCI DSS Compliance Check,@olivier please audit Security Hub findings against PCI DSS requirements. #chart compliance status and #alert on requirements below 90% compliance.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
320,🔍 Network Security Group Analysis,@olivier please use Security Hub findings to identify security groups with overly permissive inbound rules. #recommend rule modifications following least privilege.,@olivier,SECURITY,AWS,NETWORK,Security Hub,3,3
321,🏢 Multi-Account Security Hub Deployment,@anna please #plan deployment of Security Hub across all organizational accounts with consistent standards and configurations.,@anna,SECURITY,AWS,SECURITY,Security Hub,3,3
322,🔍 Public Resource Exposure Check,@olivier please review Security Hub findings for publicly exposed resources. #alert on any critical/high severity public exposure findings.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
323,📱 HIPAA Compliance Dashboard,@olivier please create dedicated Security Hub dashboard for HIPAA compliance controls. #chart compliance status by HIPAA requirement category.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
324,🔍 Database Security Findings Analysis,"@tony please review Security Hub findings specific to database services and #recommend remediation actions for encryption, access, and configuration issues.",@tony,SECURITY,AWS,DATABASE,Security Hub,3,3
325,🛡️ Container Security Findings Review,@kai please analyze Security Hub findings related to container security and #recommend remediation actions for ECR and EKS vulnerabilities.,@kai,SECURITY,AWS,COMPUTE,Security Hub,3,3
326,🔒 Least Privilege IAM Finding Analysis,@olivier please analyze Security Hub findings related to IAM permissions and #recommend least privilege adjustments for roles with findings.,@olivier,SECURITY,AWS,SECURITY,Security Hub,3,3
327,🔍 S3 Security Finding Analysis,@alex please analyze Security Hub findings related to S3 bucket configurations. #recommend remediation for public access and encryption issues.,@alex,SECURITY,AWS,STORAGE,Security Hub,3,3
328,🔄 Cloud Security Posture Management Strategy,@anna please #plan comprehensive CSPM strategy leveraging Security Hub as the primary tool. Define maturity model and implementation roadmap.,@anna,SECURITY,AWS,SECURITY,Security Hub,3,3
329,🛡️ EKS Cluster Security Assessment,@kai please analyze Security Hub findings specific to EKS clusters and #recommend Kubernetes security improvements based on findings.,@kai,SECURITY,AWS,COMPUTE,Security Hub,3,3
330,🚫 Dead Letter Queue Analysis,@alex please analyze DLQ trends over past 7 days. #chart message failure patterns and #recommend remediation actions for recurring errors.,@alex,OPERATIONAL_EFFICIENCY,AWS,,SQS/SNS,3,3
331,📈 MQ Performance Optimization,@alex please review throughput metrics for all message queues and identify bottlenecks. #recommend configuration changes to improve latency and throughput.,@alex,OPERATIONAL_EFFICIENCY,AWS,,SQS,3,3
332,🔍 Queue Scalability Assessment,@alex please evaluate auto-scaling configurations for high-traffic queues. #recommend throughput limits and scaling parameters based on historical patterns.,@alex,SCALABILITY,AWS,,SQS,3,3
333,🔐 MQ Security Audit,@olivier please audit message queue access policies and encryption settings. #alert on queues without server-side encryption or with overly permissive policies.,@olivier,SECURITY,AWS,,SQS/SNS,3,3
336,📊 Beanstalk Environment Audit,@alex please audit all Elastic Beanstalk environments to identify non-production environments consuming production-tier resources. #recommend resource optimization for dev/test environments.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Elastic Beanstalk,3,3
337,🔒 Security Group Review,@olivier please analyze all Elastic Beanstalk security groups for overly permissive inbound rules (0.0.0.0/0). #recommend least privilege access configurations and #alert on critical security gaps.,@olivier,SECURITY,AWS,COMPUTE,Elastic Beanstalk,3,3
338,📈 Autoscaling Configuration Analysis,@alex please review autoscaling configurations across all Beanstalk environments. #recommend optimal min/max instance counts based on historical usage patterns to balance performance and cost.,@alex,SCALABILITY,AWS,COMPUTE,Elastic Beanstalk,3,3
339,🏗️ Platform Update Assessment,@alex please identify Elastic Beanstalk environments running deprecated platform versions. #plan upgrade path with minimal disruption and #recommend security improvements in newer versions.,@alex,OPERATIONAL_EFFICIENCY,AWS,COMPUTE,Elastic Beanstalk,3,3
340,💰 Reserved Instance Coverage,@alex please analyze EC2 instances launched by Elastic Beanstalk and #recommend Reserved Instance purchases for stable environments with predictable workloads to reduce costs by 40-60%.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Elastic Beanstalk,3,3
341,🔐 IAM Role Permission Review,@olivier please audit IAM instance profiles used by Elastic Beanstalk environments for excessive permissions. #recommend least-privilege adjustments and #alert on security risks.,@olivier,SECURITY,AWS,COMPUTE,Elastic Beanstalk,3,3
342,📟 Environment Variable Security Scan,@olivier please scan environment variables in Elastic Beanstalk for exposed secrets or credentials. #alert on any sensitive information found and #recommend secure alternative approaches.,@olivier,SECURITY,AWS,COMPUTE,Elastic Beanstalk,3,3
343,📊 Instance Type Optimization,@alex please analyze instance types used in Elastic Beanstalk environments. #recommend right-sizing based on actual workload characteristics and newer generation instances.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Elastic Beanstalk,3,3
344,🔒 Web Application Firewall Integration,@olivier please implement AWS WAF for Elastic Beanstalk environments with public endpoints. #recommend rule sets for common web vulnerabilities and #alert on attack patterns.,@olivier,SECURITY,AWS,NETWORK,Elastic Beanstalk,3,3
345,💰 Savings Plan Analysis,@alex please analyze EC2 usage patterns from Elastic Beanstalk environments. #recommend appropriate Savings Plans to reduce compute costs by 30%+ for stable workloads.,@alex,COST_OPTIMIZE,AWS,COMPUTE,Elastic Beanstalk,3,3
