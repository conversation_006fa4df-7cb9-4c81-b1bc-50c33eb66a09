Mon Feb 25 01:25:15 UTC 2013  <PERSON> <<EMAIL>>

	* *: <PERSON><PERSON> from <PERSON> to improve testing and
	  documentation.
	* *: Rebuild with cython >= 0.17 to remove bug in generated code in
	  python 3.3.
	* *: Fix bug in setup.py on windows; reported by <PERSON><PERSON>.
	* Bump version number to 1.3.0

<PERSON><PERSON> Aug 09 16:23:11 BST 2011  <PERSON> <<EMAIL>>

	* *: <PERSON> from <PERSON>, with some small tweaks, to support
	  python 3.X.  Tested with python 3.2rc3
	* Incompatibility: in python 2.X, algorithms() now returns unicode
	  strings, instead of byte strings.
	* Update to use latest libstemmer.
	* Bump version number to 1.2.0

Thu Nov 05 23:44:19 GMT 2009  <PERSON> <<EMAIL>>

	* HACKING: Add a note to avoid me being confused by an out-of-date
	  MANIFEST file again.
	* README: Update note about version of Python I'm using.
	* docs/quickstart.txt: Expect romanian and turkish algorithms to be
	  listed, too.

Thu Nov 05 22:28:52 GMT 2009  <PERSON> <<EMAIL>>

	* docs/quickstart.txt: Add hungarian stemming algorithm.
	* setup.py: Bump version number to 1.1.0.
	* src/Stemmer.pyx: Add aliases parameter to the algorithms()
	  function to control whether to return alias names for the
	  algorithms or just the canonical names.

Mon Sep 18 02:08:52 BST 2006  Richard Boulton <<EMAIL>>

        * setup.py: Read the source files from libstemmer_c/mkinc_utf8.mak,
          instead of MANIFEST.  Should remove unused code from generated
          library which implemented stemming in non-UTF-8 character sets.
        * Bump version number to 1.0.2

Mon Jun 19 07:51:38 BST 2006  Richard Boulton <<EMAIL>>

        * setup.py: fix setup.py to work with python 2.3: don't use "set",
          which doesn't exist before python 2.4.
        * Stemmer.pyx: Add module-level "version()" function, to determine
          version of Stemmer.  General tidying up, too.
        * Bump version number to 1.0.1

Sun Jun 11 22:22:00 BST 2006  Richard Boulton <<EMAIL>>

        * Release initial package of PyStemmer, version 1.0

