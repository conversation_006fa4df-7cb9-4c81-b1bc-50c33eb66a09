name: pip-install

# Controls when the workflow will run
on:
  # Triggers only when this file changes.
  push:
    paths:
      - .github/workflows/pip-install.yml
  # Manual trigger from the Actions tab.
  workflow_dispatch:

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        pipoptions: ['', '--no-binary PyStemmer']
    runs-on: ${{ matrix.os }}
    steps:
    - name: Install
      run: pip install ${{ matrix.pipoptions }} PyStemmer
