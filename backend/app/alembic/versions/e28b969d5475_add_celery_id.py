"""add_celery_id

Revision ID: e28b969d5475
Revises: eddc364cd3e1
Create Date: 2025-06-07 10:38:02.588025

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'e28b969d5475'
down_revision = 'eddc364cd3e1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task', sa.Column('celery_task_id', sa.Uuid(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('task', 'celery_task_id')
    # ### end Alembic commands ###
