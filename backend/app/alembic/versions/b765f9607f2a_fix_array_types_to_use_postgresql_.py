"""Fix ARRAY types to use PostgreSQL specific arrays

Revision ID: b765f9607f2a
Revises: a085fc2f3174
Create Date: 2025-06-08 21:26:56.445505

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'b765f9607f2a'
down_revision = 'a085fc2f3174'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
