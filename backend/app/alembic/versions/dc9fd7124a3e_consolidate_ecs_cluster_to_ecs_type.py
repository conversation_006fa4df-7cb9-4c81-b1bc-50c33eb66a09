"""consolidate_ecs_cluster_to_ecs_type

Revision ID: dc9fd7124a3e
Revises: c40620e42e2e
Create Date: 2025-06-10 14:00:20.294466

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'dc9fd7124a3e'
down_revision = 'c40620e42e2e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Update existing ECS_CLUSTER resources to use ECS type (only if table exists)
    op.execute("""
        DO $$
        BEGIN
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'resources') THEN
                UPDATE resources SET type = 'ECS' WHERE type = 'ECS_CLUSTER';
            END IF;
        END $$;
    """)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Revert ECS resources back to ECS_CLUSTER where they were clusters (only if table exists)
    # Note: This assumes cluster resources can be identified by their description
    op.execute("""
        DO $$
        BEGIN
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'resources') THEN
                UPDATE resources SET type = 'ECS_CLUSTER' WHERE type = 'ECS' AND description LIKE '%cluster%';
            END IF;
        END $$;
    """)
    # ### end Alembic commands ###
