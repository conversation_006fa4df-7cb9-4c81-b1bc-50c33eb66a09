"""remove_assigned_task

Revision ID: 80e3ae75f95a
Revises: a824fc066ab3
Create Date: 2025-06-06 19:18:01.963139

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '80e3ae75f95a'
down_revision = 'a824fc066ab3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
