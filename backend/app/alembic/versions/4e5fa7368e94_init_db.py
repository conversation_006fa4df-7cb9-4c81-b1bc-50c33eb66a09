"""init db

Revision ID: 4e5fa7368e94
Revises:
Create Date: 2025-06-04 13:48:11.027435

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4e5fa7368e94'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('GOOD', 'BAD', name='feedbacktype').create(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='asynctaskstatus').create(op.get_bind())
    sa.Enum('URL', 'FILE', name='documenttype').create(op.get_bind())
    sa.Enum('MANUAL', 'AGENT_REQUESTED', 'ALWAYS', name='kbusagemode').create(op.get_bind())
    sa.Enum('PRIVATE', 'SHARED', name='kbaccesslevel').create(op.get_bind())
    sa.Enum('UNREAD', 'READ', 'ARCHIVED', name='notificationstatus').create(op.get_bind())
    sa.Enum('INFO', 'WARNING', 'ERROR', 'INTERRUPT', name='notificationtype').create(op.get_bind())
    sa.Enum('OPEN', 'ACKNOWLEDGED', 'RESOLVED', 'CLOSED', name='alertstatus').create(op.get_bind())
    sa.Enum('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', name='alertseverity').create(op.get_bind())
    sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskimpactenum').create(op.get_bind())
    sa.Enum('AUTONOMOUS', 'AGENT', name='runmodeenum').create(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='uploadstatus').create(op.get_bind())
    sa.Enum('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus').create(op.get_bind())
    sa.Enum('MANUAL', 'AUTOMATED', name='taskmode').create(op.get_bind())
    sa.Enum('AWS', 'AZURE', 'GCP', 'ALL', name='taskcouldenum').create(op.get_bind())
    sa.Enum('ALL', 'OTHER', 'COMPUTE', 'STORAGE', 'SERVERLESS', 'DATABASE', 'NETWORK', 'MESSAGING', 'MANAGEMENT', 'BILLING', 'CROSS_SERVICE', 'MONITORING', 'STREAMING', 'SECURITY', name='taskserviceenum').create(op.get_bind())
    sa.Enum('COST_OPTIMIZE', 'OPERATIONAL', 'SCALABILITY', 'SECURITY', 'OPERATIONAL_EFFICIENCY', 'OTHER', name='taskcategoryenum').create(op.get_bind())
    sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskpriority').create(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus').create(op.get_bind())
    sa.Enum('LINE', 'BAR', 'PIE', 'DOUGHNUT', 'AREA', 'SCATTER', 'RADAR', 'STEP_AREA', name='charttype').create(op.get_bind())
    sa.Enum('TABLE', 'CHART', name='messagedisplaycomponenttype').create(op.get_bind())
    sa.Enum('NONE', 'RECOMMENDATION', name='messageactiontype').create(op.get_bind())
    sa.Enum('AWS', name='cloudprovider').create(op.get_bind())
    sa.Enum('CONVERSATION_AGENT', 'AUTONOMOUS_AGENT', name='agenttype').create(op.get_bind())
    sa.Enum('START', 'TOOL', 'HUMAN_IN_LOOP', 'END', 'OUTPUT', name='nodetype').create(op.get_bind())
    sa.Enum('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus').create(op.get_bind())
    sa.Enum('PENDING', 'IMPLEMENTED', 'IGNORED', 'IN_PROGRESS', name='recommendationstatus').create(op.get_bind())
    sa.Enum('INSTANCE_RIGHTSIZING', 'AUTOSCALING_OPTIMIZATION', 'AUTO_START_STOP_OPTIMIZATION', 'VOLUME_OPTIMIZATION', 'SNAPSHOT_CLEANUP', 'RESERVED_INSTANCE_RECOMMENDATION', 'SAVINGS_PLAN_RECOMMENDATION', 'SPOT_INSTANCE_USAGE', 'IDLE_RESOURCE_CLEANUP', 'UNUSED_EIP_CLEANUP', 'ORPHANED_SNAPSHOT_CLEANUP', 'UNDERUTILIZED_EBS_CLEANUP', 'SERVERLESS_MIGRATION', 'CONTAINER_ADOPTION', 'MULTI_AZ_OPTIMIZATION', 'DATA_TRANSFER_OPTIMIZATION', 'CLOUDFRONT_OPTIMIZATION', 'NAT_GATEWAY_OPTIMIZATION', 'RDS_OPTIMIZATION', 'REDSHIFT_OPTIMIZATION', 'DYNAMODB_OPTIMIZATION', 'S3_STORAGE_CLASS_OPTIMIZATION', 'LAMBDA_OPTIMIZATION', 'TAGGING_IMPROVEMENT', 'COST_ALLOCATION_IMPROVEMENT', 'COST_ANOMALY_DETECTION', 'BUDGET_ALERT_SETUP', 'COST_EXPLORER_USAGE', 'MODERNIZE_LEGACY_SERVICES', 'MIGRATE_TO_GRAVITON', 'COMPLIANCE_OPTIMIZATION', 'GOVERNANCE_IMPROVEMENT', 'CROSS_REGION_OPTIMIZATION', 'CROSS_ACCOUNT_OPTIMIZATION', 'PREDICTIVE_SCALING', 'AI_DRIVEN_OPTIMIZATION', 'QUANTUM_COMPUTING_READINESS', 'CARBON_FOOTPRINT_REDUCTION', 'RENEWABLE_ENERGY_USAGE', 'MARKETPLACE_ALTERNATIVE', 'THIRD_PARTY_TOOL_RECOMMENDATION', 'CUSTOM_OPTIMIZATION', 'OTHER', 'EC2_FLEET_OPTIMIZATION', 'SPOT_FLEET_OPTIMIZATION', 'GRAVITON_MIGRATION', 'PREDICTIVE_SCALING_OPTIMIZATION', 'INSTANCE_CONNECT_ENDPOINT', name='recommendationtype').create(op.get_bind())
    sa.Enum('USAGE', 'PERFORMANCE', 'COST', name='metrictype').create(op.get_bind())
    sa.Enum('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus').create(op.get_bind())
    sa.Enum('PRODUCTION', 'STAGING', 'DEVELOPMENT', name='accountenvironement').create(op.get_bind())
    sa.Enum('ACTIVE', 'INACTIVE', 'EXPIRED', name='credentialstatus').create(op.get_bind())
    op.create_table('builtinconnector',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('display_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('default_required_permission', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_builtinconnector_name'), 'builtinconnector', ['name'], unique=True)
    op.create_table('item',
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('modulesetting',
    sa.Column('key', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('value', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('key')
    )
    op.create_index(op.f('ix_modulesetting_key'), 'modulesetting', ['key'], unique=False)
    op.create_table('setting',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('provider_name', postgresql.ENUM('AWS', name='cloudprovider', create_type=False), nullable=False),
    sa.Column('regions', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('types', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('cron_patterns', sa.ARRAY(sa.String()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user',
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_email_verified', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('last_login_time', sa.DateTime(), nullable=True),
    sa.Column('full_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('avatar_url', sqlmodel.sql.sqltypes.AutoString(length=1024), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('hashed_password', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_table('notification',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('type', postgresql.ENUM('INFO', 'WARNING', 'ERROR', 'INTERRUPT', name='notificationtype', create_type=False), nullable=False),
    sa.Column('status', postgresql.ENUM('UNREAD', 'READ', 'ARCHIVED', name='notificationstatus', create_type=False), nullable=False),
    sa.Column('notification_metadata', sa.JSON(), nullable=True),
    sa.Column('requires_action', sa.Boolean(), nullable=False),
    sa.Column('action_url', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('message_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notification_user_id'), 'notification', ['user_id'], unique=False)
    op.create_table('usage_quota',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('quota_used_messages', sa.Integer(), nullable=False),
    sa.Column('quota_used_tokens', sa.Integer(), nullable=False),
    sa.Column('reset_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_usage_quota_user_id'), 'usage_quota', ['user_id'], unique=False)
    op.create_table('useractivationtoken',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('token', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_useractivationtoken_token'), 'useractivationtoken', ['token'], unique=True)
    op.create_table('workspace',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('owner_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('agent',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('type', postgresql.ENUM('CONVERSATION_AGENT', 'AUTONOMOUS_AGENT', name='agenttype', create_type=False), nullable=False),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_agent_id'), 'agent', ['id'], unique=False)
    op.create_index(op.f('ix_agent_is_active'), 'agent', ['is_active'], unique=False)
    op.create_index(op.f('ix_agent_is_deleted'), 'agent', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_agent_title'), 'agent', ['title'], unique=False)
    op.create_index(op.f('ix_agent_type'), 'agent', ['type'], unique=False)
    op.create_index(op.f('ix_agent_workspace_id'), 'agent', ['workspace_id'], unique=False)
    op.create_table('alert',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=True),
    sa.Column('severity', postgresql.ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', name='alertseverity', create_type=False), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', postgresql.ENUM('OPEN', 'ACKNOWLEDGED', 'RESOLVED', 'CLOSED', name='alertstatus', create_type=False), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_alert_workspace_id'), 'alert', ['workspace_id'], unique=False)
    op.create_table('awsaccount',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('environment', postgresql.ENUM('PRODUCTION', 'STAGING', 'DEVELOPMENT', name='accountenvironement', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(length=12), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('knowledge_bases',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('access_level', postgresql.ENUM('PRIVATE', 'SHARED', name='kbaccesslevel', create_type=False), nullable=False),
    sa.Column('usage_mode', postgresql.ENUM('MANUAL', 'AGENT_REQUESTED', 'ALWAYS', name='kbusagemode', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=True),
    sa.Column('owner_id', sa.Uuid(), nullable=True),
    sa.Column('allowed_users', sa.ARRAY(sa.UUID()), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('tags', sa.ARRAY(sa.String()), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_bases_is_deleted'), 'knowledge_bases', ['is_deleted'], unique=False)
    op.create_table('mcpconfig',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('workspace_id')
    )
    op.create_table('resource',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('arn', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=False),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('configurations', sa.JSON(), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('region', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('status', postgresql.ENUM('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('task',
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=False),
    sa.Column('updated_by', sa.Uuid(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('version', sa.Integer(), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=True),
    sa.Column('priority', postgresql.ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskpriority', create_type=False), nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus', create_type=False), nullable=False),
    sa.Column('enable', sa.Boolean(), nullable=False),
    sa.Column('category', postgresql.ENUM('COST_OPTIMIZE', 'OPERATIONAL', 'SCALABILITY', 'SECURITY', 'OPERATIONAL_EFFICIENCY', 'OTHER', name='taskcategoryenum', create_type=False), nullable=True),
    sa.Column('service', postgresql.ENUM('ALL', 'OTHER', 'COMPUTE', 'STORAGE', 'SERVERLESS', 'DATABASE', 'NETWORK', 'MESSAGING', 'MANAGEMENT', 'BILLING', 'CROSS_SERVICE', 'MONITORING', 'STREAMING', 'SECURITY', name='taskserviceenum', create_type=False), nullable=True),
    sa.Column('service_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('cloud', postgresql.ENUM('AWS', 'AZURE', 'GCP', 'ALL', name='taskcouldenum', create_type=False), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('mode', postgresql.ENUM('MANUAL', 'AUTOMATED', name='taskmode', create_type=False), nullable=False),
    sa.Column('deadline', sa.DateTime(), nullable=True),
    sa.Column('completion_notes', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=True),
    sa.Column('execution_status', postgresql.ENUM('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus', create_type=False), nullable=False),
    sa.Column('last_run', sa.DateTime(), nullable=True),
    sa.Column('next_run', sa.DateTime(), nullable=True),
    sa.Column('scheduled_at', sa.DateTime(), nullable=True),
    sa.Column('schedule', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('last_error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('timeout_seconds', sa.Integer(), nullable=True),
    sa.Column('agent_config', sa.JSON(), nullable=True),
    sa.Column('owner_id', sa.Uuid(), nullable=False),
    sa.Column('assigned_to', sa.Uuid(), nullable=True),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['assigned_to'], ['user.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['user.id'], ),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_assigned_to'), 'task', ['assigned_to'], unique=False)
    op.create_index(op.f('ix_task_category'), 'task', ['category'], unique=False)
    op.create_index(op.f('ix_task_cloud'), 'task', ['cloud'], unique=False)
    op.create_index(op.f('ix_task_created_at'), 'task', ['created_at'], unique=False)
    op.create_index(op.f('ix_task_deadline'), 'task', ['deadline'], unique=False)
    op.create_index(op.f('ix_task_enable'), 'task', ['enable'], unique=False)
    op.create_index(op.f('ix_task_execution_status'), 'task', ['execution_status'], unique=False)
    op.create_index(op.f('ix_task_id'), 'task', ['id'], unique=False)
    op.create_index(op.f('ix_task_is_deleted'), 'task', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_task_last_run'), 'task', ['last_run'], unique=False)
    op.create_index(op.f('ix_task_mode'), 'task', ['mode'], unique=False)
    op.create_index(op.f('ix_task_next_run'), 'task', ['next_run'], unique=False)
    op.create_index(op.f('ix_task_owner_id'), 'task', ['owner_id'], unique=False)
    op.create_index(op.f('ix_task_priority'), 'task', ['priority'], unique=False)
    op.create_index(op.f('ix_task_scheduled_at'), 'task', ['scheduled_at'], unique=False)
    op.create_index(op.f('ix_task_service'), 'task', ['service'], unique=False)
    op.create_index(op.f('ix_task_service_name'), 'task', ['service_name'], unique=False)
    op.create_index(op.f('ix_task_status'), 'task', ['status'], unique=False)
    op.create_index(op.f('ix_task_title'), 'task', ['title'], unique=False)
    op.create_index(op.f('ix_task_updated_at'), 'task', ['updated_at'], unique=False)
    op.create_index(op.f('ix_task_workspace_id'), 'task', ['workspace_id'], unique=False)
    op.create_table('tasktemplate',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=True),
    sa.Column('task', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('category', postgresql.ENUM('COST_OPTIMIZE', 'OPERATIONAL', 'SCALABILITY', 'SECURITY', 'OPERATIONAL_EFFICIENCY', 'OTHER', name='taskcategoryenum', create_type=False), nullable=False),
    sa.Column('service', postgresql.ENUM('ALL', 'OTHER', 'COMPUTE', 'STORAGE', 'SERVERLESS', 'DATABASE', 'NETWORK', 'MESSAGING', 'MANAGEMENT', 'BILLING', 'CROSS_SERVICE', 'MONITORING', 'STREAMING', 'SECURITY', name='taskserviceenum', create_type=False), nullable=False),
    sa.Column('service_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('cloud', postgresql.ENUM('AWS', 'AZURE', 'GCP', 'ALL', name='taskcouldenum', create_type=False), nullable=False),
    sa.Column('run_mode', postgresql.ENUM('AUTONOMOUS', 'AGENT', name='runmodeenum', create_type=False), nullable=True),
    sa.Column('schedule', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('context', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('priority', postgresql.ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskpriority', create_type=False), nullable=False),
    sa.Column('impact', postgresql.ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskimpactenum', create_type=False), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tasktemplate_category'), 'tasktemplate', ['category'], unique=False)
    op.create_index(op.f('ix_tasktemplate_cloud'), 'tasktemplate', ['cloud'], unique=False)
    op.create_index(op.f('ix_tasktemplate_service'), 'tasktemplate', ['service'], unique=False)
    op.create_index(op.f('ix_tasktemplate_service_name'), 'tasktemplate', ['service_name'], unique=False)
    op.create_index(op.f('ix_tasktemplate_task'), 'tasktemplate', ['task'], unique=False)
    op.create_index(op.f('ix_tasktemplate_workspace_id'), 'tasktemplate', ['workspace_id'], unique=False)
    op.create_table('upload',
    sa.Column('filename', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('file_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('s3_key', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='uploadstatus', create_type=False), nullable=False),
    sa.Column('upload_url', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('owner_id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_upload_id'), 'upload', ['id'], unique=False)
    op.create_table('userworkspace',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'workspace_id', name='uq_user_workspace')
    )
    op.create_table('workflow',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('status', postgresql.ENUM('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus', create_type=False), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('workspacebuiltinconnector',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('connector_id', sa.Uuid(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('required_permission', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['connector_id'], ['builtinconnector.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('workspacesetting',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('regions', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('types', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('cron_pattern', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('workspace_id')
    )
    op.create_table('agent_contexts',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('context', sqlmodel.sql.sqltypes.AutoString(length=5000), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('agentconnector',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('mcp_servers', sa.ARRAY(sa.String()), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('awsaccountcredentials',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('aws_account_id', sa.Uuid(), nullable=False),
    sa.Column('access_key_id', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
    sa.Column('secret_access_key', sqlmodel.sql.sqltypes.AutoString(length=256), nullable=False),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', 'EXPIRED', name='credentialstatus', create_type=False), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.Column('last_rotated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['aws_account_id'], ['awsaccount.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('aws_account_id', name='uq_aws_account_credential')
    )
    op.create_table('conversation',
    sa.Column('model_provider', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('resource_id', sa.Uuid(), nullable=True),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('share_id', sa.Uuid(), nullable=True),
    sa.Column('is_shared', sa.Boolean(), nullable=False),
    sa.Column('shared_at', sa.DateTime(), nullable=True),
    sa.Column('shared_by', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], ),
    sa.ForeignKeyConstraint(['shared_by'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('share_id', name='uq_conversation_share_id')
    )
    op.create_index(op.f('ix_conversation_share_id'), 'conversation', ['share_id'], unique=True)
    op.create_table('document_kbs',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('type', postgresql.ENUM('URL', 'FILE', name='documenttype', create_type=False), nullable=False),
    sa.Column('url', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('deep_crawl', sa.Boolean(), nullable=False),
    sa.Column('file_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('file_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('object_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('embed_status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='asynctaskstatus', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('parent_id', sa.Uuid(), nullable=True),
    sa.Column('kb_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['kb_id'], ['knowledge_bases.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['parent_id'], ['document_kbs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_kbs_is_deleted'), 'document_kbs', ['is_deleted'], unique=False)
    op.create_table('metric',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('value', sa.Float(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('type', postgresql.ENUM('USAGE', 'PERFORMANCE', 'COST', name='metrictype', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('resource_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('recommendation',
    sa.Column('type', postgresql.ENUM('INSTANCE_RIGHTSIZING', 'AUTOSCALING_OPTIMIZATION', 'AUTO_START_STOP_OPTIMIZATION', 'VOLUME_OPTIMIZATION', 'SNAPSHOT_CLEANUP', 'RESERVED_INSTANCE_RECOMMENDATION', 'SAVINGS_PLAN_RECOMMENDATION', 'SPOT_INSTANCE_USAGE', 'IDLE_RESOURCE_CLEANUP', 'UNUSED_EIP_CLEANUP', 'ORPHANED_SNAPSHOT_CLEANUP', 'UNDERUTILIZED_EBS_CLEANUP', 'SERVERLESS_MIGRATION', 'CONTAINER_ADOPTION', 'MULTI_AZ_OPTIMIZATION', 'DATA_TRANSFER_OPTIMIZATION', 'CLOUDFRONT_OPTIMIZATION', 'NAT_GATEWAY_OPTIMIZATION', 'RDS_OPTIMIZATION', 'REDSHIFT_OPTIMIZATION', 'DYNAMODB_OPTIMIZATION', 'S3_STORAGE_CLASS_OPTIMIZATION', 'LAMBDA_OPTIMIZATION', 'TAGGING_IMPROVEMENT', 'COST_ALLOCATION_IMPROVEMENT', 'COST_ANOMALY_DETECTION', 'BUDGET_ALERT_SETUP', 'COST_EXPLORER_USAGE', 'MODERNIZE_LEGACY_SERVICES', 'MIGRATE_TO_GRAVITON', 'COMPLIANCE_OPTIMIZATION', 'GOVERNANCE_IMPROVEMENT', 'CROSS_REGION_OPTIMIZATION', 'CROSS_ACCOUNT_OPTIMIZATION', 'PREDICTIVE_SCALING', 'AI_DRIVEN_OPTIMIZATION', 'QUANTUM_COMPUTING_READINESS', 'CARBON_FOOTPRINT_REDUCTION', 'RENEWABLE_ENERGY_USAGE', 'MARKETPLACE_ALTERNATIVE', 'THIRD_PARTY_TOOL_RECOMMENDATION', 'CUSTOM_OPTIMIZATION', 'OTHER', 'EC2_FLEET_OPTIMIZATION', 'SPOT_FLEET_OPTIMIZATION', 'GRAVITON_MIGRATION', 'PREDICTIVE_SCALING_OPTIMIZATION', 'INSTANCE_CONNECT_ENDPOINT', name='recommendationtype', create_type=False), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('potential_savings', sa.Float(), nullable=False),
    sa.Column('technical_guidances', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('document_url', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=True),
    sa.Column('effort', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('risk', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'IMPLEMENTED', 'IGNORED', 'IN_PROGRESS', name='recommendationstatus', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('resource_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('taskhistory',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('task_id', sa.Uuid(), nullable=False),
    sa.Column('changed_by', sa.Uuid(), nullable=True),
    sa.Column('changed_at', sa.DateTime(), nullable=False),
    sa.Column('previous_state', sa.JSON(), nullable=True),
    sa.Column('new_state', sa.JSON(), nullable=True),
    sa.Column('change_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.ForeignKeyConstraint(['changed_by'], ['user.id'], ),
    sa.ForeignKeyConstraint(['task_id'], ['task.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_taskhistory_changed_at'), 'taskhistory', ['changed_at'], unique=False)
    op.create_index(op.f('ix_taskhistory_task_id'), 'taskhistory', ['task_id'], unique=False)
    op.create_table('workflownode',
    sa.Column('type', postgresql.ENUM('START', 'TOOL', 'HUMAN_IN_LOOP', 'END', 'OUTPUT', name='nodetype', create_type=False), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workflow_id', sa.Uuid(), nullable=False),
    sa.Column('parent_id', sa.Uuid(), nullable=True),
    sa.Column('status', postgresql.ENUM('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus', create_type=False), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['workflownode.id'], ),
    sa.ForeignKeyConstraint(['workflow_id'], ['workflow.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('agentconnectorbuiltinconnector',
    sa.Column('agent_connector_id', sa.Uuid(), nullable=False),
    sa.Column('workspace_builtin_connector_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['agent_connector_id'], ['agentconnector.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_builtin_connector_id'], ['workspacebuiltinconnector.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_connector_id', 'workspace_builtin_connector_id')
    )
    op.create_table('message',
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('role', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('is_interrupt', sa.Boolean(), nullable=False),
    sa.Column('interrupt_message', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('action_type', postgresql.ENUM('NONE', 'RECOMMENDATION', name='messageactiontype', create_type=False), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('conversation_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('message_metadata', sa.JSON(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversation.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('taskconversation',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('task_id', sa.Uuid(), nullable=False),
    sa.Column('conversation_id', sa.Uuid(), nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus', create_type=False), nullable=False),
    sa.Column('celery_task_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversation.id'], ),
    sa.ForeignKeyConstraint(['task_id'], ['task.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_taskconversation_conversation_id'), 'taskconversation', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_taskconversation_id'), 'taskconversation', ['id'], unique=False)
    op.create_index(op.f('ix_taskconversation_status'), 'taskconversation', ['status'], unique=False)
    op.create_index(op.f('ix_taskconversation_task_id'), 'taskconversation', ['task_id'], unique=False)
    op.create_table('message_feedback',
    sa.Column('feedback_type', postgresql.ENUM('GOOD', 'BAD', name='feedbacktype', create_type=False), nullable=False),
    sa.Column('reason', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('additional_comments', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('message_id', name='uq_message_feedback')
    )
    op.create_index(op.f('ix_message_feedback_message_id'), 'message_feedback', ['message_id'], unique=False)
    op.create_index(op.f('ix_message_feedback_user_id'), 'message_feedback', ['user_id'], unique=False)
    op.create_table('messageagentthought',
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('tool', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('tool_input', sa.JSON(), nullable=True),
    sa.Column('observation', sa.Text(), nullable=True),
    sa.Column('answer', sa.Text(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('messagecheckpoint',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('start_checkpoint_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('end_checkpoint_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('messagedisplaycomponent',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('type', postgresql.ENUM('TABLE', 'CHART', name='messagedisplaycomponenttype', create_type=False), nullable=False),
    sa.Column('chart_type', postgresql.ENUM('LINE', 'BAR', 'PIE', 'DOUGHNUT', 'AREA', 'SCATTER', 'RADAR', 'STEP_AREA', name='charttype', create_type=False), nullable=True),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('token_usage',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('input_tokens', sa.Integer(), nullable=False),
    sa.Column('output_tokens', sa.Integer(), nullable=False),
    sa.Column('model_provider', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('model_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_token_usage_message_id'), 'token_usage', ['message_id'], unique=False)
    op.create_index(op.f('ix_token_usage_workspace_id'), 'token_usage', ['workspace_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_token_usage_workspace_id'), table_name='token_usage')
    op.drop_index(op.f('ix_token_usage_message_id'), table_name='token_usage')
    op.drop_table('token_usage')
    op.drop_table('messagedisplaycomponent')
    op.drop_table('messagecheckpoint')
    op.drop_table('messageagentthought')
    op.drop_index(op.f('ix_message_feedback_user_id'), table_name='message_feedback')
    op.drop_index(op.f('ix_message_feedback_message_id'), table_name='message_feedback')
    op.drop_table('message_feedback')
    op.drop_index(op.f('ix_taskconversation_task_id'), table_name='taskconversation')
    op.drop_index(op.f('ix_taskconversation_status'), table_name='taskconversation')
    op.drop_index(op.f('ix_taskconversation_id'), table_name='taskconversation')
    op.drop_index(op.f('ix_taskconversation_conversation_id'), table_name='taskconversation')
    op.drop_table('taskconversation')
    op.drop_table('message')
    op.drop_table('agentconnectorbuiltinconnector')
    op.drop_table('workflownode')
    op.drop_index(op.f('ix_taskhistory_task_id'), table_name='taskhistory')
    op.drop_index(op.f('ix_taskhistory_changed_at'), table_name='taskhistory')
    op.drop_table('taskhistory')
    op.drop_table('recommendation')
    op.drop_table('metric')
    op.drop_index(op.f('ix_document_kbs_is_deleted'), table_name='document_kbs')
    op.drop_table('document_kbs')
    op.drop_index(op.f('ix_conversation_share_id'), table_name='conversation')
    op.drop_table('conversation')
    op.drop_table('awsaccountcredentials')
    op.drop_table('agentconnector')
    op.drop_table('agent_contexts')
    op.drop_table('workspacesetting')
    op.drop_table('workspacebuiltinconnector')
    op.drop_table('workflow')
    op.drop_table('userworkspace')
    op.drop_index(op.f('ix_upload_id'), table_name='upload')
    op.drop_table('upload')
    op.drop_index(op.f('ix_tasktemplate_workspace_id'), table_name='tasktemplate')
    op.drop_index(op.f('ix_tasktemplate_task'), table_name='tasktemplate')
    op.drop_index(op.f('ix_tasktemplate_service_name'), table_name='tasktemplate')
    op.drop_index(op.f('ix_tasktemplate_service'), table_name='tasktemplate')
    op.drop_index(op.f('ix_tasktemplate_cloud'), table_name='tasktemplate')
    op.drop_index(op.f('ix_tasktemplate_category'), table_name='tasktemplate')
    op.drop_table('tasktemplate')
    op.drop_index(op.f('ix_task_workspace_id'), table_name='task')
    op.drop_index(op.f('ix_task_updated_at'), table_name='task')
    op.drop_index(op.f('ix_task_title'), table_name='task')
    op.drop_index(op.f('ix_task_status'), table_name='task')
    op.drop_index(op.f('ix_task_service_name'), table_name='task')
    op.drop_index(op.f('ix_task_service'), table_name='task')
    op.drop_index(op.f('ix_task_scheduled_at'), table_name='task')
    op.drop_index(op.f('ix_task_priority'), table_name='task')
    op.drop_index(op.f('ix_task_owner_id'), table_name='task')
    op.drop_index(op.f('ix_task_next_run'), table_name='task')
    op.drop_index(op.f('ix_task_mode'), table_name='task')
    op.drop_index(op.f('ix_task_last_run'), table_name='task')
    op.drop_index(op.f('ix_task_is_deleted'), table_name='task')
    op.drop_index(op.f('ix_task_id'), table_name='task')
    op.drop_index(op.f('ix_task_execution_status'), table_name='task')
    op.drop_index(op.f('ix_task_enable'), table_name='task')
    op.drop_index(op.f('ix_task_deadline'), table_name='task')
    op.drop_index(op.f('ix_task_created_at'), table_name='task')
    op.drop_index(op.f('ix_task_cloud'), table_name='task')
    op.drop_index(op.f('ix_task_category'), table_name='task')
    op.drop_index(op.f('ix_task_assigned_to'), table_name='task')
    op.drop_table('task')
    op.drop_table('resource')
    op.drop_table('mcpconfig')
    op.drop_index(op.f('ix_knowledge_bases_is_deleted'), table_name='knowledge_bases')
    op.drop_table('knowledge_bases')
    op.drop_table('awsaccount')
    op.drop_index(op.f('ix_alert_workspace_id'), table_name='alert')
    op.drop_table('alert')
    op.drop_index(op.f('ix_agent_workspace_id'), table_name='agent')
    op.drop_index(op.f('ix_agent_type'), table_name='agent')
    op.drop_index(op.f('ix_agent_title'), table_name='agent')
    op.drop_index(op.f('ix_agent_is_deleted'), table_name='agent')
    op.drop_index(op.f('ix_agent_is_active'), table_name='agent')
    op.drop_index(op.f('ix_agent_id'), table_name='agent')
    op.drop_table('agent')
    op.drop_table('workspace')
    op.drop_index(op.f('ix_useractivationtoken_token'), table_name='useractivationtoken')
    op.drop_table('useractivationtoken')
    op.drop_index(op.f('ix_usage_quota_user_id'), table_name='usage_quota')
    op.drop_table('usage_quota')
    op.drop_index(op.f('ix_notification_user_id'), table_name='notification')
    op.drop_table('notification')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    op.drop_table('setting')
    op.drop_index(op.f('ix_modulesetting_key'), table_name='modulesetting')
    op.drop_table('modulesetting')
    op.drop_table('item')
    op.drop_index(op.f('ix_builtinconnector_name'), table_name='builtinconnector')
    op.drop_table('builtinconnector')
    sa.Enum('ACTIVE', 'INACTIVE', 'EXPIRED', name='credentialstatus').drop(op.get_bind())
    sa.Enum('PRODUCTION', 'STAGING', 'DEVELOPMENT', name='accountenvironement').drop(op.get_bind())
    sa.Enum('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus').drop(op.get_bind())
    sa.Enum('USAGE', 'PERFORMANCE', 'COST', name='metrictype').drop(op.get_bind())
    sa.Enum('INSTANCE_RIGHTSIZING', 'AUTOSCALING_OPTIMIZATION', 'AUTO_START_STOP_OPTIMIZATION', 'VOLUME_OPTIMIZATION', 'SNAPSHOT_CLEANUP', 'RESERVED_INSTANCE_RECOMMENDATION', 'SAVINGS_PLAN_RECOMMENDATION', 'SPOT_INSTANCE_USAGE', 'IDLE_RESOURCE_CLEANUP', 'UNUSED_EIP_CLEANUP', 'ORPHANED_SNAPSHOT_CLEANUP', 'UNDERUTILIZED_EBS_CLEANUP', 'SERVERLESS_MIGRATION', 'CONTAINER_ADOPTION', 'MULTI_AZ_OPTIMIZATION', 'DATA_TRANSFER_OPTIMIZATION', 'CLOUDFRONT_OPTIMIZATION', 'NAT_GATEWAY_OPTIMIZATION', 'RDS_OPTIMIZATION', 'REDSHIFT_OPTIMIZATION', 'DYNAMODB_OPTIMIZATION', 'S3_STORAGE_CLASS_OPTIMIZATION', 'LAMBDA_OPTIMIZATION', 'TAGGING_IMPROVEMENT', 'COST_ALLOCATION_IMPROVEMENT', 'COST_ANOMALY_DETECTION', 'BUDGET_ALERT_SETUP', 'COST_EXPLORER_USAGE', 'MODERNIZE_LEGACY_SERVICES', 'MIGRATE_TO_GRAVITON', 'COMPLIANCE_OPTIMIZATION', 'GOVERNANCE_IMPROVEMENT', 'CROSS_REGION_OPTIMIZATION', 'CROSS_ACCOUNT_OPTIMIZATION', 'PREDICTIVE_SCALING', 'AI_DRIVEN_OPTIMIZATION', 'QUANTUM_COMPUTING_READINESS', 'CARBON_FOOTPRINT_REDUCTION', 'RENEWABLE_ENERGY_USAGE', 'MARKETPLACE_ALTERNATIVE', 'THIRD_PARTY_TOOL_RECOMMENDATION', 'CUSTOM_OPTIMIZATION', 'OTHER', 'EC2_FLEET_OPTIMIZATION', 'SPOT_FLEET_OPTIMIZATION', 'GRAVITON_MIGRATION', 'PREDICTIVE_SCALING_OPTIMIZATION', 'INSTANCE_CONNECT_ENDPOINT', name='recommendationtype').drop(op.get_bind())
    sa.Enum('PENDING', 'IMPLEMENTED', 'IGNORED', 'IN_PROGRESS', name='recommendationstatus').drop(op.get_bind())
    sa.Enum('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus').drop(op.get_bind())
    sa.Enum('START', 'TOOL', 'HUMAN_IN_LOOP', 'END', 'OUTPUT', name='nodetype').drop(op.get_bind())
    sa.Enum('CONVERSATION_AGENT', 'AUTONOMOUS_AGENT', name='agenttype').drop(op.get_bind())
    sa.Enum('AWS', name='cloudprovider').drop(op.get_bind())
    sa.Enum('NONE', 'RECOMMENDATION', name='messageactiontype').drop(op.get_bind())
    sa.Enum('TABLE', 'CHART', name='messagedisplaycomponenttype').drop(op.get_bind())
    sa.Enum('LINE', 'BAR', 'PIE', 'DOUGHNUT', 'AREA', 'SCATTER', 'RADAR', 'STEP_AREA', name='charttype').drop(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus').drop(op.get_bind())
    sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskpriority').drop(op.get_bind())
    sa.Enum('COST_OPTIMIZE', 'OPERATIONAL', 'SCALABILITY', 'SECURITY', 'OPERATIONAL_EFFICIENCY', 'OTHER', name='taskcategoryenum').drop(op.get_bind())
    sa.Enum('ALL', 'OTHER', 'COMPUTE', 'STORAGE', 'SERVERLESS', 'DATABASE', 'NETWORK', 'MESSAGING', 'MANAGEMENT', 'BILLING', 'CROSS_SERVICE', 'MONITORING', 'STREAMING', 'SECURITY', name='taskserviceenum').drop(op.get_bind())
    sa.Enum('AWS', 'AZURE', 'GCP', 'ALL', name='taskcouldenum').drop(op.get_bind())
    sa.Enum('MANUAL', 'AUTOMATED', name='taskmode').drop(op.get_bind())
    sa.Enum('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus').drop(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='uploadstatus').drop(op.get_bind())
    sa.Enum('AUTONOMOUS', 'AGENT', name='runmodeenum').drop(op.get_bind())
    sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='taskimpactenum').drop(op.get_bind())
    sa.Enum('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', name='alertseverity').drop(op.get_bind())
    sa.Enum('OPEN', 'ACKNOWLEDGED', 'RESOLVED', 'CLOSED', name='alertstatus').drop(op.get_bind())
    sa.Enum('INFO', 'WARNING', 'ERROR', 'INTERRUPT', name='notificationtype').drop(op.get_bind())
    sa.Enum('UNREAD', 'READ', 'ARCHIVED', name='notificationstatus').drop(op.get_bind())
    sa.Enum('PRIVATE', 'SHARED', name='kbaccesslevel').drop(op.get_bind())
    sa.Enum('MANUAL', 'AGENT_REQUESTED', 'ALWAYS', name='kbusagemode').drop(op.get_bind())
    sa.Enum('URL', 'FILE', name='documenttype').drop(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='asynctaskstatus').drop(op.get_bind())
    sa.Enum('GOOD', 'BAD', name='feedbacktype').drop(op.get_bind())
    # ### end Alembic commands ###
