"""update_task_history

Revision ID: 6c457300cec5
Revises: b765f9607f2a
Create Date: 2025-06-09 14:06:09.960295

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = '6c457300cec5'
down_revision = 'b765f9607f2a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task', sa.Column('message', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_column('task', 'error')
    op.add_column('taskhistory', sa.Column('message', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_column('taskhistory', 'error')
    op.sync_enum_values('public', 'taskexecutionstatus', ['CANCELLED', 'FAILED', 'RUNNING', 'SUCCEEDED'], ['CANCELLED', 'FAILED', 'REQUIRED_APPROVAL', 'RUNNING', 'SUCCEEDED'], [('task', 'execution_status'), ('taskhistory', 'status')], False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values('public', 'taskexecutionstatus', ['CANCELLED', 'FAILED', 'REQUIRED_APPROVAL', 'RUNNING', 'SUCCEEDED'], ['CANCELLED', 'FAILED', 'RUNNING', 'SUCCEEDED'], [('task', 'execution_status'), ('taskhistory', 'status')], True)
    op.add_column('taskhistory', sa.Column('error', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('taskhistory', 'message')
    op.add_column('task', sa.Column('error', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('task', 'message')
    # ### end Alembic commands ###
