"""add seed version table

Revision ID: 60f058f666fb
Revises: 66d17ce826f8
Create Date: 2025-06-06 13:08:52.355642

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '60f058f666fb'
down_revision = '66d17ce826f8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('seedversion',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('seed_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('version', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('applied_at', sa.DateTime(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('checksum', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_seedversion_seed_type'), 'seedversion', ['seed_type'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_seedversion_seed_type'), table_name='seedversion')
    op.drop_table('seedversion')
    # ### end Alembic commands ###
