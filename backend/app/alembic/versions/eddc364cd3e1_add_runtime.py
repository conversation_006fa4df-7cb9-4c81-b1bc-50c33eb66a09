"""add_runtime

Revision ID: eddc364cd3e1
Revises: 80e3ae75f95a
Create Date: 2025-06-06 21:51:54.184308

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'eddc364cd3e1'
down_revision = '80e3ae75f95a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taskhistory', sa.Column('run_time', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_taskhistory_run_time'), 'taskhistory', ['run_time'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_taskhistory_run_time'), table_name='taskhistory')
    op.drop_column('taskhistory', 'run_time')
    # ### end Alembic commands ###
