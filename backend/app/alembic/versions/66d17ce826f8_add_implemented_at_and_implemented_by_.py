"""Add implemented_at and implemented_by fields to recommendation table

Revision ID: 66d17ce826f8
Revises: fb1174ab0135
Create Date: 2025-06-05 15:29:09.396507

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '66d17ce826f8'
down_revision = 'fb1174ab0135'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('recommendation', sa.Column('implemented_at', sa.DateTime(), nullable=True))
    op.add_column('recommendation', sa.Column('implemented_by', sa.Uuid(), nullable=True))
    op.create_foreign_key(None, 'recommendation', 'user', ['implemented_by'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'recommendation', type_='foreignkey')
    op.drop_column('recommendation', 'implemented_by')
    op.drop_column('recommendation', 'implemented_at')
    # ### end Alembic commands ###
