"""remove_orphaned_ecs_cluster_resources

Revision ID: db745f7c4ad4
Revises: dc9fd7124a3e
Create Date: 2025-06-10 14:11:20.146075

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'db745f7c4ad4'
down_revision = 'dc9fd7124a3e'
branch_labels = None
depends_on = None


def upgrade():
    # Remove any remaining ECS_CLUSTER resources and their related data
    # This is a cleanup migration to ensure no orphaned ECS_CLUSTER records remain
    op.execute("""
        DO $$
        BEGIN
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'resources') THEN
                -- First, delete related metrics for ECS_CLUSTER resources
                DELETE FROM metrics
                WHERE resource_id IN (
                    SELECT id FROM resources WHERE type = 'ECS_CLUSTER'
                );

                -- Delete related recommendations for ECS_CLUSTER resources
                DELETE FROM recommendations
                WHERE resource_id IN (
                    SELECT id FROM resources WHERE type = 'ECS_CLUSTER'
                );

                -- Delete related conversations for ECS_CLUSTER resources (if table exists)
                IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'conversations') THEN
                    DELETE FROM conversations
                    WHERE resource_id IN (
                        SELECT id FROM resources WHERE type = 'ECS_CLUSTER'
                    );
                END IF;

                -- Finally, delete the ECS_CLUSTER resources themselves
                DELETE FROM resources WHERE type = 'ECS_CLUSTER';

                RAISE NOTICE 'Removed all ECS_CLUSTER resources and their related data';
            END IF;
        END $$;
    """)


def downgrade():
    # Note: This migration is irreversible as we're deleting data
    # The downgrade is intentionally left empty as we cannot restore deleted resources
    # without knowing their original data
    op.execute("SELECT 'Cannot restore deleted ECS_CLUSTER resources - migration is irreversible';")
