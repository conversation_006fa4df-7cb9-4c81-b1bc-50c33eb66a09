"""remove_orphaned_ecs_cluster_resources

Revision ID: db745f7c4ad4
Revises: dc9fd7124a3e
Create Date: 2025-06-10 14:11:20.146075

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'db745f7c4ad4'
down_revision = 'dc9fd7124a3e'
branch_labels = None
depends_on = None


def upgrade():
    # Remove any remaining ECS_CLUSTER resources and their related data
    # This is a cleanup migration to ensure no orphaned ECS_CLUSTER records remain
    op.execute("""
        DO $$
        DECLARE
            ecs_cluster_count INTEGER;
            metrics_deleted INTEGER;
            recommendations_deleted INTEGER;
            token_usage_deleted INTEGER;
            message_components_deleted INTEGER;
            message_thoughts_deleted INTEGER;
            message_checkpoints_deleted INTEGER;
            message_feedback_deleted INTEGER;
            messages_deleted INTEGER;
            conversations_deleted INTEGER;
        BEGIN
            -- Check if resource table exists
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'resource') THEN
                -- Count ECS_CLUSTER resources before deletion
                SELECT COUNT(*) INTO ecs_cluster_count FROM resource WHERE type = 'ECS_CLUSTER';

                IF ecs_cluster_count > 0 THEN
                    RAISE NOTICE 'Found % ECS_CLUSTER resources to remove', ecs_cluster_count;

                    -- Delete related metrics for ECS_CLUSTER resources
                    DELETE FROM metric
                    WHERE resource_id IN (
                        SELECT id FROM resource WHERE type = 'ECS_CLUSTER'
                    );
                    GET DIAGNOSTICS metrics_deleted = ROW_COUNT;

                    -- Delete related recommendations for ECS_CLUSTER resources
                    DELETE FROM recommendation
                    WHERE resource_id IN (
                        SELECT id FROM resource WHERE type = 'ECS_CLUSTER'
                    );
                    GET DIAGNOSTICS recommendations_deleted = ROW_COUNT;

                    -- Delete conversation-related data in correct order (deepest dependencies first)
                    -- 1. Delete token_usage records for messages in conversations related to ECS_CLUSTER
                    DELETE FROM token_usage
                    WHERE message_id IN (
                        SELECT m.id FROM message m
                        JOIN conversation c ON m.conversation_id = c.id
                        WHERE c.resource_id IN (SELECT id FROM resource WHERE type = 'ECS_CLUSTER')
                    );
                    GET DIAGNOSTICS token_usage_deleted = ROW_COUNT;

                    -- 2. Delete message display components
                    DELETE FROM messagedisplaycomponent
                    WHERE message_id IN (
                        SELECT m.id FROM message m
                        JOIN conversation c ON m.conversation_id = c.id
                        WHERE c.resource_id IN (SELECT id FROM resource WHERE type = 'ECS_CLUSTER')
                    );
                    GET DIAGNOSTICS message_components_deleted = ROW_COUNT;

                    -- 3. Delete message agent thoughts
                    DELETE FROM messageagentthought
                    WHERE message_id IN (
                        SELECT m.id FROM message m
                        JOIN conversation c ON m.conversation_id = c.id
                        WHERE c.resource_id IN (SELECT id FROM resource WHERE type = 'ECS_CLUSTER')
                    );
                    GET DIAGNOSTICS message_thoughts_deleted = ROW_COUNT;

                    -- 4. Delete message checkpoints
                    DELETE FROM messagecheckpoint
                    WHERE message_id IN (
                        SELECT m.id FROM message m
                        JOIN conversation c ON m.conversation_id = c.id
                        WHERE c.resource_id IN (SELECT id FROM resource WHERE type = 'ECS_CLUSTER')
                    );
                    GET DIAGNOSTICS message_checkpoints_deleted = ROW_COUNT;

                    -- 5. Delete message feedback
                    DELETE FROM message_feedback
                    WHERE message_id IN (
                        SELECT m.id FROM message m
                        JOIN conversation c ON m.conversation_id = c.id
                        WHERE c.resource_id IN (SELECT id FROM resource WHERE type = 'ECS_CLUSTER')
                    );
                    GET DIAGNOSTICS message_feedback_deleted = ROW_COUNT;

                    -- 6. Delete messages
                    DELETE FROM message
                    WHERE conversation_id IN (
                        SELECT id FROM conversation WHERE resource_id IN (SELECT id FROM resource WHERE type = 'ECS_CLUSTER')
                    );
                    GET DIAGNOSTICS messages_deleted = ROW_COUNT;

                    -- 7. Delete conversations
                    DELETE FROM conversation
                    WHERE resource_id IN (
                        SELECT id FROM resource WHERE type = 'ECS_CLUSTER'
                    );
                    GET DIAGNOSTICS conversations_deleted = ROW_COUNT;

                    -- Finally, delete the ECS_CLUSTER resources themselves
                    DELETE FROM resource WHERE type = 'ECS_CLUSTER';

                    RAISE NOTICE 'Successfully removed % ECS_CLUSTER resources and related data:', ecs_cluster_count;
                    RAISE NOTICE '  - % metrics deleted', metrics_deleted;
                    RAISE NOTICE '  - % recommendations deleted', recommendations_deleted;
                    RAISE NOTICE '  - % token usage records deleted', token_usage_deleted;
                    RAISE NOTICE '  - % message components deleted', message_components_deleted;
                    RAISE NOTICE '  - % message thoughts deleted', message_thoughts_deleted;
                    RAISE NOTICE '  - % message checkpoints deleted', message_checkpoints_deleted;
                    RAISE NOTICE '  - % message feedback deleted', message_feedback_deleted;
                    RAISE NOTICE '  - % messages deleted', messages_deleted;
                    RAISE NOTICE '  - % conversations deleted', conversations_deleted;
                ELSE
                    RAISE NOTICE 'No ECS_CLUSTER resources found to remove';
                END IF;
            ELSE
                RAISE NOTICE 'Resource table does not exist, skipping migration';
            END IF;
        END $$;
    """)


def downgrade():
    # Note: This migration is irreversible as we're deleting data
    # The downgrade is intentionally left empty as we cannot restore deleted resources
    # without knowing their original data
    op.execute("SELECT 'Cannot restore deleted ECS_CLUSTER resources - migration is irreversible';")
