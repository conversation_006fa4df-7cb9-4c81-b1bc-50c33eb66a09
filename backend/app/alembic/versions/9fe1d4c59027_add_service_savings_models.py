"""Add service savings models

Revision ID: 9fe1d4c59027
Revises: b765f9607f2a
Create Date: 2025-06-09 23:33:06.272369

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9fe1d4c59027'
down_revision = 'b765f9607f2a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
