"""remove technical_guidances and document_url

Revision ID: fb1174ab0135
Revises: 4e5fa7368e94
Create Date: 2025-06-05 13:24:59.221763

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fb1174ab0135'
down_revision = '4e5fa7368e94'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('recommendation', 'technical_guidances')
    op.drop_column('recommendation', 'document_url')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('recommendation', sa.Column('document_url', sa.VARCHAR(length=2048), autoincrement=False, nullable=True))
    op.add_column('recommendation', sa.Column('technical_guidances', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
