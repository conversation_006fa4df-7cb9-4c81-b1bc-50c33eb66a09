"""revamp_task_table

Revision ID: a824fc066ab3
Revises: 60f058f666fb
Create Date: 2025-06-06 19:06:06.536832

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a824fc066ab3'
down_revision = '60f058f666fb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('PENDING', 'SCHEDULED', name='taskscheduledstatus').create(op.get_bind())
    sa.Enum('RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', name='taskexecutionstatus').create(op.get_bind())
    op.drop_index('ix_taskconversation_conversation_id', table_name='taskconversation')
    op.drop_index('ix_taskconversation_id', table_name='taskconversation')
    op.drop_index('ix_taskconversation_status', table_name='taskconversation')
    op.drop_index('ix_taskconversation_task_id', table_name='taskconversation')
    op.drop_table('taskconversation')
    op.add_column('task', sa.Column('error', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('task', sa.Column('scheduled_status', postgresql.ENUM('PENDING', 'SCHEDULED', name='taskscheduledstatus', create_type=False), nullable=False))
    op.alter_column('task', 'execution_status',
               existing_type=postgresql.ENUM('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus'),
               type_=sa.Enum('RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', name='taskexecutionstatus'),
               nullable=True,
               postgresql_using="""
                   CASE
                       WHEN execution_status = 'RUNNING' THEN 'RUNNING'::taskexecutionstatus
                       WHEN execution_status = 'SUCCEEDED' THEN 'SUCCEEDED'::taskexecutionstatus
                       WHEN execution_status = 'FAILED' THEN 'FAILED'::taskexecutionstatus
                       WHEN execution_status = 'TIMEOUT' THEN 'FAILED'::taskexecutionstatus
                       WHEN execution_status = 'CANCELLED' THEN 'CANCELLED'::taskexecutionstatus
                       WHEN execution_status = 'PENDING' THEN NULL
                       WHEN execution_status = 'SCHEDULED' THEN NULL
                       ELSE NULL
                   END
               """)
    op.alter_column('task', 'next_run',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_index('ix_task_assigned_to', table_name='task')
    op.drop_index('ix_task_deadline', table_name='task')
    op.drop_index('ix_task_mode', table_name='task')
    op.drop_index('ix_task_scheduled_at', table_name='task')
    op.drop_index('ix_task_status', table_name='task')
    op.create_index(op.f('ix_task_scheduled_status'), 'task', ['scheduled_status'], unique=False)
    op.drop_constraint('task_assigned_to_fkey', 'task', type_='foreignkey')
    op.drop_column('task', 'mode')
    op.drop_column('task', 'completion_notes')
    op.drop_column('task', 'retry_count')
    op.drop_column('task', 'scheduled_at')
    op.drop_column('task', 'deadline')
    op.drop_column('task', 'timeout_seconds')
    op.drop_column('task', 'status')
    op.drop_column('task', 'max_retries')
    op.drop_column('task', 'assigned_to')
    op.drop_column('task', 'last_error')
    op.add_column('taskhistory', sa.Column('conversation_id', sa.Uuid(), nullable=False))
    op.add_column('taskhistory', sa.Column('status', postgresql.ENUM('RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', name='taskexecutionstatus', create_type=False), nullable=False))
    op.add_column('taskhistory', sa.Column('error', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('taskhistory', sa.Column('celery_task_id', sa.Uuid(), nullable=True))
    op.add_column('taskhistory', sa.Column('start_time', sa.DateTime(), nullable=False))
    op.add_column('taskhistory', sa.Column('end_time', sa.DateTime(), nullable=True))
    op.drop_index('ix_taskhistory_changed_at', table_name='taskhistory')
    op.create_index(op.f('ix_taskhistory_conversation_id'), 'taskhistory', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_taskhistory_end_time'), 'taskhistory', ['end_time'], unique=False)
    op.create_index(op.f('ix_taskhistory_id'), 'taskhistory', ['id'], unique=False)
    op.create_index(op.f('ix_taskhistory_start_time'), 'taskhistory', ['start_time'], unique=False)
    op.create_index(op.f('ix_taskhistory_status'), 'taskhistory', ['status'], unique=False)
    op.drop_constraint('taskhistory_changed_by_fkey', 'taskhistory', type_='foreignkey')
    op.create_foreign_key(None, 'taskhistory', 'conversation', ['conversation_id'], ['id'])
    op.drop_column('taskhistory', 'changed_by')
    op.drop_column('taskhistory', 'previous_state')
    op.drop_column('taskhistory', 'change_type')
    op.drop_column('taskhistory', 'changed_at')
    op.drop_column('taskhistory', 'new_state')
    sa.Enum('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus').drop(op.get_bind())
    sa.Enum('MANUAL', 'AUTOMATED', name='taskmode').drop(op.get_bind())
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus').create(op.get_bind())
    sa.Enum('MANUAL', 'AUTOMATED', name='taskmode').create(op.get_bind())
    sa.Enum('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus').create(op.get_bind())
    op.add_column('taskhistory', sa.Column('new_state', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('taskhistory', sa.Column('changed_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False))
    op.add_column('taskhistory', sa.Column('change_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.add_column('taskhistory', sa.Column('previous_state', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('taskhistory', sa.Column('changed_by', sa.UUID(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'taskhistory', type_='foreignkey')
    op.create_foreign_key('taskhistory_changed_by_fkey', 'taskhistory', 'user', ['changed_by'], ['id'])
    op.drop_index(op.f('ix_taskhistory_status'), table_name='taskhistory')
    op.drop_index(op.f('ix_taskhistory_start_time'), table_name='taskhistory')
    op.drop_index(op.f('ix_taskhistory_id'), table_name='taskhistory')
    op.drop_index(op.f('ix_taskhistory_end_time'), table_name='taskhistory')
    op.drop_index(op.f('ix_taskhistory_conversation_id'), table_name='taskhistory')
    op.create_index('ix_taskhistory_changed_at', 'taskhistory', ['changed_at'], unique=False)
    op.drop_column('taskhistory', 'end_time')
    op.drop_column('taskhistory', 'start_time')
    op.drop_column('taskhistory', 'celery_task_id')
    op.drop_column('taskhistory', 'error')
    op.drop_column('taskhistory', 'status')
    op.drop_column('taskhistory', 'conversation_id')
    op.add_column('task', sa.Column('last_error', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('assigned_to', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('max_retries', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus', create_type=False), autoincrement=False, nullable=False))
    op.add_column('task', sa.Column('timeout_seconds', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('deadline', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('scheduled_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('retry_count', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('completion_notes', sa.VARCHAR(length=2000), autoincrement=False, nullable=True))
    op.add_column('task', sa.Column('mode', postgresql.ENUM('MANUAL', 'AUTOMATED', name='taskmode', create_type=False), autoincrement=False, nullable=False))
    op.create_foreign_key('task_assigned_to_fkey', 'task', 'user', ['assigned_to'], ['id'])
    op.drop_index(op.f('ix_task_scheduled_status'), table_name='task')
    op.create_index('ix_task_status', 'task', ['status'], unique=False)
    op.create_index('ix_task_scheduled_at', 'task', ['scheduled_at'], unique=False)
    op.create_index('ix_task_mode', 'task', ['mode'], unique=False)
    op.create_index('ix_task_deadline', 'task', ['deadline'], unique=False)
    op.create_index('ix_task_assigned_to', 'task', ['assigned_to'], unique=False)
    op.alter_column('task', 'next_run',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('task', 'execution_status',
               existing_type=sa.Enum('RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', name='taskexecutionstatus'),
               type_=postgresql.ENUM('PENDING', 'SCHEDULED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'CANCELLED', name='executionstatus'),
               nullable=False)
    op.drop_column('task', 'scheduled_status')
    op.drop_column('task', 'error')
    op.create_table('taskconversation',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('conversation_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus', create_type=False), autoincrement=False, nullable=False),
    sa.Column('celery_task_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversation.id'], name='taskconversation_conversation_id_fkey'),
    sa.ForeignKeyConstraint(['task_id'], ['task.id'], name='taskconversation_task_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='taskconversation_pkey')
    )
    op.create_index('ix_taskconversation_task_id', 'taskconversation', ['task_id'], unique=False)
    op.create_index('ix_taskconversation_status', 'taskconversation', ['status'], unique=False)
    op.create_index('ix_taskconversation_id', 'taskconversation', ['id'], unique=False)
    op.create_index('ix_taskconversation_conversation_id', 'taskconversation', ['conversation_id'], unique=False)
    sa.Enum('RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', name='taskexecutionstatus').drop(op.get_bind())
    sa.Enum('PENDING', 'SCHEDULED', name='taskscheduledstatus').drop(op.get_bind())
    # ### end Alembic commands ###
