"""init_schedule_task_buitlin_tools

Revision ID: a085fc2f3174
Revises: e28b969d5475
Create Date: 2025-06-08 16:52:16.536625

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlmodel import Session, select
from uuid import uuid4


# revision identifiers, used by Alembic.
revision = 'a085fc2f3174'
down_revision = 'e28b969d5475'
branch_labels = None
depends_on = None

# Constants for builtin connectors (replicated here to avoid import issues)
BUILTIN_CONNECTORS = [
    {
        "name": "aws_script_execution_read_only_permissions",
        "display_name": "AWS Script Execution Read Only Permissions",
        "description": "Built-in connector for AWS Runtime operations with read only permissions",
        "default_required_permission": False,
    },
    {
        "name": "aws_script_execution_read_only_permissions_price_list",
        "display_name": "AWS Script Execution Read Only Permissions Price List",
        "description": "Built-in connector for AWS Runtime operations with read only permissions to price list",
        "default_required_permission": False,
    },
    {
        "name": "aws_script_execution_write_permissions",
        "display_name": "AWS Script Execution Write Permissions",
        "description": "Built-in connector for AWS Runtime operations with write permissions",
        "default_required_permission": True,
    },
    {
        "name": "create_chart",
        "display_name": "Create Chart",
        "description": "Built-in connector for creating charts",
        "default_required_permission": False,
    },
    {
        "name": "push_alert",
        "display_name": "Push Alert",
        "description": "Built-in connector for pushing alerts",
        "default_required_permission": False,
    },
    {
        "name": "recommendation",
        "display_name": "Recommendation",
        "description": "Built-in connector for recommendations",
        "default_required_permission": False,
    },
    {
        "name": "search_internet",
        "display_name": "Search Internet",
        "description": "Built-in connector for searching the internet",
        "default_required_permission": True,
    },
    {
        "name": "planning",
        "display_name": "Planning",
        "description": "Built-in connector for planning",
        "default_required_permission": False,
    },
    {
        "name": "schedule_task",
        "display_name": "Schedule Task",
        "description": "Built-in connector for scheduling tasks",
        "default_required_permission": True,
    },
]


def upgrade():
    # Get database connection
    bind = op.get_bind()
    session = Session(bind)

    try:
        # Step 1: Create or update built-in connectors
        connectors = []
        for connector_data in BUILTIN_CONNECTORS:
            # Check if connector already exists
            result = session.execute(
                sa.text("SELECT id, name, display_name, description, default_required_permission FROM builtinconnector WHERE name = :name"),
                {"name": connector_data["name"]}
            )
            existing_connector = result.fetchone()

            if not existing_connector:
                # Create new connector
                connector_id = str(uuid4())
                session.execute(
                    sa.text("""
                        INSERT INTO builtinconnector (id, name, display_name, description, default_required_permission, created_at, updated_at)
                        VALUES (:id, :name, :display_name, :description, :default_required_permission, NOW(), NOW())
                    """),
                    {
                        "id": connector_id,
                        "name": connector_data["name"],
                        "display_name": connector_data["display_name"],
                        "description": connector_data["description"],
                        "default_required_permission": connector_data["default_required_permission"]
                    }
                )
                connectors.append((connector_id, connector_data))
                print(f"Created built-in connector: {connector_data['name']}")
            else:
                # Check if update is needed
                needs_update = (
                    existing_connector[1] != connector_data["name"] or
                    existing_connector[2] != connector_data["display_name"] or
                    existing_connector[3] != connector_data["description"] or
                    existing_connector[4] != connector_data["default_required_permission"]
                )

                if needs_update:
                    # Update existing connector
                    session.execute(
                        sa.text("""
                            UPDATE builtinconnector
                            SET display_name = :display_name,
                                description = :description,
                                default_required_permission = :default_required_permission,
                                updated_at = NOW()
                            WHERE name = :name
                        """),
                        {
                            "name": connector_data["name"],
                            "display_name": connector_data["display_name"],
                            "description": connector_data["description"],
                            "default_required_permission": connector_data["default_required_permission"]
                        }
                    )
                    print(f"Updated built-in connector: {connector_data['name']}")

                connectors.append((existing_connector[0], connector_data))

        # Step 2: Associate connectors with existing workspaces
        # Get all workspaces
        workspaces_result = session.execute(sa.text("SELECT id FROM workspace"))
        workspaces = workspaces_result.fetchall()

        if workspaces:
            associations_created = 0
            for workspace_row in workspaces:
                workspace_id = workspace_row[0]

                for connector_id, connector_data in connectors:
                    # Check if association already exists
                    existing_assoc = session.execute(
                        sa.text("""
                            SELECT id FROM workspacebuiltinconnector
                            WHERE workspace_id = :workspace_id AND connector_id = :connector_id
                        """),
                        {"workspace_id": workspace_id, "connector_id": connector_id}
                    ).fetchone()

                    if not existing_assoc:
                        # Create new association
                        association_id = str(uuid4())
                        session.execute(
                            sa.text("""
                                INSERT INTO workspacebuiltinconnector (id, workspace_id, connector_id, is_active, required_permission)
                                VALUES (:id, :workspace_id, :connector_id, :is_active, :required_permission)
                            """),
                            {
                                "id": association_id,
                                "workspace_id": workspace_id,
                                "connector_id": connector_id,
                                "is_active": True,  # Enable by default
                                "required_permission": connector_data["default_required_permission"]
                            }
                        )
                        associations_created += 1

            if associations_created > 0:
                print(f"Created {associations_created} new connector associations")
        else:
            print("No workspaces found. Skipping built-in connector association.")

        # Commit all changes
        session.commit()
        print(f"Successfully initialized {len(connectors)} built-in connectors for {len(workspaces)} workspaces")

    except Exception as e:
        session.rollback()
        print(f"Error initializing built-in connectors: {str(e)}")
        raise
    finally:
        session.close()


def downgrade():
    # Get database connection
    bind = op.get_bind()
    session = Session(bind)

    try:
        # Remove workspace-builtin connector associations for the schedule_task connector
        # Note: We only remove the schedule_task associations in downgrade to be conservative
        result = session.execute(
            sa.text("SELECT id FROM builtinconnector WHERE name = 'schedule_task'")
        )
        schedule_task_connector = result.fetchone()

        if schedule_task_connector:
            connector_id = schedule_task_connector[0]

            # Remove associations
            session.execute(
                sa.text("DELETE FROM workspacebuiltinconnector WHERE connector_id = :connector_id"),
                {"connector_id": connector_id}
            )

            # Remove the connector itself
            session.execute(
                sa.text("DELETE FROM builtinconnector WHERE id = :connector_id"),
                {"connector_id": connector_id}
            )

            print("Removed schedule_task builtin connector and its associations")

        session.commit()

    except Exception as e:
        session.rollback()
        print(f"Error during downgrade: {str(e)}")
        raise
    finally:
        session.close()
