"""empty message

Revision ID: c40620e42e2e
Revises: 6c457300cec5, 9fe1d4c59027
Create Date: 2025-06-10 08:23:09.054163

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'c40620e42e2e'
down_revision = ('6c457300cec5', '9fe1d4c59027')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
