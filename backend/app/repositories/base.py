import uuid
from abc import ABC, abstractmethod
from typing import Generic, TypeVar

from sqlmodel import SQLModel, select
from sqlmodel.ext.asyncio.session import AsyncSession

ModelType = TypeVar("ModelType", bound=SQLModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=SQLModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=SQLModel)


class Result(Generic[ModelType]):
    """
    Result is a generic class that represents the result of an operation.
    It is used to return a value or an error from an operation.
    """

    def __init__(
        self, success: bool, value: ModelType | None = None, error: str | None = None
    ):
        self.success = success
        self.value = value
        self.error = error

    @property
    def failure(self) -> bool:
        return not self.success

    @classmethod
    def Ok(cls, value: ModelType) -> "Result":
        return cls(True, value=value, error=None)

    @classmethod
    def Fail(cls, error: str) -> "Result":
        return cls(False, value=None, error=error)


class IRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """
    IRepository is an abstract class that defines the interface for a repository.
    It is used to define the methods that a repository must implement.
    """

    @abstractmethod
    async def get_by_id(self, db: AsyncSession, id: uuid.UUID) -> Result[ModelType]:
        pass

    @abstractmethod
    async def get_all(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        search: str | None = None,
    ) -> Result[list[ModelType]]:
        pass

    @abstractmethod
    async def create(
        self, db: AsyncSession, *, obj_in: CreateSchemaType
    ) -> Result[ModelType]:
        pass

    @abstractmethod
    async def update(
        self, db: AsyncSession, *, db_obj: ModelType, obj_in: UpdateSchemaType
    ) -> Result[ModelType]:
        pass

    @abstractmethod
    async def remove(self, db: AsyncSession, *, id: uuid.UUID) -> Result[ModelType]:
        pass


class BaseRepository(IRepository[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    BaseRepository is a concrete implementation of the IRepository interface.
    It is used to define the methods that a repository must implement.
    """

    def __init__(self, model: type[ModelType]):
        self.model = model

    async def get_by_id(self, db: AsyncSession, id: uuid.UUID) -> Result[ModelType]:
        try:
            statement = select(self.model).where(self.model.id == id)
            result = await db.exec(statement)
            return Result.Ok(result.first())
        except Exception as e:
            return Result.Fail(str(e))

    async def get_all(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        search: str | None = None,
    ) -> Result[list[ModelType]]:
        try:
            statement = select(self.model).offset(skip).limit(limit)
            if search:
                statement = statement.where(self.model.name.ilike(f"%{search}%"))
            result = await db.exec(statement)
            return Result.Ok(result.all())
        except Exception as e:
            return Result.Fail(str(e))

    async def create(
        self, db: AsyncSession, *, obj_in: CreateSchemaType
    ) -> Result[ModelType]:
        try:
            db_obj = self.model.model_validate(obj_in)
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            return Result.Ok(db_obj)
        except Exception as e:
            return Result.Fail(str(e))

    async def update(
        self, db: AsyncSession, *, db_obj: ModelType, obj_in: UpdateSchemaType
    ) -> Result[ModelType]:
        try:
            obj_data = obj_in.model_dump(exclude_unset=True)
            for field, value in obj_data.items():
                setattr(db_obj, field, value)
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            return Result.Ok(db_obj)
        except Exception as e:
            return Result.Fail(str(e))

    async def remove(self, db: AsyncSession, *, id: uuid.UUID) -> Result[ModelType]:
        try:
            obj = await db.get(self.model, id)
            if obj:
                await db.delete(obj)
            await db.commit()
            return Result.Ok(obj)
        except Exception as e:
            return Result.Fail(str(e))
