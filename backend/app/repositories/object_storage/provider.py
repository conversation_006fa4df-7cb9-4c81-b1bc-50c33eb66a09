from app.core.config import settings
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.minio import MinioStorageRepository
from app.repositories.object_storage.s3 import S3StorageRepository


def get_object_storage_repository() -> BaseStorageRepository:
    storage_option = settings.OBJECT_STORAGE_OPTION.lower()

    if storage_option == "minio":
        return MinioStorageRepository()
    elif storage_option == "s3":
        return S3StorageRepository()
    else:
        raise ValueError(
            f"Invalid storage option: {storage_option}. Choose from: 'local', 'minio', or 's3'."
        )
