import asyncio
import io
import os
from collections.abc import As<PERSON><PERSON><PERSON>ator
from concurrent.futures import <PERSON>hr<PERSON><PERSON>ool<PERSON>xecutor
from datetime import timed<PERSON><PERSON>
from uuid import uuid4

import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from fastapi import HTTPEx<PERSON>, UploadFile

from app.core.config import settings

from .base import BaseStorageRepository

load_dotenv()


class S3StorageRepository(BaseStorageRepository):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.session = boto3.Session(
            region_name=os.getenv("S3_REGION"),
            aws_access_key_id=os.getenv("S3_ACCESS_KEY"),
            aws_secret_access_key=os.getenv("S3_SECRET_KEY"),
        )
        self.images_bucket = settings.IMAGES_BUCKET
        self.kb_bucket = settings.KB_BUCKET
        # Thread pool executor for async operations
        self._executor = ThreadPoolExecutor(max_workers=10)

    def _get_client(self):
        """Get a new S3 client for thread-safe operations."""
        return self.session.client("s3")

    async def upload_file(
        self,
        file: UploadFile,
        prefix: str,
        bucket_name: str,
        metadata: dict[str, str] | None = None,
    ) -> dict:
        """
        Upload a file to the specified bucket.

        Args:
            file: The file to upload
            prefix: Prefix for the object key
            bucket_name: Target bucket name
            metadata: Optional metadata to attach to the object

        Returns:
            Object name/path of the uploaded file
        """

        content_type = file.content_type
        try:
            # Read file content
            file_data = await file.read()

            # Upload to S3
            object_name = f"{prefix}/{file.filename}"

            def _upload():
                s3_client = self._get_client()
                s3_client.put_object(
                    Bucket=bucket_name,
                    Key=object_name,
                    Body=file_data,
                    ContentType=content_type,
                    Metadata=metadata or {},
                )

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, _upload)

            return {
                "object_name": object_name,
                "name": file.filename,
                "type": content_type,
            }

        except ClientError as err:
            raise HTTPException(status_code=500, detail=f"Failed to upload file: {err}")
        finally:
            # Reset file pointer for potential reuse
            await file.seek(0)

    async def upload_files(
        self,
        files: list[UploadFile],
        prefix: str,
        bucket_name: str,
    ) -> list[dict]:
        """
        Upload multiple files to the specified bucket.
        """

        return await asyncio.gather(
            *[
                self.upload_file(
                    file,
                    prefix=prefix,
                    bucket_name=bucket_name,
                )
                for file in files
            ]
        )

    async def upload_bytes(
        self,
        data: bytes,
        bucket_name: str,
        object_name: str | None = None,
        content_type: str = "application/octet-stream",
        metadata: dict[str, str] | None = None,
    ) -> str:
        """
        Upload bytes data to the specified bucket.

        Args:
            data: The bytes data to upload
            bucket_name: Target bucket name
            object_name: Custom object name (generated if not provided)
            content_type: MIME type of the content
            metadata: Optional metadata to attach to the object

        Returns:
            Object name/path of the uploaded bytes
        """

        # Generate a unique object name if not provided
        if not object_name:
            object_name = str(uuid4())

        try:

            def _upload():
                s3_client = self._get_client()
                s3_client.put_object(
                    Bucket=bucket_name,
                    Key=object_name,
                    Body=data,
                    ContentType=content_type,
                    Metadata=metadata or {},
                )

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, _upload)

            return object_name
        except ClientError as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to upload bytes data: {err}"
            )

    async def get_file(
        self, object_name: str, bucket_name: str
    ) -> tuple[io.BytesIO, int, str, dict[str, str]]:
        """
        Get a file from the specified bucket.

        Args:
            object_name: The name of the object to retrieve
            bucket_name: Source bucket name

        Returns:
            Tuple containing file data, size, content type, and metadata
        """

        try:

            def _get():
                s3_client = self._get_client()
                # Get the object
                response = s3_client.get_object(Bucket=bucket_name, Key=object_name)

                # Read the data
                data = response["Body"].read()

                # Extract metadata
                content_type = response.get("ContentType", "application/octet-stream")
                content_length = response.get("ContentLength", len(data))
                metadata = response.get("Metadata", {})

                return (io.BytesIO(data), content_length, content_type, metadata)

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, _get)

        except ClientError as err:
            if err.response["Error"]["Code"] == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(
                status_code=500, detail=f"Failed to retrieve file: {err}"
            )

    async def stream_file(
        self, object_name: str, bucket_name: str
    ) -> AsyncGenerator[bytes, None]:
        """
        Stream a file from the specified bucket in chunks.

        Args:
            object_name: The name of the object to retrieve
            bucket_name: Source bucket name

        Yields:
            Chunks of file data as bytes
        """

        try:

            def _get_response():
                s3_client = self._get_client()
                return s3_client.get_object(Bucket=bucket_name, Key=object_name)

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(self._executor, _get_response)

            # Stream the response in chunks
            # Note: This is a simplified approach. For true streaming,
            # you might want to use a different approach with threading
            body = response["Body"]
            while True:
                chunk = body.read(8192)
                if not chunk:
                    break
                yield chunk

        except ClientError as err:
            if err.response["Error"]["Code"] == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(status_code=500, detail=f"Failed to stream file: {err}")

    async def download_file(
        self, object_name: str, file_path: str, bucket_name: str
    ) -> None:
        """
        Download a file from the bucket to a local path.

        Args:
            object_name: The name of the object to download
            file_path: Local path where the file will be saved
            bucket_name: Source bucket name
        """

        try:

            def _download():
                s3_client = self._get_client()
                s3_client.download_file(bucket_name, object_name, file_path)

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, _download)
        except ClientError as err:
            if err.response["Error"]["Code"] == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(
                status_code=500, detail=f"Failed to download file: {err}"
            )

    async def delete_file(self, object_name: str, bucket_name: str) -> None:
        """
        Delete a file from the specified bucket.

        Args:
            object_name: The name of the object to delete
            bucket_name: Source bucket name
        """

        try:

            def _delete():
                s3_client = self._get_client()
                s3_client.delete_object(Bucket=bucket_name, Key=object_name)

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, _delete)
        except ClientError as err:
            # If object doesn't exist, we consider deletion successful
            if err.response["Error"]["Code"] != "NoSuchKey":
                raise HTTPException(
                    status_code=500, detail=f"Failed to delete file: {err}"
                )

    async def delete_multiple_files(
        self, object_names: list[str], bucket_name: str
    ) -> None:
        """
        Delete multiple files from the specified bucket.

        Args:
            object_names: List of object names to delete
            bucket_name: Source bucket name
        """

        try:

            def _delete_multiple():
                s3_client = self._get_client()
                # Prepare delete objects request
                delete_objects = {
                    "Objects": [{"Key": object_name} for object_name in object_names],
                    "Quiet": False,
                }

                response = s3_client.delete_objects(
                    Bucket=bucket_name, Delete=delete_objects
                )

                # Check for errors
                errors = response.get("Errors", [])
                if errors:
                    error_count = len(errors)
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to delete {error_count} of {len(object_names)} files",
                    )

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, _delete_multiple)

        except ClientError as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to delete files: {err}"
            )

    async def list_files(
        self,
        bucket_name: str,
        prefix: str = "",
        recursive: bool = True,
        skip: int = 0,
        limit: int = 50,
        search: str | None = None,
    ) -> list[dict[str, str | int]]:
        """
        List objects in the specified bucket.

        Args:
            bucket_name: Source bucket name
            prefix: Prefix filter for object names
            recursive: Whether to list objects recursively (not used in S3, always recursive)
            skip: Number of objects to skip
            limit: Maximum number of objects to return
            search: Search term to filter object names

        Returns:
            List of objects with their metadata
        """

        try:

            def _list():
                s3_client = self._get_client()
                objects = []
                paginator = s3_client.get_paginator("list_objects_v2")

                for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix):
                    for obj in page.get("Contents", []):
                        objects.append(
                            {
                                "name": obj["Key"],
                                "size": obj["Size"],
                                "last_modified": obj["LastModified"].isoformat(),
                            }
                        )

                # Apply search filter
                if search:
                    objects = [obj for obj in objects if search in obj["name"]]

                # Apply pagination
                if skip:
                    objects = objects[skip:]
                if limit:
                    objects = objects[:limit]

                return objects

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, _list)

        except ClientError as err:
            raise HTTPException(status_code=500, detail=f"Failed to list files: {err}")

    async def get_presigned_url(
        self,
        object_name: str,
        bucket_name: str,
        expires: timedelta | None = None,
        response_headers: dict[str, str] | None = None,
    ) -> str:
        """
        Generate a presigned URL for object download.

        Args:
            object_name: The name of the object
            bucket_name: Source bucket name
            expires: Expiration time
            response_headers: Additional response headers

        Returns:
            Presigned URL for downloading the object
        """

        try:

            def _generate_url():
                s3_client = self._get_client()
                # Convert timedelta to seconds if provided
                expires_in = int(expires.total_seconds()) if expires else 3600

                params = {
                    "Bucket": bucket_name,
                    "Key": object_name,
                }

                if response_headers:
                    params.update(response_headers)

                url = s3_client.generate_presigned_url(
                    "get_object",
                    Params=params,
                    ExpiresIn=expires_in,
                )
                return url

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, _generate_url)

        except ClientError as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate presigned URL: {err}"
            )

    async def get_presigned_put_url(
        self,
        object_name: str,
        bucket_name: str,
        expires: int = 3600,
        content_type: str | None = None,
    ) -> str:
        """
        Generate a presigned URL for object upload.

        Args:
            object_name: The name for the object to be uploaded
            bucket_name: Target bucket name
            expires: Expiration time in seconds
            content_type: Content type for the upload

        Returns:
            Presigned URL for uploading to the object
        """

        try:

            def _generate_put_url():
                s3_client = self._get_client()
                params = {"Bucket": bucket_name, "Key": object_name}

                # Add content type if provided to ensure consistency
                if content_type:
                    params["ContentType"] = content_type

                url = s3_client.generate_presigned_url(
                    "put_object",
                    Params=params,
                    ExpiresIn=expires,
                )
                return url

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, _generate_put_url)

        except ClientError as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate presigned PUT URL: {err}"
            )

    async def copy_object(
        self,
        source_bucket_name: str,
        source_object_name: str,
        dest_bucket_name: str,
        dest_object_name: str | None = None,
        metadata: dict[str, str] | None = None,
    ) -> None:
        """
        Copy an object from source to destination.

        Args:
            source_bucket_name: Source bucket name
            source_object_name: Source object name
            dest_bucket_name: Destination bucket name
            dest_object_name: Destination object name (defaults to source name)
            metadata: Optional metadata to attach to the destination object
        """

        # Use source object name as destination if not specified
        dest_object_name = dest_object_name or source_object_name

        try:

            def _copy():
                s3_client = self._get_client()
                copy_source = {"Bucket": source_bucket_name, "Key": source_object_name}

                copy_args = {
                    "CopySource": copy_source,
                    "Bucket": dest_bucket_name,
                    "Key": dest_object_name,
                }

                if metadata:
                    copy_args["Metadata"] = metadata
                    copy_args["MetadataDirective"] = "REPLACE"

                s3_client.copy_object(**copy_args)

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, _copy)

        except ClientError as err:
            raise HTTPException(status_code=500, detail=f"Failed to copy object: {err}")

    async def get_object_metadata(
        self, object_name: str, bucket_name: str
    ) -> dict[str, str]:
        """
        Get object metadata.

        Args:
            object_name: The name of the object
            bucket_name: Source bucket name

        Returns:
            Object metadata
        """

        try:

            def _get_metadata():
                s3_client = self._get_client()
                response = s3_client.head_object(Bucket=bucket_name, Key=object_name)
                return response.get("Metadata", {})

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, _get_metadata)

        except ClientError as err:
            if err.response["Error"]["Code"] == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(
                status_code=500, detail=f"Failed to get object metadata: {err}"
            )

    async def file_exists(self, object_name: str, bucket_name: str) -> bool:
        """
        Check if a file exists in the bucket.

        Args:
            object_name: The name of the object to check
            bucket_name: Source bucket name

        Returns:
            True if the file exists, False otherwise
        """

        try:

            def _check_exists():
                s3_client = self._get_client()
                s3_client.head_object(Bucket=bucket_name, Key=object_name)
                return True

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, _check_exists)
        except ClientError as err:
            error_code = err.response["Error"]["Code"]
            # Handle both NoSuchKey and 404 errors as file not found
            if error_code in ("NoSuchKey", "404", "NotFound"):
                return False
            raise HTTPException(
                status_code=500, detail=f"Failed to check if file exists: {err}"
            )
