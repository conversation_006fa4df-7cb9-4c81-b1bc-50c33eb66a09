from uuid import UUID

from sqlmodel import Session, func, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import (
    Agent,
)
from app.schemas.agent import AgentsPublic, AgentUpdate


class AgentRepository:
    def __init__(
        self,
        async_session: AsyncSession | None = None,
        session: Session | None = None,
    ):
        self.async_session = async_session
        self.session = session

    async def create_agent(self, agent: Agent) -> Agent:
        self.async_session.add(agent)
        await self.async_session.commit()
        await self.async_session.refresh(agent)
        return agent

    async def get_agents(
        self, workspace_id: UUID, skip: int, limit: int
    ) -> list[Agent]:
        count_statement = (
            select(func.count())
            .select_from(Agent)
            .where(
                Agent.workspace_id == workspace_id,
            )
        )
        count = await self.async_session.exec(count_statement)
        count = count.one()

        statement = (
            select(Agent)
            .where(Agent.workspace_id == workspace_id)
            .offset(skip)
            .limit(limit)
        )
        agents = await self.async_session.exec(statement)
        agents = agents.all()

        return AgentsPublic(data=agents, count=count)

    async def get_agent(self, id: UUID) -> Agent | None:
        statement = select(Agent).where(Agent.id == id)
        response = await self.async_session.exec(statement)
        return response.one_or_none()

    async def update_agent(self, id: UUID, agent_update: AgentUpdate) -> Agent | str:
        statement = select(Agent).where(Agent.id == id)
        result = await self.async_session.exec(statement)
        db_agent = result.one_or_none()

        if db_agent is None:
            return "Agent not found"

        for key, value in agent_update.model_dump(exclude_unset=True).items():
            if value is not None:
                setattr(db_agent, key, value)

        self.async_session.add(db_agent)
        await self.async_session.commit()
        await self.async_session.refresh(db_agent)
        return db_agent

    async def delete_agent(self, id: UUID) -> str | bool:
        statement = select(Agent).where(Agent.id == id)
        agent = await self.async_session.exec(statement)
        agent = agent.one_or_none()
        if agent is None:
            return "Agent not found"
        await self.async_session.delete(agent)
        await self.async_session.commit()
        return True

    async def get_all_agent_roles(self, workspace_id: UUID) -> list[str]:
        statement = select(Agent.title).where(Agent.workspace_id == workspace_id)
        response = await self.async_session.exec(statement)
        results = response.all()
        agent_roles = [
            agent_role.split(" ")[0]
            for agent_role in results
            if "Cloud Thinker" not in agent_role
        ]
        return agent_roles
