from datetime import datetime, timedelta
from uuid import UUID

from fastapi import HTT<PERSON>Ex<PERSON>
from sqlmodel import Session, and_, func, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser
from app.models import (
    Notification,
    NotificationCreate,
    NotificationList,
    NotificationResponse,
    NotificationStatus,
    NotificationType,
    NotificationUpdate,
)


class NotificationRepository:
    def __init__(self, async_session: AsyncSession | None = None):
        self.async_session = async_session

    async def list_notifications(
        self,
        user_id: UUID,
        type: list[NotificationType],
        requires_action: bool,
        timeframe: str,
        skip: int,
        limit: int,
    ) -> NotificationList:
        """List notifications for a user."""

        query = (
            select(Notification)
            .where(Notification.user_id == user_id)
            .where(Notification.status == NotificationStatus.UNREAD)
        )

        if type:
            query = query.where(Notification.type.in_(type))

        if requires_action is not None:
            query = query.where(Notification.requires_action == requires_action)

        # Apply timeframe filter if specified
        if timeframe:
            now = datetime.now()
            if timeframe == "today":
                start_date = datetime(now.year, now.month, now.day)
                query = query.where(Notification.created_at >= start_date)
            elif timeframe == "week":
                start_date = now - timedelta(days=7)
                query = query.where(Notification.created_at >= start_date)
            elif timeframe == "month":
                start_date = now - timedelta(days=30)
                query = query.where(Notification.created_at >= start_date)

        # Don't show expired notifications
        query = query.where(
            or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.now(),
            )
        )

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        result = await self.async_session.exec(count_query)
        count = result.first() or 0

        # Get paginated results - only if limit > 0
        if limit > 0:
            query = (
                query.order_by(Notification.created_at.desc()).offset(skip).limit(limit)
            )
            notifications = await self.async_session.exec(query)
            notifications_list = notifications.all()
        else:
            # Return empty list for count-only queries
            notifications_list = []

        return NotificationList(data=notifications_list, count=count)

    async def get(
        self,
        notification_id: UUID,
        current_user: CurrentUser,
    ) -> NotificationResponse:
        """Get a specific notification."""

        notification = await self.async_session.get(Notification, notification_id)

        if not notification:
            raise HTTPException(status_code=404, detail="Notification not found")

        if notification.user_id != current_user.id:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this notification"
            )

        return notification

    async def create(
        self,
        notification_in: NotificationCreate,
    ) -> NotificationResponse:
        """Create a new notification."""
        notification = Notification(**notification_in.model_dump())
        self.async_session.add(notification)
        await self.async_session.commit()
        await self.async_session.refresh(notification)
        return notification

    async def update(
        self,
        notification_id: UUID,
        notification_in: NotificationUpdate,
        current_user: CurrentUser,
    ) -> NotificationResponse:
        """Update a notification."""
        notification = await self.get(notification_id, current_user)
        if not notification:
            raise HTTPException(status_code=404, detail="Notification not found")

        if not current_user.is_superuser and notification.user_id != current_user.id:
            raise HTTPException(
                status_code=403, detail="Not authorized to update this notification"
            )

        update_data = notification_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(notification, field, value)

        notification.updated_at = datetime.now()
        if (
            notification_in.status == NotificationStatus.READ
            and not notification.read_at
        ):
            notification.read_at = datetime.now()

        self.async_session.add(notification)
        await self.async_session.commit()
        await self.async_session.refresh(notification)
        return notification

    async def mark_read(
        self,
        notification_id: UUID,
        current_user: CurrentUser,
    ) -> NotificationResponse:
        """Mark a notification as read."""
        notification = await self.get(notification_id, current_user)
        if not notification:
            raise HTTPException(status_code=404, detail="Notification not found")

        if notification.user_id != current_user.id:
            raise HTTPException(
                status_code=403, detail="Not authorized to update this notification"
            )

        notification.status = NotificationStatus.READ
        notification.read_at = datetime.now()
        notification.updated_at = datetime.now()

        self.async_session.add(notification)
        await self.async_session.commit()
        await self.async_session.refresh(notification)
        return notification

    async def mark_all_read(
        self,
        current_user: CurrentUser,
        type: list[NotificationType],
    ) -> NotificationResponse:
        """Mark all notifications as read."""
        # Create query for unread notifications
        query = select(Notification).where(
            and_(
                Notification.user_id == current_user.id,
                Notification.status == NotificationStatus.UNREAD,
            )
        )

        # Add type filter if specified
        if type:
            query = query.where(Notification.type.in_(type))

        # For large numbers of notifications, process in background
        async def mark_notifications_read():
            notifications = await self.async_session.exec(query)
            notifications_list = notifications.all()
            now = datetime.now()

            for notification in notifications_list:
                notification.status = NotificationStatus.READ
                notification.read_at = now
                notification.updated_at = now
                self.async_session.add(notification)

            await self.async_session.commit()

        # Get count for response
        count_query = select(func.count()).select_from(query.subquery())
        result = await self.async_session.exec(count_query)
        count = result.first()

        # If there are many notifications, process in background
        await mark_notifications_read()
        return {"message": f"Marked {count} notifications as read"}

    def sync_create(self, notification: Notification, session: Session):
        session.add(notification)
        session.commit()
        session.refresh(notification)
        return notification

    def sync_mark_notification_read_given_message_id(
        self, message_id: UUID, session: Session
    ):
        notification = session.exec(
            select(Notification)
            .where(Notification.message_id == message_id)
            .where(Notification.status == NotificationStatus.UNREAD)
        )
        notification = notification.first()

        if not notification:
            raise HTTPException(status_code=404, detail="Notification not found")

        notification.status = NotificationStatus.READ
        notification.read_at = datetime.now()
        notification.updated_at = datetime.now()

        session.add(notification)
        session.commit()
        session.refresh(notification)

        return notification
