from uuid import <PERSON><PERSON><PERSON>

from fastapi import <PERSON><PERSON><PERSON>Exception
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser
from app.models import (
    MessageFeedback,
    MessageFeedbackCreate,
    MessageFeedbackPublic,
    MessageFeedbackUpdate,
)


class MessageFeedbackRepository:
    def __init__(self, async_session: AsyncSession | None = None):
        self.async_session = async_session

    async def get_by_message_id(
        self,
        message_id: UUID,
    ) -> MessageFeedbackPublic | None:
        """Get feedback for a specific message."""
        query = select(MessageFeedback).where(MessageFeedback.message_id == message_id)
        result = await self.async_session.exec(query)
        feedback = result.first()
        return feedback

    async def create(
        self,
        feedback_in: MessageFeedbackCreate,
        current_user: CurrentUser,
    ) -> MessageFeedbackPublic:
        """Create or update feedback for a message."""
        # Check if feedback already exists for this message
        existing_feedback = await self.get_by_message_id(feedback_in.message_id)

        if existing_feedback:
            # Update existing feedback
            feedback_data = feedback_in.model_dump(exclude={"message_id"})
            for field, value in feedback_data.items():
                setattr(existing_feedback, field, value)

            self.async_session.add(existing_feedback)
            await self.async_session.commit()
            await self.async_session.refresh(existing_feedback)
            return existing_feedback
        else:
            # Create new feedback
            feedback_data = feedback_in.model_dump()
            feedback_data["user_id"] = current_user.id

            feedback = MessageFeedback(**feedback_data)
            self.async_session.add(feedback)
            await self.async_session.commit()
            await self.async_session.refresh(feedback)
            return feedback

    async def update(
        self,
        message_id: UUID,
        feedback_in: MessageFeedbackUpdate,
        current_user: CurrentUser,
    ) -> MessageFeedbackPublic:
        """Update feedback for a message."""
        feedback = await self.get_by_message_id(message_id)
        if not feedback:
            raise HTTPException(
                status_code=404, detail="Feedback not found for this message"
            )

        if feedback.user_id != current_user.id:
            raise HTTPException(
                status_code=403, detail="Not authorized to update this feedback"
            )

        update_data = feedback_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(feedback, field, value)

        self.async_session.add(feedback)
        await self.async_session.commit()
        await self.async_session.refresh(feedback)
        return feedback

    async def delete(
        self,
        message_id: UUID,
        current_user: CurrentUser,
    ) -> bool:
        """Delete feedback for a message."""
        feedback = await self.get_by_message_id(message_id)
        if not feedback:
            raise HTTPException(
                status_code=404, detail="Feedback not found for this message"
            )

        if feedback.user_id != current_user.id:
            raise HTTPException(
                status_code=403, detail="Not authorized to delete this feedback"
            )

        await self.async_session.delete(feedback)
        await self.async_session.commit()
        return True
