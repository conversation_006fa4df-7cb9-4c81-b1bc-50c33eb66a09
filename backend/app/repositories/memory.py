from llama_index.core.vector_stores.types import (
    MetadataFilter,
    MetadataFilters,
)


class MemoryRepository:
    @staticmethod
    async def get_memory_by_agent_role(vector_store, agent_role):
        return await vector_store.aget_nodes(
            filters=MetadataFilters(
                filters=[
                    MetadataFilter(
                        key="role",
                        value=agent_role,
                    )
                ]
            )
        )
