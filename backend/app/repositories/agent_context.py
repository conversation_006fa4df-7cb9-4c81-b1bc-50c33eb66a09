from uuid import UUID

from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import (
    Agent<PERSON>ontext,
    AgentContextCreate,
    AgentContextRead,
    AgentContextUpdate,
)


class AgentContextRepository:
    def __init__(
        self, async_session: AsyncSession | None = None, session: Session | None = None
    ):
        self.async_session = async_session
        self.session = session

    def create(self, agent_context_create: AgentContextCreate) -> AgentContext:
        try:
            agent_context = AgentContext(**agent_context_create.model_dump())
            self.session.add(agent_context)
            self.session.commit()
            self.session.refresh(agent_context)
            return agent_context
        except:
            self.session.rollback()
            raise

    def get(self, agent_context_id: UUID) -> AgentContextRead | None:
        try:
            # Get newest agent context
            query = (
                select(AgentContext)
                .where(AgentContext.id == agent_context_id)
                .order_by(AgentContext.created_at.desc())
            )
            result = self.session.exec(query)
            return result.first()
        except:
            self.session.rollback()
            raise

    async def aget(self, agent_context_id: UUID) -> AgentContextRead | None:
        try:
            query = (
                select(AgentContext)
                .where(AgentContext.id == agent_context_id)
                .order_by(AgentContext.created_at.desc())
            )
            result = await self.async_session.exec(query)
            return result.first()
        except:
            self.async_session.rollback()
            raise

    async def aget_by_agent_id(self, agent_id: UUID) -> AgentContextRead | None:
        try:
            query = (
                select(AgentContext)
                .where(AgentContext.agent_id == agent_id)
                .order_by(AgentContext.created_at.desc())
            )
            result = await self.async_session.exec(query)
            return result.first()
        except:
            self.async_session.rollback()
            raise

    async def aget_by_agent_ids(self, agent_ids: list[UUID]) -> list[AgentContextRead]:
        try:
            query = (
                select(AgentContext)
                .where(AgentContext.agent_id.in_(agent_ids))
                .order_by(AgentContext.created_at.desc())
            )
            result = await self.async_session.exec(query)
            return result.all()
        except:
            self.async_session.rollback()
            raise

    def update(
        self,
        agent_context_id: UUID,
        agent_context_update: AgentContextUpdate,
    ) -> AgentContext | None:
        try:
            agent_context = self.get(agent_context_id)
            if not agent_context:
                return None

            if agent_context_update.title is not None:
                agent_context.title = agent_context_update.title
            if agent_context_update.context is not None:
                agent_context.context = agent_context_update.context
            if agent_context_update.is_active is not None:
                agent_context.is_active = agent_context_update.is_active

            self.session.commit()
            self.session.refresh(agent_context)

            return agent_context
        except:
            self.session.rollback()
            raise

    async def aupdate_by_agent_id(
        self,
        agent_id: UUID,
        agent_context_update: AgentContextUpdate,
    ) -> AgentContext | None:
        try:
            agent_context = await self.aget_by_agent_id(agent_id)
            if not agent_context:
                # Create new context if it doesn't exist
                agent_context = AgentContext(
                    agent_id=agent_id,
                    title=agent_context_update.title or "Default Context",
                    context=agent_context_update.context or "",
                    is_active=agent_context_update.is_active
                    if agent_context_update.is_active is not None
                    else True,
                )
                self.async_session.add(agent_context)
            else:
                for key, value in agent_context_update.model_dump().items():
                    if value is not None:
                        setattr(agent_context, key, value)

            await self.async_session.commit()
            await self.async_session.refresh(agent_context)
            return agent_context
        except Exception as e:
            await self.async_session.rollback()
            raise e

    def delete(self, agent_context_id: UUID) -> bool:
        try:
            agent_context = self.get(agent_context_id)
            if not agent_context:
                return False

            agent_context.is_deleted = True
            self.session.commit()
            return True
        except:
            self.session.rollback()
            raise

    def get_by_agent_id(self, agent_id: UUID) -> AgentContextRead | None:
        try:
            query = select(AgentContext).where(
                AgentContext.agent_id == agent_id,
                AgentContext.is_active == True,
                AgentContext.is_deleted == False,
            )
            result = self.session.exec(query)
            return result.first()
        except:
            self.session.rollback()
            raise
