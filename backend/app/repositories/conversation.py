from uuid import UUID

from sqlalchemy import func
from sqlalchemy.orm import selectinload
from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import Agent, Conversation, Message, MessageCheckpoint


class ConversationRepository:
    def __init__(
        self, async_session: AsyncSession | None = None, session: Session | None = None
    ):
        self.async_session = async_session
        self.session = session

    def get_conversation(self, conversation_id: UUID) -> Conversation | None:
        return self.session.get(Conversation, conversation_id)

    async def async_get_conversation(
        self, conversation_id: UUID
    ) -> Conversation | None:
        query = (
            select(Conversation)
            .where(Conversation.id == conversation_id)
            .options(selectinload(Conversation.agent))
        )
        result = await self.async_session.exec(query)
        return result.first()

    def get_conversations(
        self,
        agent_id: UUID | None = None,
        resource_id: UUID | None = None,
        skip: int = 0,
        limit: int = 20,
    ) -> tuple[list[Conversation], int]:
        """Get list of conversations with filtering and pagination.

        Args:
            agent_id: Optional agent ID to filter by
            resource_id: Optional resource ID to filter by
            skip: Number of records to skip for pagination (must be >= 0)
            limit: Maximum number of records to return (must be between 1 and 100)

        Returns:
            Tuple containing:
                - List of conversations matching the filters
                - Total count of conversations before pagination

        Note:
            Results are ordered by created_at in descending order (newest first)
        """
        # Validate pagination parameters
        if skip < 0:
            raise ValueError("skip must be >= 0")
        if not 1 <= limit <= 100:
            raise ValueError("limit must be between 1 and 100")

        # Build base query
        query = select(Conversation).where(~Conversation.is_deleted)

        if agent_id:
            query = query.where(Conversation.agent_id == agent_id)

        if resource_id:
            query = query.where(Conversation.resource_id == resource_id)

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_count = self.session.exec(count_query).first()

        # Get paginated results
        query = query.order_by(Conversation.created_at.desc()).offset(skip).limit(limit)
        conversations = self.session.exec(query).all()

        return conversations, total_count

    def create_conversation(
        self,
        agent_id: UUID,
        model_provider: str | None = "bedrock",
        instructions: str | None = None,
        resource_id: UUID | None = None,
    ) -> Conversation:
        """Create a new conversation."""
        agent = self.session.get(Agent, agent_id)

        conversation = Conversation(
            agent_id=agent_id,
            model_provider=model_provider,
            instructions=instructions or agent.instructions,
            resource_id=resource_id,
        )

        self.session.add(conversation)
        self.session.commit()
        self.session.refresh(conversation)

        return conversation

    def rename_conversation(self, conversation_id: UUID, name: str) -> Conversation:
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError("Conversation not found")

        conversation.name = name
        self.session.add(conversation)
        self.session.commit()
        self.session.refresh(conversation)

        return conversation

    def delete_conversation(self, conversation_id: UUID) -> None:
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError("Conversation not found")

        self.session.delete(conversation)
        self.session.commit()

    def get_conversation_by_share_id(self, share_id: UUID) -> Conversation | None:
        return (
            self.session.query(Conversation)
            .filter(
                Conversation.share_id == share_id,
                Conversation.is_shared == True,
                Conversation.is_deleted == False,
            )
            .first()
        )

    async def get_last_assistant_message(self, conversation_id: UUID) -> Message | None:
        query = (
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .where(Message.is_deleted == False)
            .where(Message.role != "user")
            .order_by(Message.created_at.desc())
            .limit(1)
        )
        result = await self.async_session.exec(query)
        return result.first()

    async def get_checkpoint_by_message_id(
        self, message_id: UUID
    ) -> MessageCheckpoint | None:
        query = select(MessageCheckpoint).where(
            MessageCheckpoint.message_id == message_id
        )
        result = await self.async_session.exec(query)
        return result.first()

    async def get_latest_user_message(self, conversation_id: UUID) -> Message | None:
        query = (
            select(Message)
            .where(Message.is_deleted == False)
            .where(Message.conversation_id == conversation_id)
            .where(Message.role == "user")
            .order_by(Message.created_at.desc())
            .limit(1)
        )
        result = await self.async_session.exec(query)
        return result.first()
