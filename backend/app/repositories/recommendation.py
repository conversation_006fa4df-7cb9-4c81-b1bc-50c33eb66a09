from uuid import UUID

from sqlalchemy.orm import Session
from sqlmodel import delete, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import Recommendation, RecommendationsPublic


class RecommendationRepository:
    def __init__(self, async_session: AsyncSession | None = None):
        self.async_session = async_session

    def get_all_recommendations(
        self, resource_id: UUID, session: Session
    ) -> RecommendationsPublic:
        query = select(Recommendation).where(Recommendation.resource_id == resource_id)
        result = session.exec(query)
        rcms = result.all()
        return RecommendationsPublic(data=rcms, count=len(rcms))

    async def delete_recommendations(
        self,
        ids: list[UUID],
        resource_id: UUID,
    ) -> None:
        query = delete(Recommendation).where(
            Recommendation.id.in_(ids),
            Recommendation.resource_id == resource_id,
        )
        await self.async_session.exec(query)
        await self.async_session.commit()

    async def create_recommendation(
        self,
        new_recommendations: list[dict],
    ) -> None:
        recommendations = [
            Recommendation.model_validate(recommendation)
            for recommendation in new_recommendations
        ]
        self.async_session.add_all(recommendations)
        await self.async_session.commit()
