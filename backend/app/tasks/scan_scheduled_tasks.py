import logging
from enum import Enum
from uuid import UUID

from pydantic import BaseModel
from sqlmodel import Session

from app.core.celery_app import LOW_PRIORITY, celery_app
from app.core.db import engine
from app.core.exceptions import ScanningError
from app.models import Workspace
from app.modules.resource_crawlers.crawler_factory import CrawlerFactory, CrawlerType
from app.repositories.workspaces import WorkspaceRepository

logger = logging.getLogger(__name__)


class AWSService(str, Enum):
    """Supported AWS services for scanning"""

    # Compute Services
    EC2 = "EC2"
    ECS = "ECS"
    EKS = "EKS"
    LAMBDA = "LAMBDA"
    BATCH = "BATCH"
    EC2_AUTO_SCALING = "EC2_AUTO_SCALING"
    ELASTIC_BEANSTALK = "ELASTIC_BEANSTALK"
    APP_RUNNER = "APP_RUNNER"

    # Storage Services
    S3 = "S3"
    EBS = "EBS"
    EFS = "EFS"
    BACKUP = "BACKUP"

    # Database Services
    RDS = "RDS"
    DYNAMODB = "DYNAMODB"
    ELASTICACHE = "ELASTICACHE"
    NEPTUNE = "NEPTUNE"
    DOCUMENTDB = "DOCUMENTDB"
    OPENSEARCH = "OPENSEARCH"
    REDSHIFT = "REDSHIFT"

    # Networking & Content Delivery
    VPC = "VPC"
    ELB = "ELB"

    # Management & Governance
    CLOUDFORMATION = "CLOUDFORMATION"

    # Monitoring & Logging
    CLOUDWATCH = "CLOUDWATCH"
    SQS = "SQS"
    SNS = "SNS"


class ScanningContext(BaseModel):
    """Validation model for scanning parameters"""

    workspace_id: UUID
    aws_account_id: str
    aws_access_key_id: str
    aws_secret_access_key: str
    regions: list[str]
    services: set[AWSService]


class ResourceScanner:
    """Unified resource scanner for AWS services"""

    def __init__(self, session: Session):
        self.session = session
        self.workspace_repo = WorkspaceRepository(session)

    def _validate_workspace(self, workspace: Workspace) -> ScanningContext | None:
        """Validate workspace configuration and return scanning context"""
        try:
            if not workspace.aws_account or not workspace.aws_account.account_id:
                raise ScanningError("Missing AWS account configuration")

            credential = self.workspace_repo.get_aws_credentials(
                workspace.aws_account.id
            )
            if not credential:
                raise ScanningError("Missing AWS credentials")

            if not workspace.settings.regions:
                raise ScanningError("No regions configured")

            if not workspace.settings.types:
                raise ScanningError("No services configured for scanning")

            return ScanningContext(
                workspace_id=workspace.id,
                aws_account_id=workspace.aws_account.account_id,
                aws_access_key_id=credential.access_key_id,
                aws_secret_access_key=credential.secret_access_key,
                regions=workspace.settings.regions,
                services=set(workspace.settings.types),
            )
        except Exception as e:
            logger.error(
                f"Unexpected error validating workspace {workspace.id}: {str(e)}"
            )
            return None

    def _scan_service_in_region(
        self, context: ScanningContext, service: AWSService, region: str
    ):
        """Scan specific service resources in a region"""
        try:
            crawler_type = CrawlerType[service.upper()]
            crawler = CrawlerFactory.get_crawler(
                workspace_id=context.workspace_id,
                scan_type=crawler_type,
                aws_account_id=context.aws_account_id,
                aws_access_account_id=context.aws_access_key_id,
                aws_access_key_id=context.aws_access_key_id,
                aws_secret_access_key=context.aws_secret_access_key,
                region=region,
            )

            # Create a fresh session for each crawling operation to avoid stale references
            with Session(engine) as fresh_session:
                crawler.crawl_resources_and_metrics(fresh_session)

        except Exception as e:
            logger.error(f"Failed to scan {service} resources in {region}: {str(e)}")
            raise ScanningError(f"Scanning failed for {service} in {region}")

    def scan_resources(self, selected_workspaces: list[UUID] = None):
        """Scan configured AWS resources across all workspaces"""
        workspaces = self.workspace_repo.get_all_workspaces(selected_workspaces)

        for workspace in workspaces:
            logger.info(f"Starting resource scan for workspace {workspace.id}")

            context = self._validate_workspace(workspace)
            if not context:
                continue

            for service in context.services:
                logger.info(
                    f"Scanning {service} resources for workspace {workspace.id}"
                )
                for region in context.regions:
                    try:
                        # Refresh session before each service-region scan to avoid stale references
                        self.session.expire_all()
                        self._scan_service_in_region(context, service, region)
                    except ScanningError as e:
                        logger.error(str(e))
                        # Continue with the next region despite errors
                        continue

            logger.info(f"Completed resource scan for workspace {workspace.id}")


@celery_app.task(name="scan_scheduled_tasks.scan_aws_resources", priority=LOW_PRIORITY)
def scan_aws_resources():
    """Consolidated Celery task for AWS resource scanning"""
    logger.info("Starting AWS resource scan")
    try:
        with Session(engine) as session:
            scanner = ResourceScanner(session)
            scanner.scan_resources()
        logger.info("Completed AWS resource scan")
    except Exception as e:
        logger.error(f"AWS resource scanning failed: {str(e)}")
        raise


@celery_app.task(
    name="scan_scheduled_tasks.scan_aws_resources_by_workspace", priority=LOW_PRIORITY
)
def scan_aws_resources_by_workspace(workspace_id: UUID):
    """Consolidated Celery task for AWS resource scanning"""
    logger.info("Starting AWS resource scan")
    try:
        with Session(engine) as session:
            scanner = ResourceScanner(session)
            scanner.scan_resources(selected_workspaces=[workspace_id])
        logger.info("Completed AWS resource scan")
    except Exception as e:
        logger.error(f"AWS resource scanning failed: {str(e)}")
        raise
