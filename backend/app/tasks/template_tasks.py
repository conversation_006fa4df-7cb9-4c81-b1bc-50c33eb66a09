import csv
import logging
from io import <PERSON><PERSON>
from typing import Any
from uuid import UUID
from pathlib import Path

import celery
from sqlmodel import Session

from app.core.celery_app import NORMAL_PRIORITY, celery_app
from app.core.db import engine
from app.core.exceptions import TaskOperationError
from app.models import TaskTemplate, TaskCategoryEnum, TaskServiceEnum, RunModeEnum, TaskPriority, TaskImpactEnum, TaskCouldEnum
from app.schemas.task_template import TaskTemplateCreate
from app.services.seed_service import SeedService

logger = logging.getLogger(__name__)

# Version of the default task templates
DEFAULT_TEMPLATES_VERSION = "1.0.0"

@celery_app.task(
    name="template_tasks.import_from_csv",
    bind=True,
    priority=NORMAL_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
def import_templates_from_csv(
    self: celery.Task,
    csv_content: str,
    workspace_id: UUID | None = None,
    is_default: bool = True,
    version: str = DEFAULT_TEMPLATES_VERSION,
) -> dict[str, Any]:
    """
    Process a CSV file to create task templates.
    
    Expected CSV format:
    task,category,service,service_name,cloud,run_mode,schedule,context,priority,impact
    
    Args:
        csv_content: String content of the CSV file
        workspace_id: Optional workspace ID to associate templates with
        is_default: Whether these are default templates
        version: Version identifier for the seed data
    
    Returns:
        dict containing import results
    """
    self.update_state(
        state="PROGRESS",
        meta={
            "progress": 0,
            "message": "Starting template import",
        },
    )

    try:
        # Parse CSV content
        csv_file = StringIO(csv_content)
        reader = csv.DictReader(csv_file)
        rows = list(reader)  # Convert to list to get length and allow iteration
        
        # Map CSV headers to required fields
        header_mapping = {
            "Task": "task",
            "Category": "category",
            "Service": "service",
            "Description": "context",  # Use Description as context
            "Cloud": "cloud",
            "Service Name": "service_name",
            "Priority": "priority",
            "Impact": "impact"
        }
        
        # Validate required headers
        required_headers = {"task", "category", "service", "context"}
        fieldnames = set(reader.fieldnames or [])
        mapped_fieldnames = {header_mapping.get(h, h) for h in fieldnames}
        
        if not required_headers.issubset(mapped_fieldnames):
            raise TaskOperationError(
                f"CSV missing required headers: {required_headers - mapped_fieldnames}"
            )

        # Process templates
        templates_created = 0
        templates_failed = 0
        errors = []
        templates_data = []

        with Session(engine) as session:
            seed_service = SeedService(session)
            
            # Process all rows to collect template data
            for i, row in enumerate(rows):
                try:
                    # Update progress
                    progress = int((i / len(rows)) * 100)
                    self.update_state(
                        state="PROGRESS",
                        meta={
                            "progress": progress,
                            "message": f"Processing template {i+1}/{len(rows)}",
                        },
                    )

                    # Map row data using header mapping
                    mapped_row = {header_mapping.get(k, k): v for k, v in row.items()}
                    
                    # Skip empty rows
                    if not mapped_row.get("task") or not mapped_row.get("category"):
                        continue
                    
                    # Map numeric priority/impact to enum values
                    priority_map = {
                        "1": TaskPriority.LOW,
                        "2": TaskPriority.MEDIUM,
                        "3": TaskPriority.HIGH,
                        "4": TaskPriority.CRITICAL
                    }
                    impact_map = {
                        "1": TaskImpactEnum.LOW,
                        "2": TaskImpactEnum.MEDIUM,
                        "3": TaskImpactEnum.HIGH,
                        "4": TaskImpactEnum.CRITICAL
                    }
                    
                    # Map category values
                    category_map = {
                        "OPERATIONAL_EFFICIENCY": TaskCategoryEnum.OPERATIONAL_EFFICIENCY,
                        "COST_OPTIMIZE": TaskCategoryEnum.COST_OPTIMIZE,
                        "SCALABILITY": TaskCategoryEnum.SCALABILITY,
                        "SECURITY": TaskCategoryEnum.SECURITY,
                        "PERFORMANCE": TaskCategoryEnum.OPERATIONAL,  # Map PERFORMANCE to OPERATIONAL
                        "OTHER": TaskCategoryEnum.OTHER
                    }
                    
                    # Map service values
                    service_map = {
                        "COMPUTE": TaskServiceEnum.COMPUTE,
                        "STORAGE": TaskServiceEnum.STORAGE,
                        "SERVERLESS": TaskServiceEnum.SERVERLESS,
                        "DATABASE": TaskServiceEnum.DATABASE,
                        "NETWORK": TaskServiceEnum.NETWORK,
                        "MESSAGING": TaskServiceEnum.MESSAGING,
                        "MANAGEMENT": TaskServiceEnum.MANAGEMENT,
                        "BILLING": TaskServiceEnum.BILLING,
                        "CROSS_SERVICE": TaskServiceEnum.CROSS_SERVICE,
                        "MONITORING": TaskServiceEnum.MONITORING,
                        "STREAMING": TaskServiceEnum.STREAMING,
                        "SECURITY": TaskServiceEnum.SECURITY,
                        "ALL": TaskServiceEnum.ALL,
                        "OTHER": TaskServiceEnum.OTHER
                    }
                    
                    # Map cloud values
                    cloud_map = {
                        "AWS": TaskCouldEnum.AWS,
                        "AZURE": TaskCouldEnum.AZURE,
                        "GCP": TaskCouldEnum.GCP,
                        "ALL": TaskCouldEnum.ALL
                    }
                    
                    # Create template data
                    template_data = TaskTemplateCreate(
                        task=mapped_row["task"].strip(),
                        category=category_map.get(mapped_row["category"].upper(), TaskCategoryEnum.OTHER),
                        service=service_map.get(mapped_row["service"].upper(), TaskServiceEnum.OTHER),
                        service_name=mapped_row.get("service_name", "").strip(),
                        cloud=cloud_map.get(mapped_row.get("cloud", "AWS").upper(), TaskCouldEnum.AWS),
                        run_mode=RunModeEnum.AUTONOMOUS,  # Use the enum value directly
                        schedule=None,  # No schedule in CSV
                        context=mapped_row["context"].strip(),
                        priority=priority_map.get(mapped_row.get("priority", "3"), TaskPriority.HIGH),
                        impact=impact_map.get(mapped_row.get("impact", "3"), TaskImpactEnum.HIGH),
                    )

                    # Convert the template data to dict and ensure run_mode is lowercase
                    template_dict = template_data.dict()
                    template_dict['run_mode'] = template_dict['run_mode'].value.lower()
                    templates_data.append(template_dict)

                except Exception as e:
                    templates_failed += 1
                    errors.append(f"Row {i+1}: {str(e)}")
                    logger.error(f"Error processing row {i+1}: {str(e)}")
                    continue

            # Check if we need to apply this seed data
            seed_type = "default_templates" if is_default else f"workspace_templates_{workspace_id}"
            if not seed_service.is_seed_needed(seed_type, templates_data, version):
                logger.info(f"Seed data version {version} already applied for {seed_type}")
                return {
                    "task_id": self.request.id,
                    "status": "SKIPPED",
                    "result": {
                        "message": f"Seed data version {version} already applied",
                        "templates_created": 0,
                        "templates_failed": 0,
                        "errors": [],
                    },
                }

            # Apply the seed data
            for template_dict in templates_data:
                try:
                    template = TaskTemplate(
                        **template_dict,
                        workspace_id=None if is_default else workspace_id,
                        is_default=is_default,
                    )
                    session.add(template)
                    templates_created += 1
                except Exception as e:
                    templates_failed += 1
                    errors.append(f"Template creation failed: {str(e)}")
                    logger.error(f"Error creating template: {str(e)}")
                    continue

            try:
                session.commit()
                # Record the seed version
                seed_service.record_seed_version(
                    seed_type=seed_type,
                    version=version,
                    data=templates_data,
                    description=f"Task templates for {'default' if is_default else f'workspace {workspace_id}'}"
                )
            except Exception as e:
                session.rollback()
                raise TaskOperationError(f"Failed to commit templates: {str(e)}")

        return {
            "task_id": self.request.id,
            "status": "COMPLETED",
            "result": {
                "templates_created": templates_created,
                "templates_failed": templates_failed,
                "errors": errors,
            },
        }

    except Exception as e:
        logger.error(f"Error in import_templates_from_csv: {str(e)}")
        self.update_state(
            state="FAILURE",
            meta={
                "progress": 0,
                "status": "FAILED",
                "error": str(e),
            },
        )
        raise

@celery_app.task(
    name="app.tasks.template_tasks.import_default_templates_on_startup",
    priority=NORMAL_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
def import_default_templates_on_startup() -> dict[str, Any]:
    """
    Import default task templates on system startup.
    This task is meant to run once when the system starts.
    
    Returns:
        dict containing import results
    """
    logger.info("Starting import of default templates on startup")
    
    try:
        # Get the path to the default templates CSV file
        csv_path = Path(__file__).parent.parent.parent / "data" / "default_task_templates.csv"
        
        if not csv_path.exists():
            logger.error(f"Default templates CSV file not found at {csv_path}")
            return {"status": "ERROR", "error": "Default templates CSV file not found"}
        
        # Read CSV content
        with open(csv_path, "r") as f:
            csv_content = f.read()
        
        # Call the import task using delay and return immediately
        # The task will be processed asynchronously
        task = import_templates_from_csv.delay(
            csv_content=csv_content,
            workspace_id=None,  # None for default templates
            is_default=True,    # Mark as default templates
        )
        
        logger.info("Successfully initiated default templates import")
        return {
            "status": "STARTED",
            "task_id": task.id,
            "message": "Template import task has been queued"
        }
        
    except Exception as e:
        logger.error(f"Error importing default templates on startup: {str(e)}")
        raise 