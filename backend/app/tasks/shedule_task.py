import async<PERSON>
from logging import get<PERSON>ogger
from uuid import <PERSON><PERSON><PERSON>

from sqlmodel import select

from app.api.deps import get_async_session
from app.core.celery_app import HIGH_PRIORITY, celery_app
from app.models import Agent, AgentType
from app.schemas.task import TaskCreate
from app.services.task_service import TaskService

logger = getLogger(__name__)


@celery_app.task(
    bind=True,
    max_retries=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    name="schedule_task.create_task",
    priority=HIGH_PRIORITY,
)
def create_task(
    self,
    title: str,
    description: str,
    schedule: str,
    workspace_id: UUID,
    user_id: str,
) -> None:
    """
    Create a task asynchronously.

    Args:
        title: Title of the task
        description: Description of the task
        schedule: Schedule for the task in cron expression format (e.g. "0 0 * * *" for daily at midnight)
        workspace_id: Workspace ID
        user_id: User ID

    Raises:
        self.retry: On recoverable errors
        Exception: On critical errors
    """
    try:
        # Create async context and run
        async def process_task():
            # Use the async session dependency properly as a context manager
            async_session_gen = get_async_session()
            async_session = await async_session_gen.__anext__()

            try:
                statement = select(Agent).where(
                    Agent.workspace_id == workspace_id,
                    Agent.type == AgentType.AUTONOMOUS_AGENT,
                )
                result = await async_session.exec(statement)
                agent = result.first()

                if not agent:
                    logger.error(
                        f"No autonomous agent found for workspace {workspace_id}"
                    )
                    raise ValueError(
                        f"No autonomous agent found for workspace {workspace_id}"
                    )

                task_service = TaskService(async_session)

                await task_service.create_task(
                    workspace_id=workspace_id,
                    user_id=user_id,
                    data=TaskCreate(
                        title=title,
                        description=description,
                        schedule=schedule,
                        agent_config={
                            "agent_id": agent.id,
                            "message": description,
                            "conversation_name": title,
                        },
                    ),
                )

                logger.info(
                    f"Successfully created task: {title} for workspace {workspace_id}"
                )

            except Exception as e:
                logger.error(
                    f"Failed to create task: {str(e)}, "
                    f"title={title}, "
                    f"workspace_id={workspace_id}"
                )
                raise
            finally:
                # Properly close the async session
                try:
                    await async_session_gen.aclose()
                except Exception:
                    pass

        # Run async code in sync context
        asyncio.run(process_task())

    except ValueError as exc:
        # Invalid UUID or validation errors - don't retry
        logger.error(f"Validation error: {str(exc)}")
        raise

    except Exception as exc:
        # Other unexpected errors - retry with backoff
        logger.error(
            f"Error creating task: {str(exc)}, "
            f"attempt {self.request.retries + 1} of {self.max_retries + 1}"
        )
        raise self.retry(exc=exc)
