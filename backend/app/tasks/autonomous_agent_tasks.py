import asyncio
import os
import uuid
from datetime import UTC, datetime, timedelta
from functools import wraps
from http.client import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from logging import get<PERSON>ogger
from typing import Any

import nest_asyncio
from celery.signals import worker_process_init
from croniter import croniter
from pydantic import BaseModel
from sqlmodel import Enum, Session, select

from app.core.celery_app import HIGH_PRIORITY, celery_app
from app.core.config import settings
from app.core.db import engine
from app.models import (
    Agent,
    Conversation,
    Task,
    TaskExecutionStatus,
    TaskHistory,
    TaskScheduledStatus,
)
from app.services.agent import AutonomousAgentService

logger = getLogger(__name__)

# Apply nest_asyncio to handle nested event loops
nest_asyncio.apply()


def async_to_sync(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(f(*args, **kwargs))

    return wrapper


@worker_process_init.connect
def initialize_worker_agent_factory(**kwargs):
    """Initialize AgentFactory once per worker process"""

    logger.info(f"Worker {os.getpid()} starting - initializing AgentFactory")

    @async_to_sync
    async def initialize_agent_factory():
        from app.modules.multi_agents.core.agents.factory import AgentFactory

        return await AgentFactory.initialize()

    initialize_agent_factory()
    logger.info(f"AgentFactory initialized for worker {os.getpid()}")


class TaskSchedulingError(Exception):
    """Custom exception for task scheduling errors"""

    pass


def ensure_utc(dt: datetime) -> datetime:
    """Ensure datetime is UTC timezone-aware"""
    if dt.tzinfo is None:
        return dt.replace(tzinfo=UTC)
    return dt.astimezone(UTC)


def get_automated_tasks(session: Session) -> list[Task]:
    """Fetch automated tasks where next_run is within the scheduled interval"""
    now = datetime.now(UTC)
    window_end = now + timedelta(minutes=settings.TASK_SCHEDULE_WINDOW_MINUTES)
    logger.info(f"Fetching automated tasks with next_run <= {window_end}")
    query = (
        select(Task)
        .where(
            ~Task.is_deleted,
            Task.enable,
            Task.scheduled_status == TaskScheduledStatus.PENDING,
            Task.next_run.is_not(None),
            Task.next_run <= window_end,
        )
        .order_by(Task.next_run.asc().nulls_first())
    )
    tasks = session.exec(query).all()
    logger.info(f"Found {len(tasks)} automated tasks")
    return tasks


# def calculate_next_run(task: Task, base_time: datetime) -> tuple[datetime, int] | None:
#     """Calculate next run time and countdown for a task"""
#     logger.info(f"Calculating next run for task {task.id}")
#     logger.info(f"Base time: {base_time}, Task schedule: {task.schedule}")

#     base_time = ensure_utc(base_time)
#     logger.info(f"Base time in UTC: {base_time}")

#     if task.execution_status == ExecutionStatus.RUNNING:
#         logger.info(f"Task {task.id} is currently running, skipping")
#         return None

#     if task.execution_status == ExecutionStatus.SCHEDULED:
#         logger.info(f"Task {task.id} is scheduled, skipping")
#         return None

#     last_run = ensure_utc(task.last_run) if task.last_run else base_time
#     logger.info(f"Last run time: {last_run}")

#     cron = croniter(task.schedule, last_run)
#     next_run = ensure_utc(cron.get_next(datetime))
#     logger.info(f"Initial next run calculation: {next_run}")

#     if task.last_run:
#         logger.info("Task has previous run, checking for more recent next run time")
#         iterations = 0
#         while next_run <= ensure_utc(task.last_run):
#             next_run = ensure_utc(cron.get_next(datetime))
#             iterations += 1
#             logger.info(f"Iteration {iterations}: adjusted next run to {next_run}")

#     if next_run < base_time:
#         logger.info(f"Next run {next_run} is before base time {base_time}, skipping")
#         return None

#     countdown = int((next_run - base_time).total_seconds())

#     logger.info(
#         f"Next run calculated for task {task.id}:"
#         f"\n  - Next run time: {next_run}"
#         f"\n  - Countdown: {countdown} seconds"
#         f"\n  - Schedule: {task.schedule}"
#         f"\n  - Last run: {task.last_run}"
#     )
#     return next_run, countdown


# def validate_task_schedule(
#     task: Task, next_run: datetime, countdown: int, schedule_window: datetime
# ) -> bool:
#     """Validate if task should be scheduled based on timing and current state"""
#     logger.info(f"Validating schedule for task {task.id}")

#     next_run = ensure_utc(next_run)
#     schedule_window = ensure_utc(schedule_window)
#     current_time = datetime.now(UTC)

#     # Check if task is already scheduled
#     if task.scheduled_at:
#         task_scheduled_at = ensure_utc(task.scheduled_at)
#         schedule_window_limit = task_scheduled_at + timedelta(
#             seconds=TASK_SCHEDULE_WINDOW_SECONDS
#         )

#         if schedule_window_limit > current_time:
#             logger.info(f"Task {task.id} is already scheduled, skipping")
#             return False

#     is_valid = (
#         next_run <= schedule_window
#         and countdown < TASK_SCHEDULE_WINDOW_SECONDS
#         and (
#             not task.last_run
#             or not task.next_run
#             or next_run != ensure_utc(task.next_run)
#         )
#     )

#     logger.info(f"Schedule validation result for task {task.id}: {is_valid}")
#     return is_valid


# def update_task_metadata(session: Session, task: Task, next_run: datetime) -> None:
#     """Update task scheduling metadata"""
#     logger.info(f"Updating metadata for task {task.id}")

#     task.next_run = ensure_utc(next_run)
#     session.add(task)
#     session.commit()

#     logger.info(f"Metadata updated for task {task.id}")


def schedule_task(session: Session, task: Task) -> tuple[bool, int]:
    """Schedule task"""

    # Double check if task is already scheduled
    session.refresh(task)
    if task.scheduled_status == TaskScheduledStatus.SCHEDULED:
        logger.info(f"Task {task.id} is already scheduled, skipping")
        return False, 0

    countdown = int((ensure_utc(task.next_run) - datetime.now(UTC)).total_seconds())
    logger.info(f"Scheduling task {task.id} with countdown {countdown}")

    async_result = autonomous_agent_task_handler.s(str(task.id)).apply_async(
        countdown=countdown
    )

    task.celery_task_id = async_result.id
    task.scheduled_status = TaskScheduledStatus.SCHEDULED
    session.add(task)
    session.commit()

    logger.info(f"Task {task.id} scheduled successfully")
    return True, countdown


def log_scheduling_result(task: Task, next_run: datetime, countdown: int) -> None:
    """Log task scheduling details"""
    next_run = ensure_utc(next_run)
    last_run = ensure_utc(task.last_run) if task.last_run else None

    logger.info(
        f"Scheduled task {task.id} to run in {countdown} seconds at "
        f"{next_run.isoformat()} (last run: "
        f"{last_run.isoformat() if last_run else 'never'})"
    )


class AgentExecutionConfig(BaseModel):
    """Configuration for agent execution"""

    agent_id: uuid.UUID
    model_provider: str = "bedrock"
    context_ids: list[uuid.UUID] = []
    message: str
    recursion_limit: int = 100
    conversation_name: str | None = None


class AgentExecutionResult(BaseModel):
    """Result of agent execution"""

    conversation_id: uuid.UUID
    message_result: dict[str, Any]
    status: str = "success"  # success, required_approval, error
    message: str | None = None


async def execute_agent_task(
    session: Session, task: Task, config: AgentExecutionConfig
) -> AgentExecutionResult:
    """Execute the autonomous agent task"""
    logger.info(f"Starting execution of task {task.id}")

    conversation = None
    try:
        from celery import current_task

        # Get agent from database
        agent = session.get(Agent, config.agent_id)
        if not agent:
            logger.error(f"Agent {config.agent_id} not found")
            raise ValueError(f"Agent {config.agent_id} not found")

        autonomous_agent_service = AutonomousAgentService(session)

        conversation = Conversation(
            agent_id=config.agent_id,
            name=config.conversation_name or f"Task {task.id} Execution",
            model_provider=config.model_provider,
        )

        task_history = TaskHistory(
            task_id=task.id,
            conversation_id=conversation.id,
            status=TaskExecutionStatus.RUNNING,
            celery_task_id=current_task.request.id,
            start_time=datetime.now(UTC),
        )

        session.add(conversation)
        session.add(task_history)
        session.commit()

        logger.info(f"Task conversation created: {task_history.id}")
        logger.info(f"Task conversation celery task id: {task_history.celery_task_id}")
        logger.info(f"Processing message for conversation {conversation.id}")

        try:
            message_result = await autonomous_agent_service.process_message(
                conversation_id=conversation.id,
                message_content=config.message,
                message_action_type=None,
                resume=False,
                approve=False,
                restore=False,
                restore_message_id=None,
                user_id=task.owner_id,
            )
        except Exception as e:
            logger.error(f"Task {task.id} execution failed: {e}")
            task_history.status = TaskExecutionStatus.FAILED
            task_history.message = str(e)
            task_history.end_time = datetime.now(UTC)
            task_history.run_time = int(
                (
                    task_history.end_time - ensure_utc(task_history.start_time)
                ).total_seconds()
            )
            session.add(task_history)
            session.commit()
            return AgentExecutionResult(
                conversation_id=conversation.id,
                message_result={},
                status="error",
                message=str(e),
            )

        if message_result.get("type") == "interrupt":
            task_history.status = TaskExecutionStatus.REQUIRED_APPROVAL
            task_history.message = message_result.get("content")
        else:
            task_history.status = TaskExecutionStatus.SUCCEEDED

        task_history.end_time = datetime.now(UTC)
        task_history.run_time = int(
            (
                task_history.end_time - ensure_utc(task_history.start_time)
            ).total_seconds()
        )
        session.add(task_history)
        session.commit()

        if message_result.get("type") == "interrupt":
            return AgentExecutionResult(
                conversation_id=conversation.id,
                message_result=message_result,
                status="required_approval",
                message=message_result.get("content"),
            )
        else:
            return AgentExecutionResult(
                conversation_id=conversation.id,
                message_result=message_result,
                status="success",
                message=None,
            )
    except Exception as e:
        logger.error(f"Task {task.id} execution failed: {e}")
        conversation_id = conversation.id if conversation else uuid.uuid4()
        return AgentExecutionResult(
            conversation_id=conversation_id,
            message_result={},
            status="error",
            message=str(e),
        )


class TaskChangeType(str, Enum):
    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    STATUS_CHANGE = "status_change"
    EXECUTION_CHANGE = "execution_change"


@celery_app.task(
    name="autonomous_agent_tasks.handler", priority=HIGH_PRIORITY, bind=True
)
def autonomous_agent_task_handler(self, task_id: str):
    """Handle execution of autonomous agent task"""
    logger.info(f"Starting autonomous agent task handler for task {task_id}")

    with Session(engine) as session:
        task = session.get(Task, task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            raise HTTPException(status_code=404, detail="Task not found")

        if not task.enable:
            logger.info(f"Task {task_id} is disabled, skipping")
            return

        # Update task status to in progress
        task.execution_status = TaskExecutionStatus.RUNNING
        session.add(task)
        session.commit()

        execution_config = AgentExecutionConfig(**task.agent_config)

        # Use the decorator to properly handle async execution
        @async_to_sync
        async def execute_task() -> AgentExecutionResult:
            return await execute_agent_task(session, task, execution_config)

        result: AgentExecutionResult = execute_task()

        # Update task status and error message
        task.last_run = datetime.now(UTC)
        task.scheduled_status = TaskScheduledStatus.PENDING

        if result.status == "error":
            task.execution_status = TaskExecutionStatus.FAILED
            task.message = result.message
            logger.error(f"Task {task_id} execution failed: {result.message}")
        elif result.status == "required_approval":
            task.execution_status = TaskExecutionStatus.REQUIRED_APPROVAL
            task.message = result.message
            logger.info(f"Task {task_id} execution required approval: {result.message}")
        else:
            task.execution_status = TaskExecutionStatus.SUCCEEDED
            task.message = None
            logger.info(f"Task {task_id} completed successfully")

        # Calculate next run time
        cron = croniter(task.schedule, ensure_utc(task.last_run))
        next_run = ensure_utc(cron.get_next(datetime))
        task.next_run = next_run

        session.add(task)
        session.commit()


@celery_app.task(name="autonomous_agent_tasks.publisher", priority=HIGH_PRIORITY)
def autonomous_agent_task_publish(**kwargs):
    """Publishes automated tasks based on their cron schedules"""
    logger.info("Starting autonomous agent task publisher")
    metrics = {"total": 0, "scheduled": 0}

    with Session(engine) as session:
        tasks = get_automated_tasks(session)

        logger.info(f"Found {len(tasks)} tasks to process")
        for task in tasks:
            metrics["total"] += 1
            logger.info(f"Processing task {task.id}")

            scheduled, countdown = schedule_task(session, task)
            if scheduled:
                log_scheduling_result(task, task.next_run, countdown)

            metrics["scheduled"] += 1

    logger.info(f"Task publisher completed. Metrics: {metrics}")
