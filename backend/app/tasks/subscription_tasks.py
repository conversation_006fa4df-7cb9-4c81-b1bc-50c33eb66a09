import json
import logging
from uuid import UUID
import asyncio

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import get_async_session
from app.core.celery_app import celery_app, HIGH_PRIORITY
from app.services.token_usage_service import TokenUsageService

logger = logging.getLogger(__name__)

async def _reset_user_quota_async(user_id: UUID):
    """Async helper function to reset user quota"""
    async for session in get_async_session():
        try:
            token_service = TokenUsageService(session)
            await token_service._reset_user_quota(user_id)
            logger.info(f"Successfully reset quota for user {user_id}")
            break
        finally:
            await session.close()

@celery_app.task(name="app.tasks.notifications.subscription_success", priority=HIGH_PRIORITY)
def handle_subscription_success(message: str):
    """Handle subscription success message and reset user quota"""
    logger.info("Received subscription success task")
    logger.info(f"Task message: {message}")
    
    try:
        # Parse the message if it's string
        if isinstance(message, str):
            message = json.loads(message)
            logger.info(f"Parsed message: {message}")
                        
        user_id = UUID(message["user_id"])
        subscription_id = UUID(message["subscription_id"])
        status = message["status"]
        
        logger.info(f"Processing subscription success for user {user_id}, subscription {subscription_id}, status {status}")
        
        if status == "active":
            # Run the async function in a synchronous context
            asyncio.run(_reset_user_quota_async(user_id))
        else:
            logger.info(f"Subscription status is not active ({status}), skipping quota reset")
                    
    except Exception as e:
        logger.error(f"Error handling subscription success: {str(e)}", exc_info=True)
        raise 