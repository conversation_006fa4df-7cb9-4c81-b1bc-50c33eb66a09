from typing import Annotated, Any, Literal

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool


@tool
def group_chat(
    message: str,
    target_member: Literal["Customer"] | str,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> dict[str, Any]:
    """
    Send a message to the group chat with a specific target recipient.

    This tool enables team communication by allowing messages to be directed to either team members
    or the customer. When targeting the customer, the message should only be used to confirm task
    completion or request necessary resources.

    Tips:
        - YOU MUST mention the target_member in the message with @target_member
        - ONLY mention one target_member at a time
        - When target_member is "Customer", the message should only be used for:
            * Confirming task completion
            * Requesting additional resources needed for the task
        - Always communicate blockers or challenges clearly

    Args:
        message: The message content to send to the group chat.
        target_member: The recipient of the message. Can be either:
            - "Customer": For confirming task completion or requesting resources
            - team member name: For team collaboration and task coordination

    Returns:
        dict: A dictionary containing the status of the message delivery.

    """
    return {
        "status": "success",
        "message": f"Successfully sent message to the group chat with mentioning {target_member}.",
    }
