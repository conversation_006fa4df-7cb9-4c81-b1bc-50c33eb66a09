import uuid
from typing import Annotated, Any

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool
from sqlmodel import Session

from app.core.db import engine
from app.models import Agent<PERSON>ontext<PERSON><PERSON>, AgentContextRead
from app.repositories.agent_context import AgentContextRepository


async def get_agent_context(agent_id: uuid.UUID) -> AgentContextRead:
    """
    Retrieve the context for a specific agent.

    Args:
        agent_id (uuid.UUID): The unique identifier of the agent.

    Returns:
        AgentContextRead: The context associated with the agent,
        or "No context found" if no context exists.
    """
    with Session(engine) as session:
        agent_context_repository = AgentContextRepository(session)
        agent_context = await agent_context_repository.get_by_agent_id(agent_id)

        if agent_context is None:
            return "No context found"

        return agent_context


@tool
async def create_agent_context(
    title: str,
    context: str,
    *,
    agent_id: str,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> dict[str, Any]:
    """
    Create a new context for an agent, capturing detailed information about a resource.

    This tool should only be used when an agent's context has not been previously set.
    The context should provide comprehensive details written in markdown about the resource the agent is working with,
    focusing on descriptive information rather than specific commands or scripts.

    Detailed context helps the agent understand the resource's current state and characteristics.

    Examples:
        RDS Context Example:
        - Number of databases
        - User count
        - Table details and relationships
        - View count
        - Index count
        - Connection details
        - Thread information

        EKS Context Example:
        - Node count
        - Pod count
        - Service count
        - Deployment count
        - Job count
        - Cluster node details
    """
    uuid_agent_id = uuid.UUID(agent_id)

    with Session(engine) as session:
        agent_context = AgentContextCreate(
            title=title,
            context=context,
            agent_id=uuid_agent_id,
        )

        agent_context_repository = AgentContextRepository(session=session)
        agent_context = agent_context_repository.create(
            agent_context_create=agent_context,
        )

    return "Agent context created"
