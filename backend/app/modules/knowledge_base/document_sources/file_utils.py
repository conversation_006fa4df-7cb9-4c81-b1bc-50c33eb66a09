from enum import Enum


class FileType(Enum):
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    DOC = "doc"
    XLS = "xls"
    XLSX = "xlsx"
    PPT = "ppt"
    PPTX = "pptx"
    MSG = "msg"
    JPG = "jpg"
    PNG = "png"
    TIFF = "tiff"
    EML = "eml"
    RTF = "rtf"
    JSON = "json"
    CSV = "csv"
    TSV = "tsv"
    HTML = "html"
    XML = "xml"
    MD = "md"
    EPUB = "epub"
    RST = "rst"
    ORG = "org"
    ZIP = "zip"
    ODT = "odt"
    UNK = "unk"
    EMPTY = "empty"

    @classmethod
    def from_mime_type(cls, mime_type: str):
        mime_to_type = {
            "application/pdf": cls.PDF,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document": cls.DOCX,
            "text/plain": cls.TXT,
            "application/msword": cls.DOC,
            "application/vnd.ms-excel": cls.XLS,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": cls.XLSX,
            "application/vnd.ms-powerpoint": cls.PPT,
            "application/vnd.openxmlformats-officedocument.presentationml.presentation": cls.PPTX,
            "application/vnd.ms-outlook": cls.MSG,
            "image/jpeg": cls.JPG,
            "image/png": cls.PNG,
            "image/tiff": cls.TIFF,
            "message/rfc822": cls.EML,
            "application/rtf": cls.RTF,
            "application/json": cls.JSON,
            "text/csv": cls.CSV,
            "text/tab-separated-values": cls.TSV,
            "text/html": cls.HTML,
            "application/xml": cls.XML,
            "text/markdown": cls.MD,
            "application/epub+zip": cls.EPUB,
            "text/x-rst": cls.RST,
            "text/org": cls.ORG,
            "application/zip": cls.ZIP,
            "application/vnd.oasis.opendocument.text": cls.ODT,
        }
        if mime_type in mime_to_type:
            return mime_to_type[mime_type]
        raise ValueError(f"Unsupported MIME type: {mime_type}")


# NOTE: copy from unstructured.io
STR_TO_FILETYPE = {
    "application/pdf": FileType.PDF,
    "application/msword": FileType.DOC,
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": FileType.DOCX,
    "image/jpeg": FileType.JPG,
    "image/png": FileType.PNG,
    "image/tiff": FileType.TIFF,
    "text/plain": FileType.TXT,
    "text/x-csv": FileType.CSV,
    "application/csv": FileType.CSV,
    "application/x-csv": FileType.CSV,
    "text/comma-separated-values": FileType.CSV,
    "text/x-comma-separated-values": FileType.CSV,
    "text/csv": FileType.CSV,
    "text/tsv": FileType.TSV,
    "text/markdown": FileType.MD,
    "text/x-markdown": FileType.MD,
    "text/org": FileType.ORG,
    "text/x-rst": FileType.RST,
    "application/epub": FileType.EPUB,
    "application/epub+zip": FileType.EPUB,
    "application/json": FileType.JSON,
    "application/rtf": FileType.RTF,
    "text/rtf": FileType.RTF,
    "text/html": FileType.HTML,
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": FileType.XLSX,
    "application/vnd.ms-excel": FileType.XLS,
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": FileType.PPTX,
    "application/vnd.ms-powerpoint": FileType.PPT,
    "application/xml": FileType.XML,
    "application/vnd.oasis.opendocument.text": FileType.ODT,
    "message/rfc822": FileType.EML,
    "application/x-ole-storage": FileType.MSG,
    "application/vnd.ms-outlook": FileType.MSG,
    "inode/x-empty": FileType.EMPTY,
}


MIMETYPES_TO_EXCLUDE = [
    "text/x-markdown",
    "application/epub+zip",
    "text/x-csv",
    "application/csv",
    "application/x-csv",
    "text/comma-separated-values",
    "text/x-comma-separated-values",
]

FILETYPE_TO_MIMETYPE = {
    v: k for k, v in STR_TO_FILETYPE.items() if k not in MIMETYPES_TO_EXCLUDE
}

# NOTE: copy from unstructured.io
EXT_TO_FILETYPE = {
    ".pdf": FileType.PDF,
    ".docx": FileType.DOCX,
    ".jpg": FileType.JPG,
    ".jpeg": FileType.JPG,
    ".txt": FileType.TXT,
    ".eml": FileType.EML,
    ".xml": FileType.XML,
    ".htm": FileType.HTML,
    ".html": FileType.HTML,
    ".md": FileType.MD,
    ".org": FileType.ORG,
    ".rst": FileType.RST,
    ".xlsx": FileType.XLSX,
    ".pptx": FileType.PPTX,
    ".png": FileType.PNG,
    ".doc": FileType.DOC,
    ".zip": FileType.ZIP,
    ".xls": FileType.XLS,
    ".ppt": FileType.PPT,
    ".rtf": FileType.RTF,
    ".json": FileType.JSON,
    ".epub": FileType.EPUB,
    ".msg": FileType.MSG,
    ".odt": FileType.ODT,
    ".csv": FileType.CSV,
    ".tsv": FileType.TSV,
    ".tab": FileType.TSV,
    ".tiff": FileType.TIFF,
    None: FileType.UNK,
}

# NOTE: copy from unstructured.io
PLAIN_TEXT_EXTENSIONS = [
    ".txt",
    ".text",
    ".eml",
    ".md",
    ".rtf",
    ".html",
    ".rst",
    ".org",
    ".csv",
    ".tsv",
    ".tab",
    ".json",
]

FILETYPE_TO_EXT = {v: k for k, v in EXT_TO_FILETYPE.items()}
