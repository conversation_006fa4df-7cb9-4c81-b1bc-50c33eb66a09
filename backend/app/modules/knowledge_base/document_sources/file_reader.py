import asyncio
import os
import tempfile
from pathlib import Path

from llama_cloud_services import LlamaParse
from llama_index.core import SimpleDirectoryReader
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document

from app.core.config import settings
from app.logger import logger
from app.models import DocumentKB
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.provider import get_object_storage_repository

from .file_utils import STR_TO_FILETYPE, FileType

# limit how many docs we process in parallel
_CONCURRENT_DOWNLOADS = 4
_semaphore = asyncio.Semaphore(_CONCURRENT_DOWNLOADS)

# create one parser and mapping up front
_PDF_PARSER = LlamaParse(
    parse_mode=settings.LLAMA_PARSE_MODE,
    result_type="text",
)
_EXTRACTOR_MAP: dict[FileType, dict[str, BaseReader]] = {
    FileType.PDF: {".pdf": _PDF_PARSER}
}


async def read(
    docs_to_ingest: list[DocumentKB],
) -> tuple[list[Document], list[DocumentKB]]:
    """
    Process documents in two groups:
     - advanced_group: PDFs via LlamaParse
     - normal_group: everything else via SimpleDirectoryReader
    """
    # split into PDF vs non‐PDF
    advanced_group: list[DocumentKB] = []
    normal_group: list[DocumentKB] = []
    for doc in docs_to_ingest:
        ft = STR_TO_FILETYPE.get(doc.file_type or "", FileType.UNK)
        (advanced_group if ft == FileType.PDF else normal_group).append(doc)

    tasks = []
    if advanced_group:
        tasks += [
            _process_single_document(doc, _EXTRACTOR_MAP[FileType.PDF])
            for doc in advanced_group
        ]
    if normal_group:
        tasks += [_process_single_document(doc, None) for doc in normal_group]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    out: list[Document] = []
    for res, doc in zip(results, (advanced_group + normal_group), strict=False):
        if isinstance(res, Exception):
            logger.error(f"Failed to process {doc.object_name}: {res}")
        else:
            out.extend(res)
    return out, docs_to_ingest


async def _process_single_document(
    doc: DocumentKB,
    file_extractor: dict[str, BaseReader] | None = None,
) -> list[Document]:
    """
    Download one file, run it through SimpleDirectoryReader, clean up.
    Fully async; concurrency limited by semaphore.
    """
    async with _semaphore:
        tmp_dir = tempfile.mkdtemp()
        try:
            # download
            local_path = os.path.join(tmp_dir, Path(doc.object_name).name)
            ob_repo: BaseStorageRepository = get_object_storage_repository()
            await ob_repo.download_file(
                doc.object_name,
                file_path=local_path,
                bucket_name=settings.KB_BUCKET,
            )

            # parse
            docs = SimpleDirectoryReader(
                input_files=[local_path],
                file_extractor=file_extractor,
            ).load_data()

            return docs

        except Exception as e:
            logger.error(f"Error processing {doc.object_name}: {e}", exc_info=True)
            return []
        finally:
            try:
                os.remove(local_path)
                os.rmdir(tmp_dir)
            except OSError:
                pass
