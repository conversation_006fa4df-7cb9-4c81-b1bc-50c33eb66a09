import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class ELBStateMapper(ResourceStateMapper):
    """Maps ELB load balancer states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "active": ResourceStatus.RUNNING,
        "provisioning": ResourceStatus.STARTING,
        "failed": ResourceStatus.STOPPED,
        "active_impaired": ResourceStatus.STOPPED,
        "inactive": ResourceStatus.STOPPED,
    }


class ELBResourceCrawler(BaseCrawler):
    """Crawler for Elastic Load Balancer resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = ELBStateMapper()
        self.client = boto3.client(
            "elb",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl ELB resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all load balancers
            paginator = self.client.get_paginator("describe_load_balancers")
            for page in paginator.paginate():
                for lb in page["LoadBalancerDescriptions"]:
                    try:
                        # Get load balancer tags
                        tags = self.client.describe_tags(
                            LoadBalancerNames=[lb["LoadBalancerName"]]
                        )["TagDescriptions"][0]["Tags"]

                        resource = self._map_load_balancer_to_resource(lb, tags)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for load balancer {lb['LoadBalancerName']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} ELB resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling ELB resources: {str(e)}")
            raise

    def _map_load_balancer_to_resource(self, lb: Dict[str, Any], tags: list[Dict[str, str]]) -> Resource:
        """Map ELB load balancer data to a Resource object."""
        config = {
            "load_balancer_name": lb["LoadBalancerName"],
            "dns_name": lb["DNSName"],
            "canonical_hosted_zone_name": lb.get("CanonicalHostedZoneName"),
            "canonical_hosted_zone_name_id": lb.get("CanonicalHostedZoneNameID"),
            "listener_descriptions": lb["ListenerDescriptions"],
            "policies": lb.get("Policies", {}),
            "backend_server_descriptions": lb.get("BackendServerDescriptions", []),
            "availability_zones": lb["AvailabilityZones"],
            "subnets": lb.get("Subnets", []),
            "security_groups": lb.get("SecurityGroups", []),
            "vpc_id": lb.get("VPCId"),
            "instances": lb.get("Instances", []),
            "health_check": lb.get("HealthCheck", {}),
            "source_security_group": lb.get("SourceSecurityGroup", {}),
            "scheme": lb.get("Scheme"),
            "created_time": lb.get("CreatedTime", "").isoformat() if lb.get("CreatedTime") else None,
            "state": lb.get("State", {}).get("Code", "unknown"),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=lb["LoadBalancerName"],
            region=self.region,
            type="ELB",
            arn=f"arn:aws:elasticloadbalancing:{self.region}:{self.aws_access_account_id}:loadbalancer/{lb['LoadBalancerName']}",
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"Classic Load Balancer in {lb.get('Scheme', 'unknown')} scheme",
            configurations=config,
            status=self.state_mapper.map_state(lb.get("State", {}).get("Code", "unknown")),
        ) 