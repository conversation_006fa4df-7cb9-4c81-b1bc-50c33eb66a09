import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class S3StateMapper(ResourceStateMapper):
    """Maps S3 bucket states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "Enabled": ResourceStatus.RUNNING,
        "Disabled": ResourceStatus.STOPPED,
        "Suspended": ResourceStatus.STOPPED,
        "Deleting": ResourceStatus.DELETED,
    }


class S3ResourceCrawler(BaseCrawler):
    """Crawler for S3 resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = S3StateMapper()
        self.client = boto3.client(
            "s3",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl S3 resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all buckets
            response = self.client.list_buckets()
            for bucket in response["Buckets"]:
                try:
                    # Get bucket details
                    location = self.client.get_bucket_location(Bucket=bucket["Name"])["LocationConstraint"]
                    if location is None:
                        location = "us-east-1"

                    # Get bucket tags
                    try:
                        tags = self.client.get_bucket_tagging(Bucket=bucket["Name"])["TagSet"]
                    except ClientError:
                        tags = []

                    # Get bucket versioning
                    try:
                        versioning = self.client.get_bucket_versioning(Bucket=bucket["Name"])
                    except ClientError:
                        versioning = {}

                    # Get bucket encryption
                    try:
                        encryption = self.client.get_bucket_encryption(Bucket=bucket["Name"])
                    except ClientError:
                        encryption = {}

                    # Get bucket lifecycle
                    try:
                        lifecycle = self.client.get_bucket_lifecycle_configuration(Bucket=bucket["Name"])
                    except ClientError:
                        lifecycle = {}

                    # Get bucket policy
                    try:
                        policy = self.client.get_bucket_policy(Bucket=bucket["Name"])
                    except ClientError:
                        policy = {}

                    resource = self._map_bucket_to_resource(
                        bucket,
                        location,
                        tags,
                        versioning,
                        encryption,
                        lifecycle,
                        policy,
                    )
                    db_resource = resource_repo.create_or_update(resource)
                    all_resources.append(db_resource)
                except ClientError as e:
                    logger.error(f"Error getting details for bucket {bucket['Name']}: {str(e)}")
                    continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} S3 resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling S3 resources: {str(e)}")
            raise

    def _map_bucket_to_resource(
        self,
        bucket: Dict[str, Any],
        location: str,
        tags: list[Dict[str, str]],
        versioning: Dict[str, Any],
        encryption: Dict[str, Any],
        lifecycle: Dict[str, Any],
        policy: Dict[str, Any],
    ) -> Resource:
        """Map S3 bucket data to a Resource object."""
        config = {
            "bucket_name": bucket["Name"],
            "creation_date": bucket["CreationDate"].isoformat(),
            "location": location,
            "versioning": versioning.get("Status", "Disabled"),
            "encryption": encryption.get("ServerSideEncryptionConfiguration", {}),
            "lifecycle_rules": lifecycle.get("Rules", []),
            "policy": policy.get("Policy", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=bucket["Name"],
            region=location,
            type="S3",
            arn=f"arn:aws:s3:::{bucket['Name']}",
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"S3 bucket created on {bucket['CreationDate'].strftime('%Y-%m-%d')}",
            configurations=config,
            status=self.state_mapper.map_state(versioning.get("Status", "Disabled")),
        ) 