import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class OpenSearchStateMapper(ResourceStateMapper):
    """Maps OpenSearch domain states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "Active": ResourceStatus.RUNNING,
        "Processing": ResourceStatus.STARTING,
        "NotAvailable": ResourceStatus.STOPPED,
        "Deleted": ResourceStatus.DELETED,
        "Failed": ResourceStatus.STOPPED,
        "UpgradeProcessing": ResourceStatus.STARTING,
        "UpgradeFailed": ResourceStatus.STOPPED,
        "UpgradeSucceeded": ResourceStatus.RUNNING,
        "UpgradeRollbackSucceeded": ResourceStatus.RUNNING,
        "UpgradeRollbackFailed": ResourceStatus.STOPPED,
        "UpgradeRollbackProcessing": ResourceStatus.STARTING,
    }


class OpenSearchResourceCrawler(BaseCrawler):
    """Crawler for OpenSearch resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = OpenSearchStateMapper()
        self.client = boto3.client(
            "opensearch",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl OpenSearch resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all domains
            paginator = self.client.get_paginator("list_domain_names")
            for page in paginator.paginate():
                for domain in page["DomainNames"]:
                    try:
                        # Get domain details
                        domain_details = self.client.describe_domain(
                            DomainName=domain["DomainName"]
                        )["DomainStatus"]

                        # Get domain tags
                        tags = self.client.list_tags(
                            ARN=domain_details["ARN"]
                        )["TagList"]

                        resource = self._map_domain_to_resource(domain_details, tags)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for domain {domain['DomainName']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} OpenSearch resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling OpenSearch resources: {str(e)}")
            raise

    def _map_domain_to_resource(self, domain: Dict[str, Any], tags: list[Dict[str, str]]) -> Resource:
        """Map OpenSearch domain data to a Resource object."""
        config = {
            "domain_name": domain["DomainName"],
            "domain_id": domain["DomainId"],
            "arn": domain["ARN"],
            "created": domain.get("Created", False),
            "deleted": domain.get("Deleted", False),
            "endpoint": domain.get("Endpoints", {}),
            "processing": domain.get("Processing", False),
            "upgrade_processing": domain.get("UpgradeProcessing", False),
            "engine_version": domain.get("EngineVersion", {}),
            "cluster_config": domain.get("ClusterConfig", {}),
            "ebs_options": domain.get("EBSOptions", {}),
            "access_policies": domain.get("AccessPolicies"),
            "snapshot_options": domain.get("SnapshotOptions", {}),
            "vpc_options": domain.get("VPCOptions", {}),
            "cognito_options": domain.get("CognitoOptions", {}),
            "encryption_at_rest_options": domain.get("EncryptionAtRestOptions", {}),
            "node_to_node_encryption_options": domain.get("NodeToNodeEncryptionOptions", {}),
            "advanced_options": domain.get("AdvancedOptions", {}),
            "log_publishing_options": domain.get("LogPublishingOptions", {}),
            "service_software_options": domain.get("ServiceSoftwareOptions", {}),
            "domain_endpoint_options": domain.get("DomainEndpointOptions", {}),
            "advanced_security_options": domain.get("AdvancedSecurityOptions", {}),
            "auto_tune_options": domain.get("AutoTuneOptions", {}),
            "change_progress_details": domain.get("ChangeProgressDetails", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=domain["DomainName"],
            region=self.region,
            type="OPENSEARCH",
            arn=domain["ARN"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"OpenSearch domain running {domain.get('EngineVersion', {}).get('OpenSearchVersion', 'unknown')}",
            configurations=config,
            status=self.state_mapper.map_state(domain.get("Processing", False) and "Processing" or "Active"),
        ) 