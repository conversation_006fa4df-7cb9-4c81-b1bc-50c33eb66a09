import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class BackupStateMapper(ResourceStateMapper):
    """Maps AWS Backup vault states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "AVAILABLE": ResourceStatus.RUNNING,
        "PENDING_DELETION": ResourceStatus.DELETED,
        "DELETED": ResourceStatus.DELETED,
        "CREATING": ResourceStatus.STARTING,
        "FAILED": ResourceStatus.STOPPED,
        "LOCKED": ResourceStatus.STOPPED,
    }


class BackupResourceCrawler(BaseCrawler):
    """Crawler for AWS Backup resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = BackupStateMapper()
        self.client = boto3.client(
            "backup",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl AWS Backup resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all backup vaults
            paginator = self.client.get_paginator("list_backup_vaults")
            for page in paginator.paginate():
                for vault in page["BackupVaultList"]:
                    try:
                        # Get vault details
                        vault_details = self.client.describe_backup_vault(
                            BackupVaultName=vault["BackupVaultName"]
                        )

                        resource = self._map_vault_to_resource(vault_details)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for backup vault {vault['BackupVaultName']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} AWS Backup resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling AWS Backup resources: {str(e)}")
            raise

    def _map_vault_to_resource(self, vault: Dict[str, Any]) -> Resource:
        """Map AWS Backup vault data to a Resource object."""
        config = {
            "backup_vault_name": vault["BackupVaultName"],
            "backup_vault_arn": vault["BackupVaultArn"],
            "creation_date": vault["CreationDate"].isoformat(),
            "encryption_key_arn": vault.get("EncryptionKeyArn"),
            "number_of_recovery_points": vault.get("NumberOfRecoveryPoints", 0),
            "locked": vault.get("Locked", False),
            "min_retention_days": vault.get("MinRetentionDays"),
            "max_retention_days": vault.get("MaxRetentionDays"),
            "lock_date": vault.get("LockDate", "").isoformat() if vault.get("LockDate") else None,
            "state": "LOCKED" if vault.get("Locked", False) else "AVAILABLE",
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=vault["BackupVaultName"],
            region=self.region,
            type="BACKUP",
            arn=vault["BackupVaultArn"],
            tags=vault.get("Tags", {}),
            description=f"AWS Backup vault with {config['number_of_recovery_points']} recovery points",
            configurations=config,
            status=self.state_mapper.map_state(config["state"]),
        ) 