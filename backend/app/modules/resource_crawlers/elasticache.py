import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class ElastiCacheStateMapper(ResourceStateMapper):
    """Maps ElastiCache cluster states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "modifying": ResourceStatus.STARTING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "incompatible-network": ResourceStatus.STOPPED,
        "incompatible-parameters": ResourceStatus.STOPPED,
        "incompatible-restore": ResourceStatus.STOPPED,
        "restore-failed": ResourceStatus.STOPPED,
        "backup-restoring": ResourceStatus.STARTING,
        "snapshotting": ResourceStatus.RUNNING,
        "rebooting": ResourceStatus.STARTING,
        "maintenance": ResourceStatus.STARTING,
        "failed": ResourceStatus.STOPPED,
    }


class ElastiCacheResourceCrawler(BaseCrawler):
    """Crawler for ElastiCache resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = ElastiCacheStateMapper()
        self.client = boto3.client(
            "elasticache",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl ElastiCache resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all clusters
            paginator = self.client.get_paginator("describe_cache_clusters")
            for page in paginator.paginate():
                for cluster in page["CacheClusters"]:
                    try:
                        # Get cluster tags
                        tags = self.client.list_tags_for_resource(
                            ResourceName=cluster["ARN"]
                        )["TagList"]

                        resource = self._map_cluster_to_resource(cluster, tags)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for cluster {cluster['CacheClusterId']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} ElastiCache resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling ElastiCache resources: {str(e)}")
            raise

    def _map_cluster_to_resource(self, cluster: Dict[str, Any], tags: list[Dict[str, str]]) -> Resource:
        """Map ElastiCache cluster data to a Resource object."""
        config = {
            "cache_cluster_id": cluster["CacheClusterId"],
            "engine": cluster["Engine"],
            "engine_version": cluster["EngineVersion"],
            "cache_node_type": cluster["CacheNodeType"],
            "num_cache_nodes": cluster["NumCacheNodes"],
            "status": cluster["CacheClusterStatus"],
            "port": cluster.get("Port"),
            "preferred_availability_zone": cluster.get("PreferredAvailabilityZone"),
            "preferred_maintenance_window": cluster.get("PreferredMaintenanceWindow"),
            "replication_group_id": cluster.get("ReplicationGroupId"),
            "snapshot_retention_limit": cluster.get("SnapshotRetentionLimit"),
            "snapshot_window": cluster.get("SnapshotWindow"),
            "auth_token_enabled": cluster.get("AuthTokenEnabled", False),
            "transit_encryption_enabled": cluster.get("TransitEncryptionEnabled", False),
            "at_rest_encryption_enabled": cluster.get("AtRestEncryptionEnabled", False),
            "cache_nodes": cluster.get("CacheNodes", []),
            "cache_parameter_group": cluster.get("CacheParameterGroup", {}),
            "cache_subnet_group": cluster.get("CacheSubnetGroup", {}),
            "cache_security_groups": cluster.get("CacheSecurityGroups", []),
            "notification_configuration": cluster.get("NotificationConfiguration", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=cluster["CacheClusterId"],
            region=self.region,
            type="ELASTICACHE",
            arn=cluster["ARN"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"ElastiCache cluster running {cluster['Engine']} {cluster['EngineVersion']}",
            configurations=config,
            status=self.state_mapper.map_state(cluster["CacheClusterStatus"]),
        ) 