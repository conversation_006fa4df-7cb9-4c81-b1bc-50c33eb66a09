import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class ElasticBeanstalkStateMapper(ResourceStateMapper):
    """Maps Elastic Beanstalk environment states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "Launching": ResourceStatus.STARTING,
        "Updating": ResourceStatus.STARTING,
        "Ready": ResourceStatus.RUNNING,
        "Terminating": ResourceStatus.DELETED,
        "Terminated": ResourceStatus.DELETED,
        "Degraded": ResourceStatus.STOPPED,
        "Severe": ResourceStatus.STOPPED,
    }


class ElasticBeanstalkResourceCrawler(BaseCrawler):
    """Crawler for Elastic Beanstalk resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = ElasticBeanstalkStateMapper()
        self.client = boto3.client(
            "elasticbeanstalk",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl Elastic Beanstalk resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all environments
            paginator = self.client.get_paginator("describe_environments")
            for page in paginator.paginate():
                for environment in page["Environments"]:
                    try:
                        # Get environment resources
                        resources = self.client.describe_environment_resources(
                            EnvironmentId=environment["EnvironmentId"]
                        )["EnvironmentResources"]

                        resource = self._map_environment_to_resource(environment, resources)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for environment {environment['EnvironmentName']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} Elastic Beanstalk resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling Elastic Beanstalk resources: {str(e)}")
            raise

    def _map_environment_to_resource(self, environment: Dict[str, Any], resources: Dict[str, Any]) -> Resource:
        """Map Elastic Beanstalk environment data to a Resource object."""
        config = {
            "environment_name": environment["EnvironmentName"],
            "environment_id": environment["EnvironmentId"],
            "application_name": environment["ApplicationName"],
            "version_label": environment.get("VersionLabel"),
            "solution_stack_name": environment.get("SolutionStackName"),
            "platform_arn": environment.get("PlatformArn"),
            "template_name": environment.get("TemplateName"),
            "description": environment.get("Description"),
            "endpoint_url": environment.get("EndpointURL"),
            "cname": environment.get("CNAME"),
            "status": environment["Status"],
            "health": environment.get("Health"),
            "health_status": environment.get("HealthStatus"),
            "tier": environment.get("Tier", {}),
            "environment_links": environment.get("EnvironmentLinks", []),
            "resources": {
                "auto_scaling_groups": resources.get("AutoScalingGroups", []),
                "instances": resources.get("Instances", []),
                "launch_configurations": resources.get("LaunchConfigurations", []),
                "load_balancers": resources.get("LoadBalancers", []),
                "triggers": resources.get("Triggers", []),
                "queues": resources.get("Queues", []),
            },
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=environment["EnvironmentName"],
            region=self.region,
            type="ELASTIC_BEANSTALK",
            arn=environment["EnvironmentArn"],
            tags=environment.get("Tags", {}),
            description=f"Elastic Beanstalk environment in {environment['Status']} status",
            configurations=config,
            status=self.state_mapper.map_state(environment["Status"]),
        ) 