import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class LambdaStateMapper(ResourceStateMapper):
    """Maps Lambda function states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "active": ResourceStatus.RUNNING,
        "pending": ResourceStatus.STARTING,
        "failed": ResourceStatus.DELETED,
        "inactive": ResourceStatus.STOPPED,
    }


class LambdaResourceCrawler(BaseCrawler):
    """Crawler for AWS Lambda resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = LambdaStateMapper()
        self.client = boto3.client(
            "lambda",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl AWS Lambda resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all functions
            paginator = self.client.get_paginator("list_functions")
            for page in paginator.paginate():
                for function in page["Functions"]:
                    try:
                        # Get function tags
                        tags = self.client.list_tags(
                            Resource=function["FunctionArn"]
                        )["Tags"]

                        resource = self._map_function_to_resource(function, tags)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for function {function['FunctionName']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} AWS Lambda resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling AWS Lambda resources: {str(e)}")
            raise

    def _map_function_to_resource(self, function: Dict[str, Any], tags: Dict[str, str]) -> Resource:
        """Map Lambda function data to a Resource object."""
        config = {
            "function_name": function["FunctionName"],
            "function_arn": function["FunctionArn"],
            "runtime": function["Runtime"],
            "role": function["Role"],
            "handler": function["Handler"],
            "code_size": function["CodeSize"],
            "description": function.get("Description"),
            "timeout": function["Timeout"],
            "memory_size": function["MemorySize"],
            "last_modified": function["LastModified"],
            "code_sha256": function["CodeSha256"],
            "version": function["Version"],
            "vpc_config": function.get("VpcConfig", {}),
            "environment": function.get("Environment", {}),
            "kms_key_arn": function.get("KMSKeyArn"),
            "tracing_config": function.get("TracingConfig", {}),
            "master_arn": function.get("MasterArn"),
            "revision_id": function.get("RevisionId"),
            "layers": function.get("Layers", []),
            "state": function.get("State"),
            "state_reason": function.get("StateReason"),
            "state_reason_code": function.get("StateReasonCode"),
            "last_update_status": function.get("LastUpdateStatus"),
            "last_update_status_reason": function.get("LastUpdateStatusReason"),
            "last_update_status_reason_code": function.get("LastUpdateStatusReasonCode"),
            "file_system_configs": function.get("FileSystemConfigs", []),
            "package_type": function.get("PackageType"),
            "image_config_response": function.get("ImageConfigResponse", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=function["FunctionName"],
            region=self.region,
            type="LAMBDA",
            arn=function["FunctionArn"],
            tags=tags,
            description=f"Lambda function running {function['Runtime']}",
            configurations=config,
            status=self.state_mapper.map_state(function.get("State", "active")),
        ) 