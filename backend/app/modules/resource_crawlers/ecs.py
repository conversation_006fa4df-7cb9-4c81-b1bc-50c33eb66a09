import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.models import Metric, MetricType, Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import MetricRepository, ResourceRepository

logger = logging.getLogger(__name__)


class ECSStateMapper(ResourceStateMapper):
    """Maps ECS resource states to ResourceStatus"""
    
    _STATE_MAPPING = {
        # Cluster states
        "active": ResourceStatus.RUNNING,
        "provisioning": ResourceStatus.STARTING,
        "deprovisioning": ResourceStatus.STOPPED,
        "failed": ResourceStatus.DELETED,
        "inactive": ResourceStatus.STOPPED,
        
        # Service states
        "active": ResourceStatus.RUNNING,
        "draining": ResourceStatus.STOPPED,
        "primary": ResourceStatus.RUNNING,
        "deregistering": ResourceStatus.DELETED,
    }


class ECSResourceCrawler(BaseCrawler):
    def __init__(self, metrics_to_fetch: list[str] | None = None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = ECSStateMapper()

        self.metrics_to_fetch = metrics_to_fetch or [
            "CPUUtilization",
            "MemoryUtilization",
            "RunningTaskCount",
            "PendingTaskCount",
            "ServiceCount",
            "StorageReadBytes",
            "StorageWriteBytes",
            "NetworkRxBytes",
            "NetworkTxBytes",
        ]

        self.ecs_client = self.session.client("ecs", region_name=self.region)

    def _crawl_ecs_clusters(self) -> list[dict[str, Any]]:
        """
        Crawl ECS clusters in the specified region.
        """
        clusters = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.ecs_client.get_paginator("list_clusters")
                cluster_arns = []
                for page in paginator.paginate():
                    cluster_arns.extend(page["clusterArns"])

                if cluster_arns:
                    response = self.ecs_client.describe_clusters(
                        clusters=cluster_arns, include=["TAGS"]
                    )
                    clusters.extend(response["clusters"])
                return clusters

            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error crawling ECS clusters in {self.region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)

    def _crawl_ecs_services(self, cluster_arn: str) -> list[dict[str, Any]]:
        """
        Crawl ECS services for a specific cluster.
        """
        services = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.ecs_client.get_paginator("list_services")
                service_arns = []
                for page in paginator.paginate(cluster=cluster_arn):
                    service_arns.extend(page["serviceArns"])

                if service_arns:
                    # Describe services in batches of 10 (AWS limit)
                    for i in range(0, len(service_arns), 10):
                        batch = service_arns[i : i + 10]
                        response = self.ecs_client.describe_services(
                            cluster=cluster_arn, services=batch, include=["TAGS"]
                        )
                        services.extend(response["services"])
                return services

            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error crawling ECS services for cluster {cluster_arn} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)

    def _map_ecs_to_resource(
        self, resource_data: dict[str, Any], resource_type: str, cluster_arn: str
    ) -> Resource:
        """
        Map ECS resource data to a Resource object.
        """
        tags = {tag["key"]: tag["value"] for tag in resource_data.get("tags", [])}

        config = {}
        if resource_type == "cluster":
            config = {
                "status": resource_data["status"],
                "registeredContainerInstancesCount": resource_data[
                    "registeredContainerInstancesCount"
                ],
                "runningTasksCount": resource_data["runningTasksCount"],
                "pendingTasksCount": resource_data["pendingTasksCount"],
                "activeServicesCount": resource_data["activeServicesCount"],
                "capacityProviders": resource_data.get("capacityProviders", []),
                "defaultCapacityProviderStrategy": resource_data.get(
                    "defaultCapacityProviderStrategy", []
                ),
            }
        elif resource_type == "service":
            config = {
                "status": resource_data["status"],
                "desiredCount": resource_data["desiredCount"],
                "runningCount": resource_data["runningCount"],
                "pendingCount": resource_data["pendingCount"],
                "launchType": resource_data.get("launchType"),
                "platformVersion": resource_data.get("platformVersion"),
                "taskDefinition": resource_data["taskDefinition"],
                "deploymentConfiguration": resource_data.get(
                    "deploymentConfiguration", {}
                ),
                "networkConfiguration": resource_data.get("networkConfiguration", {}),
                "loadBalancers": resource_data.get("loadBalancers", []),
            }

        return Resource(
            workspace_id=self.workspace_id,
            name=resource_data["clusterName"]
            if resource_type == "cluster"
            else resource_data["serviceName"],
            type="ECS",
            arn=resource_data["clusterArn"]
            if resource_type == "cluster"
            else resource_data["serviceArn"],
            region=self.region,
            tags=tags,
            configurations=config,
            description=f"ECS {resource_type} in cluster {cluster_arn}",
            status=self.state_mapper.map_state(resource_data["status"]),
        )

    def _fetch_cloudwatch_metrics(
        self, resource_arns: list[str], resource_type: str
    ) -> list[dict[str, Any]]:
        """
        Fetch CloudWatch metrics for ECS resources.
        """
        cloudwatch = self.session.client("cloudwatch", region_name=self.region)
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(minutes=30)

        all_metrics = []
        dimension_name = "ClusterName" if resource_type == "cluster" else "ServiceName"

        for attempt in range(self.max_retries):
            try:
                for metric_name in self.metrics_to_fetch:
                    response = cloudwatch.get_metric_data(
                        MetricDataQueries=[
                            {
                                "Id": f"metric_{i}",
                                "MetricStat": {
                                    "Metric": {
                                        "Namespace": "AWS/ECS",
                                        "MetricName": metric_name,
                                        "Dimensions": [
                                            {
                                                "Name": dimension_name,
                                                "Value": arn.split("/")[-1],
                                            }
                                        ],
                                    },
                                    "Period": 86400,
                                    "Stat": "Average",
                                },
                                "ReturnData": True,
                            }
                            for i, arn in enumerate(resource_arns)
                        ],
                        StartTime=start_time,
                        EndTime=end_time,
                    )

                    for i, arn in enumerate(resource_arns):
                        metric_data = response["MetricDataResults"][i]
                        if metric_data["Values"]:
                            all_metrics.extend(
                                [
                                    {
                                        "ResourceArn": arn,
                                        "MetricName": metric_name,
                                        "Value": value,
                                        "Unit": self._get_default_unit(metric_name),
                                        "Timestamp": timestamp,
                                    }
                                    for value, timestamp in zip(
                                        metric_data["Values"],
                                        metric_data["Timestamps"],
                                        strict=False,
                                    )
                                ]
                            )
                return all_metrics

            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error fetching CloudWatch metrics in {self.region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)

    def _get_default_unit(self, metric_name: str) -> str:
        """
        Get default unit for a given metric name.
        """
        default_units = {
            "CPUUtilization": "Percent",
            "MemoryUtilization": "Percent",
            "RunningTaskCount": "Count",
            "PendingTaskCount": "Count",
            "ServiceCount": "Count",
            "StorageReadBytes": "Bytes",
            "StorageWriteBytes": "Bytes",
            "NetworkRxBytes": "Bytes",
            "NetworkTxBytes": "Bytes",
        }
        return default_units.get(metric_name, "None")

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """
        Crawl all ECS resources and their metrics.
        """
        all_resources = []
        all_metrics = []

        resource_repo = ResourceRepository(db)
        metric_repo = MetricRepository(db)

        # Crawl clusters
        clusters = self._crawl_ecs_clusters()

        for cluster in clusters:
            # Create/update cluster resource
            cluster_resource = self._map_ecs_to_resource(
                cluster, "cluster", cluster["clusterArn"]
            )
            db_cluster = resource_repo.create_or_update(cluster_resource)
            all_resources.append(db_cluster)

            # Crawl services for each cluster
            services = self._crawl_ecs_services(cluster["clusterArn"])

            for service in services:
                # Create/update service resource
                service_resource = self._map_ecs_to_resource(
                    service, "service", cluster["clusterArn"]
                )
                db_service = resource_repo.create_or_update(service_resource)
                all_resources.append(db_service)

        # Fetch metrics for all resources
        # Separate clusters and services by checking if the resource description contains "cluster" or "service"
        cluster_arns = [r.arn for r in all_resources if r.type == "ECS" and "cluster" in r.description.lower()]
        service_arns = [r.arn for r in all_resources if r.type == "ECS" and "service" in r.description.lower()]

        if cluster_arns:
            cluster_metrics = self._fetch_cloudwatch_metrics(cluster_arns, "cluster")
            for metric in cluster_metrics:
                resource = next(
                    (r for r in all_resources if r.arn == metric["ResourceArn"]), None
                )
                if resource:
                    db_metric = metric_repo.create_or_update(
                        Metric(
                            name=metric["MetricName"],
                            value=metric["Value"],
                            unit=metric["Unit"],
                            timestamp=metric["Timestamp"],
                            type=MetricType.PERFORMANCE,
                            resource_id=resource.id,
                        )
                    )
                    all_metrics.append(db_metric)

        if service_arns:
            service_metrics = self._fetch_cloudwatch_metrics(service_arns, "service")
            for metric in service_metrics:
                resource = next(
                    (r for r in all_resources if r.arn == metric["ResourceArn"]), None
                )
                if resource:
                    db_metric = metric_repo.create_or_update(
                        Metric(
                            name=metric["MetricName"],
                            value=metric["Value"],
                            unit=metric["Unit"],
                            timestamp=metric["Timestamp"],
                            type=MetricType.PERFORMANCE,
                            resource_id=resource.id,
                        )
                    )
                    all_metrics.append(db_metric)

        db.commit()
        logger.info(
            f"Crawled {len(all_resources)} ECS resources and {len(all_metrics)} metrics"
        )
        return all_resources
