import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class EFSStateMapper(ResourceStateMapper):
    """Maps EFS file system states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "updating": ResourceStatus.STARTING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "error": ResourceStatus.STOPPED,
        "misconfigured": ResourceStatus.STOPPED,
    }


class EFSResourceCrawler(BaseCrawler):
    """Crawler for EFS resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = EFSStateMapper()
        self.client = boto3.client(
            "efs",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl EFS resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all file systems
            paginator = self.client.get_paginator("describe_file_systems")
            for page in paginator.paginate():
                for filesystem in page["FileSystems"]:
                    try:
                        # Get file system tags
                        tags = self.client.describe_tags(
                            FileSystemId=filesystem["FileSystemId"]
                        )["Tags"]

                        resource = self._map_filesystem_to_resource(filesystem, tags)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for file system {filesystem['FileSystemId']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} EFS resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling EFS resources: {str(e)}")
            raise

    def _map_filesystem_to_resource(self, filesystem: Dict[str, Any], tags: list[Dict[str, str]]) -> Resource:
        """Map EFS file system data to a Resource object."""
        config = {
            "file_system_id": filesystem["FileSystemId"],
            "creation_time": filesystem["CreationTime"].isoformat(),
            "life_cycle_state": filesystem["LifeCycleState"],
            "performance_mode": filesystem["PerformanceMode"],
            "throughput_mode": filesystem.get("ThroughputMode"),
            "encrypted": filesystem.get("Encrypted", False),
            "kms_key_id": filesystem.get("KmsKeyId"),
            "provisioned_throughput_in_mibps": filesystem.get("ProvisionedThroughputInMibps"),
            # "size_in_bytes": filesystem.get("SizeInBytes", {}),
            "availability_zone_id": filesystem.get("AvailabilityZoneId"),
            "availability_zone_name": filesystem.get("AvailabilityZoneName"),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=filesystem["FileSystemId"],
            region=self.region,
            type="EFS",
            arn=filesystem["FileSystemArn"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"EFS file system in {filesystem['LifeCycleState']} state",
            configurations=config,
            status=self.state_mapper.map_state(filesystem["LifeCycleState"]),
        ) 