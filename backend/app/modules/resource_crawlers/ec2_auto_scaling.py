import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class EC2AutoScalingStateMapper(ResourceStateMapper):
    """Maps EC2 Auto Scaling Group states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "Delete in progress": ResourceStatus.DELETED,
        "Delete failed": ResourceStatus.STOPPED,
        "Delete complete": ResourceStatus.DELETED,
        "Update in progress": ResourceStatus.STARTING,
        "Update failed": ResourceStatus.STOPPED,
        "Update complete": ResourceStatus.RUNNING,
        "Create in progress": ResourceStatus.STARTING,
        "Create failed": ResourceStatus.STOPPED,
        "Create complete": ResourceStatus.RUNNING,
    }


class EC2AutoScalingResourceCrawler(BaseCrawler):
    """Crawler for EC2 Auto Scaling resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = EC2AutoScalingStateMapper()
        self.client = boto3.client(
            "autoscaling",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl EC2 Auto Scaling resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all Auto Scaling Groups
            paginator = self.client.get_paginator("describe_auto_scaling_groups")
            for page in paginator.paginate():
                for asg in page["AutoScalingGroups"]:
                    resource = self._map_asg_to_resource(asg)
                    db_resource = resource_repo.create_or_update(resource)
                    all_resources.append(db_resource)

            db.commit()
            logger.info(f"Crawled {len(all_resources)} EC2 Auto Scaling resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling EC2 Auto Scaling resources: {str(e)}")
            raise

    def _map_asg_to_resource(self, asg: Dict[str, Any]) -> Resource:
        """Map Auto Scaling Group data to a Resource object"""
        config = {
            "min_size": asg["MinSize"],
            "max_size": asg["MaxSize"],
            "desired_capacity": asg["DesiredCapacity"],
            "launch_configuration_name": asg.get("LaunchConfigurationName"),
            "launch_template": asg.get("LaunchTemplate"),
            "mixed_instances_policy": asg.get("MixedInstancesPolicy"),
            "vpc_zone_identifier": asg.get("VPCZoneIdentifier"),
            "health_check_type": asg["HealthCheckType"],
            "health_check_grace_period": asg["HealthCheckGracePeriod"],
            "created_time": asg["CreatedTime"].isoformat(),
            "status": asg.get("Status", "Create complete"),  # Default to running state if not specified
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=asg["AutoScalingGroupName"],
            region=self.region,
            type="EC2_AUTO_SCALING",
            arn=asg["AutoScalingGroupARN"],
            tags={tag["Key"]: tag["Value"] for tag in asg.get("Tags", [])},
            description=f"Auto Scaling Group with desired capacity {asg['DesiredCapacity']}",
            configurations=config,
            status=self.state_mapper.map_state(asg.get("Status", "Create complete")),
        ) 