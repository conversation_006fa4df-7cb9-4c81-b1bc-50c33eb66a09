import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class AppRunnerStateMapper(ResourceStateMapper):
    """Maps App Runner service states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "CREATE_FAILED": ResourceStatus.STOPPED,
        "RUNNING": ResourceStatus.RUNNING,
        "DELETED": ResourceStatus.DELETED,
        "DELETE_FAILED": ResourceStatus.STOPPED,
        "PAUSED": ResourceStatus.STOPPED,
        "OPERATION_IN_PROGRESS": ResourceStatus.STARTING,
        "CREATE_IN_PROGRESS": ResourceStatus.STARTING,
        "DELETE_IN_PROGRESS": ResourceStatus.DELETED,
        "UPDATE_IN_PROGRESS": ResourceStatus.STARTING,
        "UPDATE_FAILED": ResourceStatus.STOPPED,
        "PAUSE_FAILED": ResourceStatus.STOPPED,
        "RESUME_FAILED": ResourceStatus.STOPPED,
    }


class AppRunnerResourceCrawler(BaseCrawler):
    """Crawler for AWS App Runner resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = AppRunnerStateMapper()
        self.client = boto3.client(
            "apprunner",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl AWS App Runner resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all services
            paginator = self.client.get_paginator("list_services")
            for page in paginator.paginate():
                for service in page["ServiceSummaryList"]:
                    try:
                        # Get service details
                        service_details = self.client.describe_service(
                            ServiceArn=service["ServiceArn"]
                        )["Service"]

                        resource = self._map_service_to_resource(service_details)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for service {service['ServiceName']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} AWS App Runner resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling AWS App Runner resources: {str(e)}")
            raise

    def _map_service_to_resource(self, service: Dict[str, Any]) -> Resource:
        """Map App Runner service data to a Resource object."""
        config = {
            "service_name": service["ServiceName"],
            "service_id": service["ServiceId"],
            "service_arn": service["ServiceArn"],
            "service_url": service["ServiceUrl"],
            "status": service["Status"],
            "source_configuration": service.get("SourceConfiguration", {}),
            "instance_configuration": service.get("InstanceConfiguration", {}),
            "encryption_configuration": service.get("EncryptionConfiguration", {}),
            "health_check_configuration": service.get("HealthCheckConfiguration", {}),
            "auto_scaling_configuration": service.get("AutoScalingConfigurationSummary", {}),
            "network_configuration": service.get("NetworkConfiguration", {}),
            "observability_configuration": service.get("ObservabilityConfiguration", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=service["ServiceName"],
            region=self.region,
            type="APP_RUNNER",
            arn=service["ServiceArn"],
            tags=service.get("Tags", {}),
            description=f"App Runner service in {service['Status']} status",
            configurations=config,
            status=self.state_mapper.map_state(service["Status"]),
        ) 