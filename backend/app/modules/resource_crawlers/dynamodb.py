import logging
import time
from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class DynamoDBStateMapper(ResourceStateMapper):
    """Maps DynamoDB table states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "updating": ResourceStatus.RUNNING,
        "deleting": ResourceStatus.DELETED,
        "active": ResourceStatus.RUNNING,
        "archived": ResourceStatus.STOPPED,
        "archiving": ResourceStatus.STOPPED,
        "inaccessible_encryption_credentials": ResourceStatus.STOPPED,
    }


class DynamoDBResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = DynamoDBStateMapper()
        self.dynamodb_client = self.session.client("dynamodb", region_name=self.region)

    def _crawl_dynamodb_tables(self) -> list[dict[str, Any]]:
        """Crawl DynamoDB tables in the specified region."""
        tables = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.dynamodb_client.get_paginator("list_tables")
                for page in paginator.paginate():
                    for table_name in page["TableNames"]:
                        try:
                            # Get table details
                            table_response = self.dynamodb_client.describe_table(
                                TableName=table_name
                            )
                            table = table_response["Table"]

                            # Get table tags
                            tags = {}
                            try:
                                tag_response = self.dynamodb_client.list_tags_of_resource(
                                    ResourceArn=table["TableArn"]
                                )
                                tags = {
                                    tag["Key"]: tag["Value"]
                                    for tag in tag_response.get("Tags", [])
                                }
                            except ClientError:
                                # Table may not have tags
                                pass

                            table_data = {
                                "TableName": table["TableName"],
                                "TableArn": table["TableArn"],
                                "TableStatus": table["TableStatus"],
                                "CreationDateTime": table["CreationDateTime"],
                                "ItemCount": table.get("ItemCount", 0),
                                "TableSizeBytes": table.get("TableSizeBytes", 0),
                                "ProvisionedThroughput": table.get(
                                    "ProvisionedThroughput", {}
                                ),
                                "GlobalSecondaryIndexes": table.get(
                                    "GlobalSecondaryIndexes", []
                                ),
                                "LocalSecondaryIndexes": table.get(
                                    "LocalSecondaryIndexes", []
                                ),
                                "StreamSpecification": table.get("StreamSpecification", {}),
                                "Tags": tags,
                            }
                            tables.append(table_data)
                        except ClientError as e:
                            logger.error(
                                f"Error getting details for table {table_name}: {e}"
                            )
                return tables
            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error crawling DynamoDB tables in {self.region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)

    def _map_dynamodb_to_resource(self, table: dict[str, Any]) -> Resource:
        """Map DynamoDB table data to a Resource object."""
        config = {
            "TableStatus": table["TableStatus"],
            "CreationDateTime": table["CreationDateTime"].isoformat(),
            "ItemCount": table["ItemCount"],
            "TableSizeBytes": table["TableSizeBytes"],
            "ProvisionedThroughput": table["ProvisionedThroughput"],
            "GlobalSecondaryIndexes": table["GlobalSecondaryIndexes"],
            "LocalSecondaryIndexes": table["LocalSecondaryIndexes"],
            "StreamSpecification": table["StreamSpecification"],
        }

        resource = Resource(
            workspace_id=self.workspace_id,
            name=table["TableName"],
            region=self.region,
            type="DYNAMODB",
            arn=table["TableArn"],
            tags=table["Tags"],
            description=f"DynamoDB table with {table['ItemCount']} items",
            configurations=config,
            status=self.state_mapper.map_state(table["TableStatus"]),
        )
        return resource

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl all DynamoDB resources."""
        all_resources = []
        resource_repo = ResourceRepository(db)

        tables = self._crawl_dynamodb_tables()
        if tables:
            resources = [self._map_dynamodb_to_resource(table) for table in tables]
            for resource in resources:
                db_resource = resource_repo.create_or_update(resource)
                all_resources.append(db_resource)

        db.commit()
        logger.info(
            f"Crawled {len(all_resources)} DynamoDB resources"
        )
        return all_resources 