import logging
from typing import Any, Dict

import boto3
from botocore.exceptions import Client<PERSON>rror
from sqlmodel import Session

from app.models import Resource, ResourceStatus
from app.modules.resource_crawlers.base_crawler import BaseCrawler, ResourceStateMapper
from app.repositories.resources import ResourceRepository

logger = logging.getLogger(__name__)


class DocumentDBStateMapper(ResourceStateMapper):
    """Maps DocumentDB cluster states to ResourceStatus"""
    
    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "failed": ResourceStatus.STOPPED,
        "incompatible-restore": ResourceStatus.STOPPED,
        "incompatible-parameters": ResourceStatus.STOPPED,
        "incompatible-network": ResourceStatus.STOPPED,
        "incompatible-credentials": ResourceStatus.STOPPED,
        "incompatible-option-group": ResourceStatus.STOPPED,
        "incompatible-parameter-group": ResourceStatus.STOPPED,
        "incompatible-security-group": ResourceStatus.STOPPED,
        "incompatible-subnet": ResourceStatus.STOPPED,
        "incompatible-vpc": ResourceStatus.STOPPED,
        "incompatible-version": ResourceStatus.STOPPED,
        "incompatible-zone": ResourceStatus.STOPPED,
        "incompatible-credentials": ResourceStatus.STOPPED,
        "incompatible-option-group": ResourceStatus.STOPPED,
        "incompatible-parameter-group": ResourceStatus.STOPPED,
        "incompatible-security-group": ResourceStatus.STOPPED,
        "incompatible-subnet": ResourceStatus.STOPPED,
        "incompatible-vpc": ResourceStatus.STOPPED,
        "incompatible-version": ResourceStatus.STOPPED,
        "incompatible-zone": ResourceStatus.STOPPED,
        "modifying": ResourceStatus.STARTING,
        "rebooting": ResourceStatus.STARTING,
        "renaming": ResourceStatus.STARTING,
        "resizing": ResourceStatus.STARTING,
        "rotating-keys": ResourceStatus.STARTING,
        "storage-full": ResourceStatus.STOPPED,
        "updating-hsm": ResourceStatus.STARTING,
    }


class DocumentDBResourceCrawler(BaseCrawler):
    """Crawler for DocumentDB resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = DocumentDBStateMapper()
        self.client = boto3.client(
            "docdb",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl DocumentDB resources"""
        all_resources = []
        resource_repo = ResourceRepository(db)

        try:
            # Get all clusters
            paginator = self.client.get_paginator("describe_db_clusters")
            for page in paginator.paginate():
                for cluster in page["DBClusters"]:
                    try:
                        # Get cluster tags
                        tags = self.client.list_tags_for_resource(
                            ResourceArn=cluster["DBClusterArn"]
                        )["TagList"]

                        resource = self._map_cluster_to_resource(cluster, tags)
                        db_resource = resource_repo.create_or_update(resource)
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for cluster {cluster['DBClusterIdentifier']}: {str(e)}"
                        )
                        continue

            db.commit()
            logger.info(f"Crawled {len(all_resources)} DocumentDB resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling DocumentDB resources: {str(e)}")
            raise

    def _map_cluster_to_resource(self, cluster: Dict[str, Any], tags: list[Dict[str, str]]) -> Resource:
        """Map DocumentDB cluster data to a Resource object."""
        config = {
            "engine": cluster["Engine"],
            "engine_version": cluster["EngineVersion"],
            "status": cluster["Status"],
            "master_username": cluster["MasterUsername"],
            "db_cluster_identifier": cluster["DBClusterIdentifier"],
            "endpoint": cluster["Endpoint"],
            "port": cluster["Port"],
            "vpc_security_groups": cluster.get("VpcSecurityGroups", []),
            "db_subnet_group": cluster.get("DBSubnetGroup", {}),
            "availability_zones": cluster.get("AvailabilityZones", []),
            "backup_retention_period": cluster.get("BackupRetentionPeriod"),
            "preferred_backup_window": cluster.get("PreferredBackupWindow"),
            "preferred_maintenance_window": cluster.get("PreferredMaintenanceWindow"),
            "storage_encrypted": cluster.get("StorageEncrypted", False),
            "kms_key_id": cluster.get("KmsKeyId"),
            "db_cluster_parameter_group": cluster.get("DBClusterParameterGroup"),
            "deletion_protection": cluster.get("DeletionProtection", False),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=cluster["DBClusterIdentifier"],
            region=self.region,
            type="DOCUMENTDB",
            arn=cluster["DBClusterArn"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"DocumentDB cluster running {cluster['Engine']} {cluster['EngineVersion']}",
            configurations=config,
            status=self.state_mapper.map_state(cluster["Status"]),
        ) 