# import asyncio
# from datetime import timedelta

# from app.core.config import settings
# from app.repositories.object_storage.provider import get_object_storage_repository


# async def main():
#     os_repo = get_object_storage_repository()

#     # Get presigned URL
#     presigned_url = await os_repo.get_presigned_url(
#         object_name="7c511051-14ea-43e3-bdee-2c01076d9c39/AWS Elastic Beanstalk and AWS CloudFormation (1).pdf",
#         bucket_name=settings.KB_BUCKET,
#         expires=timedelta(seconds=3600),
#     )

#     print(presigned_url)


# if __name__ == "__main__":
#     asyncio.run(main())


import asyncio
from urllib.parse import urlparse, urlunparse

from crawl4ai import (
    AsyncWebCrawler,
    BrowserConfig,
    CacheMode,
    CrawlerRunConfig,
    RateLimiter,
)
from crawl4ai.async_dispatcher import SemaphoreDispatcher
from crawl4ai.deep_crawling import BestFirstCrawlingStrategy
from crawl4ai.models import Crawl<PERSON><PERSON>ult
from rich import print

# limit how many docs we process in parallel
_CONCURRENT_CRAWLS = 8
_semaphore = asyncio.Semaphore(_CONCURRENT_CRAWLS)

_browser_config = BrowserConfig(
    headless=True  # , use_managed_browser=True, browser_type="chromium"
)
_dispatcher = SemaphoreDispatcher(
    max_session_permit=20,
    rate_limiter=RateLimiter(base_delay=(0.01, 0.5), max_delay=5.0),
)

config = CrawlerRunConfig(
    # markdown_generator=DefaultMarkdownGenerator(
    #     content_filter=PruningContentFilter()
    # ),
    # scraping_strategy=LXMLWebScrapingStrategy(),
    cache_mode=CacheMode.BYPASS,
    remove_overlay_elements=True,
    exclude_internal_links=False,
    exclude_external_links=True,
    exclude_social_media_links=True,
    exclude_external_images=True,
    remove_forms=True,
    # wait_for_images=False,
    scan_full_page=True,
    # scroll_delay=0.5,
    # verbose=False,
    # check_robots_txt=False,
    # mean_delay=0.1,
    # max_range=0.1,
    # magic=True,
    # page_timeout=60000,
    # screenshot=False,
    # pdf=False,
    # excluded_tags=["form", "header", "img"],
    # stream=False,
    deep_crawl_strategy=BestFirstCrawlingStrategy(
        max_depth=2,
        include_external=False,
        max_pages=20,
    ),
)


def clean_and_deduplicate_urls(links: list[dict]) -> list[dict]:
    """
    Clean URLs by removing query parameters and fragments, then deduplicate.

    Args:
        links: List of link dictionaries with 'href' key

    Returns:
        List of cleaned and deduplicated link dictionaries
    """
    seen_urls = set()
    cleaned_links = []
    skip_urls = [
        "changelog",
        "license",
        "contributing",
        "security",
        "privacy",
        "terms",
        "imprint",
        "contact",
        "about",
        "faq",
        "support",
        "help",
        "events",
        "careers",
        "partners",
        "investors",
    ]

    for link in links:
        url = link["href"]

        # Parse the URL
        parsed = urlparse(url)

        # Remove query parameters and fragments
        cleaned_url = urlunparse(
            (
                parsed.scheme,
                parsed.netloc,
                parsed.path.rstrip("/"),  # Remove trailing slash for consistency
                "",  # params
                "",  # query
                "",  # fragment
            )
        )

        # Add trailing slash back for root paths to maintain consistency
        if not cleaned_url.endswith("/") and parsed.path in ["", "/"]:
            cleaned_url += "/"

        # Skip if we've already seen this cleaned URL
        if cleaned_url in seen_urls:
            continue

        # Skip URLs with fragments (like #section)
        if parsed.fragment:
            continue

        # Skip URLs that contain any of the skip_urls
        if any(skip_url in cleaned_url for skip_url in skip_urls):
            continue

        seen_urls.add(cleaned_url)

        # Create new link dict with cleaned URL
        cleaned_link = link.copy()
        cleaned_link["href"] = cleaned_url
        cleaned_links.append(cleaned_link)

    return cleaned_links


async def main():
    urls = [
        "https://langchain-ai.github.io/langmem/"
    ]  # , "https://docs.cloudthinker.io/how-to-guide/empowering-your-ai-agents-with-company-knowledge-cloudthinker-knowledge-base-guide/"]
    deep_crawl = True
    url = "https://langchain-ai.github.io/langmem/"

    # Get all internal links
    async with AsyncWebCrawler(config=_browser_config) as crawler:
        all_res: list[CrawlResult] = await crawler.arun(
            url=url,
            config=config,
            dispatcher=_dispatcher,
        )

        total_internal_links = []
        total_cleaned_links = []

        for res in all_res:
            import pdb

            pdb.set_trace()
            internal_links = res.links["internal"]

            print(f"Total internal links: {len(internal_links)}")
            print(internal_links)

            # Clean and deduplicate URLs
            cleaned_links = clean_and_deduplicate_urls(internal_links)

            print(f"Total cleaned links: {len(cleaned_links)}")
            print(cleaned_links)

            total_internal_links.extend(internal_links)
            total_cleaned_links.extend(cleaned_links)

        import pdb

        pdb.set_trace()

        print(f"Total internal links: {len(total_internal_links)}")
        print(total_internal_links)

        print(f"Total cleaned links: {len(total_cleaned_links)}")
        print(total_cleaned_links)

    # async with AsyncWebCrawler(config=_browser_config) as crawler:
    #     res_all_urls: list[CrawlResult] = await crawler.arun_many(
    #         urls=urls,
    #         config=get_crawling_config(deep_crawl=deep_crawl),
    #         dispatcher=_dispatcher,
    #     )

    #     import pdb; pdb.set_trace()

    #     for res_url in res_all_urls:
    #         print(f"URL: {res_url.url}")
    #         print(f"Success: {res_url.success}")
    #         # print(res_url.downloaded_files)
    #         # print(res_url.js_execution_result)
    #         # print(f"PDF: {res_url.pdf}")
    #         # print(f"Extracted content: {res_url.extracted_content}")
    #         print(f"Metadata: {res_url.metadata}")
    #         print(f"Error message: {res_url.error_message}")
    #         # print(f"Session ID: {res_url.session_id}")
    #         # print(f"Response headers: {res_url.response_headers}")
    #         # print(f"Status code: {res_url.status_code}")
    #         # print(f"SSL certificate: {res_url.ssl_certificate}")
    #         print(f"Dispatch result: {res_url.dispatch_result}")
    #         print(f"Redirected URL: {res_url.redirected_url}")
    #         # print(res_url.markdown)

    #     return res_all_urls


if __name__ == "__main__":
    asyncio.run(main())


# import asyncio
# import requests
# from app.core.config import settings
# from app.repositories.object_storage.s3 import S3StorageRepository

# async def main():
#     os_repo = S3StorageRepository()
#     presigned_url = await os_repo.get_presigned_put_url(
#         object_name="test/test.txt",
#         bucket_name=settings.KB_BUCKET,
#         expires=3600,
#     )
#     print(presigned_url)
#     response = requests.put(presigned_url, data=open("/Users/<USER>/BuilderStudio/cloud-cost-optimization/backend/app/test.txt", "rb"))
#     print(response.status_code)
#     print(response.text)


# if __name__ == "__main__":
#     asyncio.run(main())


# import asyncio
# import logging
# import uuid

# from sqlmodel import Session
# from app.core.db import async_engine
# from sqlmodel.ext.asyncio.session import AsyncSession
# from app.services.memory.memory_service import MemoryService

# logger = logging.getLogger(__name__)

# async def main():
#     conversation_id = "0ffec42b-f217-45fa-a253-aeaa49ecef50"
#     async with AsyncSession(async_engine) as session:
#         conv_uuid = uuid.UUID(conversation_id)
#         memory_handler = MemoryService(session)
#         await memory_handler.extract_key_learnings_by_role(conv_uuid)

# if __name__ == "__main__":
#     asyncio.run(main())


# Install with pip install firecrawl-py
# import asyncio
# from firecrawl import AsyncFirecrawlApp

# async def main():
#     app = AsyncFirecrawlApp(api_key='fc-21a747e0c0ff467bb1f26b5514115325')
#     response = await app.scrape_url(
#         url='https://langchain-ai.github.io/langmem/',
#         formats= [ 'markdown' ],
#         only_main_content= True
#     )
#     print(response)

# asyncio.run(main())
