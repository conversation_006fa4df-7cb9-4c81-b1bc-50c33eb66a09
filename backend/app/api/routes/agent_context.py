import uuid

from fastapi import APIRouter

from app.api.deps import CurrentUser, SessionAsyncDep
from app.models import (
    AgentContextListInput,
    AgentContextListResponse,
    AgentContextRead,
    AgentContextUpdate,
)
from app.services.agent_context_service import AgentContextService

router = APIRouter()


@router.put("/{agent_id}", response_model=AgentContextRead)
async def update_agent_context(
    agent_id: uuid.UUID,
    input: AgentContextUpdate,
    async_session: SessionAsyncDep,
    current_user: CurrentUser,
) -> AgentContextRead:
    """Update agent context"""
    service = AgentContextService(async_session=async_session)
    return await service.aupdate_agent_context(agent_id, input, current_user)


@router.get("/{agent_id}", response_model=AgentContextRead | None)
async def get_agent_context(
    agent_id: uuid.UUID,
    async_session: SessionAsyncDep,
    current_user: CurrentUser,
) -> AgentContextRead | None:
    """Get context based on agent id"""
    service = AgentContextService(async_session=async_session)
    return await service.aget_agent_context(agent_id, current_user)


@router.get("/", response_model=AgentContextListResponse)
async def get_agent_contexts(
    input: AgentContextListInput,
    async_session: SessionAsyncDep,
    current_user: CurrentUser,
) -> AgentContextListResponse:
    """Get contexts based on a list of agent ids, for each agent id, get the latest none deleted context"""
    service = AgentContextService(async_session=async_session)
    return await service.aget_agent_contexts(input, current_user)
