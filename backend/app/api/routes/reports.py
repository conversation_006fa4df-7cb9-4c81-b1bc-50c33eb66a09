import logging
from datetime import datetime, timedelta

from fastapi import APIRouter, Query
from sqlalchemy import case, desc
from sqlmodel import and_, func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    ChartDataPoint,
    Recommendation,
    RecommendationStatus,
    Resource,
    ResourceSavingsReport,
    ResourceType,
    SavingSummaryReport,
    ServiceSavingsData,
    ServiceSavingsReport,
    TopSavingsReport,
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/saving-summary", response_model=SavingSummaryReport)
def get_savings_summary(
    session: SessionDep,
    current_user: CurrentUser,
    start_date: datetime | None = Query(None),
    end_date: datetime | None = Query(None),
    previous_start_date: datetime | None = Query(None),
    previous_end_date: datetime | None = Query(None),
) -> SavingSummaryReport:
    """
    Get total potential savings for the workspace with comparison between two periods.
    """
    now = datetime.utcnow()

    if not end_date:
        end_date = now.replace(
            day=1, hour=23, minute=59, second=59, microsecond=999999
        ).replace(month=now.month + 1) - timedelta(days=1)

    if not start_date:
        start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    period_duration = end_date - start_date

    if not previous_end_date:
        previous_end_date = start_date

    if not previous_start_date:
        previous_start_date = previous_end_date - period_duration

    # Base query without date conditions
    common_query = (
        select(
            func.sum(
                case(
                    (
                        Recommendation.status.in_(
                            [
                                # RecommendationStatus.IN_PROGRESS,
                                RecommendationStatus.PENDING,
                            ]
                        ),
                        Recommendation.potential_savings,
                    ),
                    else_=0,
                )
            ).label("potential"),
            func.count(
                case(
                    (
                        Recommendation.status.in_(
                            [
                                # RecommendationStatus.IN_PROGRESS,
                                RecommendationStatus.PENDING,
                            ]
                        ),
                        Recommendation.id,
                    ),
                    else_=None,
                )
            ).label("opportunities"),
            func.sum(
                case(
                    (
                        Recommendation.status == RecommendationStatus.IMPLEMENTED,
                        Recommendation.potential_savings,
                    ),
                    else_=0,
                )
            ).label("saved"),
            func.count(
                case(
                    (
                        Recommendation.status == RecommendationStatus.IN_PROGRESS,
                        Recommendation.id,
                    ),
                    else_=None,
                )
            ).label("active"),
        )
        .join(Resource, Resource.id == Recommendation.resource_id)
        .where(Resource.workspace_id == current_user.current_workspace_id)
    )

    # Get current period totals
    current_result = session.exec(
        common_query.where(
            and_(
                Recommendation.created_at >= start_date,
                Recommendation.created_at <= end_date,
            )
        )
    ).first()

    current_totals = {
        "potential": float(current_result.potential or 0),
        "opportunities": float(current_result.opportunities or 0),
        "saved": float(current_result.saved or 0),
        "active": float(current_result.active or 0),
    }

    # Get previous period totals
    previous_result = session.exec(
        common_query.where(
            and_(
                Recommendation.created_at >= previous_start_date,
                Recommendation.created_at <= previous_end_date,
            )
        )
    ).first()

    previous_totals = {
        "potential": float(previous_result.potential or 0),
        "opportunities": float(previous_result.opportunities or 0),
        "saved": float(previous_result.saved or 0),
        "active": float(previous_result.active or 0),
    }
    logger.debug(
        f"Current period totals from {start_date} to {end_date}:\n"
        f"  Potential: ${current_totals['potential']:.2f}\n"
        f"  Opportunities: {current_totals['opportunities']}\n"
        f"  Saved: ${current_totals['saved']:.2f}\n"
        f"  Active: {current_totals['active']}"
    )

    logger.debug(
        f"Previous period totals from {previous_start_date} to {previous_end_date}:\n"
        f"  Potential: ${previous_totals['potential']:.2f}\n"
        f"  Opportunities: {previous_totals['opportunities']}\n"
        f"  Saved: ${previous_totals['saved']:.2f}\n"
        f"  Active: {previous_totals['active']}"
    )

    def calculate_percentage_change(
        current: float, previous: float, is_count: bool = False
    ) -> float:
        if previous == 0:
            return round(current, 2)
        return round(((current - previous) / previous * 100), 2)

    return SavingSummaryReport(
        potential_savings=round(current_totals["potential"], 2),
        save_opportunities=round(current_totals["opportunities"], 2),
        total_saved=round(current_totals["saved"], 2),
        active_saving=round(current_totals["active"], 2),
        potential_savings_percentage_change=calculate_percentage_change(
            current_totals["potential"], previous_totals["potential"]
        ),
        save_opportunities_percentage_change=calculate_percentage_change(
            current_totals["opportunities"],
            previous_totals["opportunities"],
            is_count=True,
        ),
        total_saved_percentage_change=calculate_percentage_change(
            current_totals["saved"], previous_totals["saved"]
        ),
        active_saving_percentage_change=calculate_percentage_change(
            current_totals["active"], previous_totals["active"], is_count=True
        ),
    )


@router.get("/resource-saving", response_model=ResourceSavingsReport)
def get_savings_by_resource(
    session: SessionDep,
    current_user: CurrentUser,
    start_date: datetime | None = Query(None),
    end_date: datetime | None = Query(None),
) -> ResourceSavingsReport:
    """
    Get savings data grouped by date for RDS and EC2 resources.
    """
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    query = (
        select(
            func.date_trunc("day", Recommendation.created_at).label("date"),
            Resource.type,
            func.sum(Recommendation.potential_savings).label("savings"),
        )
        .join(Resource, Resource.id == Recommendation.resource_id)
        .where(
            and_(
                Resource.workspace_id == current_user.current_workspace_id,
                Resource.type.in_([ResourceType.EC2, ResourceType.RDS]),
                Recommendation.status == RecommendationStatus.IMPLEMENTED,
                Recommendation.created_at >= start_date,
                Recommendation.created_at <= end_date,
            )
        )
        .group_by("date", Resource.type)
        .order_by("date", Resource.type)
    )

    # Process query results
    rds_data = []
    ec2_data = []
    total_rds_savings = 0.0
    total_ec2_savings = 0.0

    for row in session.exec(query):
        savings = float(row.savings or 0)
        data_point = ChartDataPoint(date=row.date, value=savings)
        if row.type == ResourceType.RDS:
            rds_data.append(data_point)
            total_rds_savings += savings
        elif row.type == ResourceType.EC2:
            ec2_data.append(data_point)
            total_ec2_savings += savings

    logger.debug(
        f"Retrieved savings chart data for period {start_date} to {end_date}:\n"
        f"  RDS points: {len(rds_data)} (total: ${total_rds_savings:.2f})\n"
        f"  EC2 points: {len(ec2_data)} (total: ${total_ec2_savings:.2f})"
    )

    return ResourceSavingsReport(
        rds_savings=rds_data,
        ec2_savings=ec2_data,
        total_rds_savings=round(total_rds_savings, 2),
        total_ec2_savings=round(total_ec2_savings, 2),
    )


@router.get("/top-potential-savings", response_model=TopSavingsReport)
async def get_top_potential_savings(
    session: SessionDep,
    current_user: CurrentUser,
    limit: int = Query(5, ge=1, le=100),
    start_date: datetime | None = Query(None, description="Start date in ISO format"),
    end_date: datetime | None = Query(None, description="End date in ISO format"),
):
    """
    Get top N recommendations with highest potential savings that are in PENDING status
    for the current workspace within the specified date range.
    """

    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    top_recommendations = session.exec(
        select(Recommendation)
        .join(Resource, Resource.id == Recommendation.resource_id)
        .where(
            and_(
                Resource.workspace_id == current_user.current_workspace_id,
                Recommendation.status == RecommendationStatus.PENDING,
                Recommendation.created_at >= start_date,
                Recommendation.created_at <= end_date,
            )
        )
        .order_by(desc(Recommendation.potential_savings))
        .limit(limit)
    ).all()

    return TopSavingsReport(data=top_recommendations)


@router.get("/service-savings", response_model=ServiceSavingsReport)
def get_savings_by_service(
    session: SessionDep,
    current_user: CurrentUser,
    start_date: datetime | None = Query(None),
    end_date: datetime | None = Query(None),
) -> ServiceSavingsReport:
    """
    Get savings data grouped by service type for pie chart visualization.
    """
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    # Query to get total potential savings by service type
    query = (
        select(
            Resource.type.label("service"),
            func.sum(Recommendation.potential_savings).label("savings"),
        )
        .join(Resource, Resource.id == Recommendation.resource_id)
        .where(
            and_(
                Resource.workspace_id == current_user.current_workspace_id,
                Recommendation.status == RecommendationStatus.PENDING,
                Recommendation.created_at >= start_date,
                Recommendation.created_at <= end_date,
            )
        )
        .group_by(Resource.type)
        .order_by(desc("savings"))
    )

    results = session.exec(query).all()

    # Calculate total savings for percentage calculation
    total_savings = sum(float(row.savings or 0) for row in results)

    # Process results and calculate percentages
    service_data = []
    for row in results:
        savings = float(row.savings or 0)
        percentage = (savings / total_savings * 100) if total_savings > 0 else 0
        service_data.append(
            ServiceSavingsData(
                service=row.service, savings=savings, percentage=percentage
            )
        )

    logger.debug(
        f"Retrieved service savings data for period {start_date} to {end_date}:\n"
        f"  Total services: {len(service_data)}\n"
        f"  Total savings: ${total_savings:.2f}"
    )

    return ServiceSavingsReport(data=service_data, total_savings=total_savings)
