import uuid
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, status
from pydantic import BaseModel

from app.api.deps import CurrentUser, SessionDep
from app.models import MessageHistoryPublic
from app.repositories.conversation import ConversationRepository
from app.services.agent.autonomous_agent_service import AutonomousAgentService

router = APIRouter()


class ShareResponse(BaseModel):
    share_id: uuid.UUID
    is_shared: bool
    shared_at: datetime
    shared_by: uuid.UUID


@router.post("/conversations/{conversation_id}/share", response_model=ShareResponse)
async def create_share_link(
    conversation_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
):
    """Create a share link for a conversation"""
    conversation_repo = ConversationRepository(session=session)
    conversation = conversation_repo.get_conversation(conversation_id)

    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
        )

    # Check if user has permission to share this conversation
    if conversation.agent.workspace_id not in [w.id for w in current_user.workspaces]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to share this conversation",
        )

    # Generate new share ID
    conversation.share_id = uuid.uuid4()
    conversation.is_shared = True
    conversation.shared_at = datetime.now()
    conversation.shared_by = current_user.id

    session.commit()
    session.refresh(conversation)

    return ShareResponse(
        share_id=conversation.share_id,
        is_shared=conversation.is_shared,
        shared_at=conversation.shared_at,
        shared_by=conversation.shared_by,
    )


@router.get("/conversations/shared/{share_id}", response_model=MessageHistoryPublic)
async def get_shared_conversation(
    share_id: uuid.UUID,
    session: SessionDep,
    limit: int = Query(default=200, ge=1, le=500),
):
    """Get message history for a shared conversation by share ID (no authentication required)"""
    conversation_repo = ConversationRepository(session=session)
    conversation = conversation_repo.get_conversation_by_share_id(share_id)

    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Shared conversation: {conversation}")

    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Shared conversation not found",
        )

    service = AutonomousAgentService(session=session)
    return service.get_conversation_history(conversation.id, limit)


@router.delete("/conversations/{conversation_id}/share")
async def revoke_share_link(
    conversation_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
):
    """Revoke a share link for a conversation"""
    conversation_repo = ConversationRepository(session=session)
    conversation = conversation_repo.get_conversation(conversation_id)

    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
        )

    # Check if user has permission to revoke this conversation
    if conversation.agent.workspace_id not in [w.id for w in current_user.workspaces]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to revoke this conversation",
        )

    # Revoke share
    conversation.share_id = None
    conversation.is_shared = False
    conversation.shared_at = None
    conversation.shared_by = None

    session.commit()

    return {"message": "Share link revoked successfully"}


@router.get("/conversations/{conversation_id}/share", response_model=ShareResponse)
async def get_share_link(
    conversation_id: uuid.UUID,
    session: SessionDep,
):
    """Get a share link for a conversation"""
    conversation_repo = ConversationRepository(session=session)
    conversation = conversation_repo.get_conversation(conversation_id)

    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
        )

    return ShareResponse(
        share_id=conversation.share_id,
        is_shared=conversation.is_shared,
        shared_at=conversation.shared_at,
        shared_by=conversation.shared_by,
    )
