import uuid

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import (
    Agent,
    AgentContextListInput,
    AgentContextListResponse,
    AgentContextRead,
    AgentContextUpdate,
    AuthorizedUser,
)
from app.repositories.agent_context import AgentContextRepository


class AgentContextService:
    def __init__(self, async_session: AsyncSession | None = None):
        self.async_session = async_session

    async def check_user_can_access_agent(
        self, agent_id: uuid.UUID, current_user: AuthorizedUser
    ):
        # Fetch the agent
        result = await self.async_session.exec(
            select(Agent).where(Agent.id == agent_id)
        )
        agent = result.first()
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        # Check if user has access to the agent's workspace
        user_workspace_ids = [
            ws.id for ws in getattr(current_user, "workspaces", []) or []
        ]

        # Optionally, also allow if user is the owner of the workspace
        own_workspace_ids = [
            ws.id for ws in getattr(current_user, "own_workspaces", []) or []
        ]

        if agent.workspace_id not in user_workspace_ids + own_workspace_ids:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this agent"
            )
        return agent

    async def aupdate_agent_context(
        self,
        agent_id: uuid.UUID,
        input: AgentContextUpdate,
        current_user: AuthorizedUser,
    ) -> AgentContextRead:
        try:
            await self.check_user_can_access_agent(agent_id, current_user)
            repo = AgentContextRepository(async_session=self.async_session)
            context = await repo.aupdate_by_agent_id(agent_id, input)
            if not context:
                raise HTTPException(status_code=404, detail="Agent context not found")
            return context
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    async def aget_agent_context(
        self, agent_id: uuid.UUID, current_user: AuthorizedUser
    ) -> AgentContextRead | None:
        try:
            await self.check_user_can_access_agent(agent_id, current_user)
            repo = AgentContextRepository(async_session=self.async_session)
            context = await repo.aget_by_agent_id(agent_id)
            if not context:
                return None
            return context
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    async def aget_agent_contexts(
        self, input: AgentContextListInput, current_user: AuthorizedUser
    ) -> AgentContextListResponse:
        try:
            for agent_id in input.agent_ids:
                await self.check_user_can_access_agent(agent_id, current_user)
            repo = AgentContextRepository(async_session=self.async_session)
            contexts = await repo.aget_by_agent_ids(input.agent_ids)
            return AgentContextListResponse(data=contexts, count=len(contexts))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
