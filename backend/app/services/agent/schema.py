import uuid
from typing import Any

from pydantic import BaseModel


class NamespaceChangeOutput(BaseModel):
    namespace: str | None
    response_message: Any  # Should be replaced with a more specific type if available
    response_text: str
    full_message_text: str
    current_thought: Any | None  # Replace with specific type if available
    first_checkpoint_id: Any | None  # Replace with UUID or specific type if available
    last_checkpoint_id: Any | None  # Replace with UUID or specific type if available
    current_thought_idx: int
    input_token_usage: int
    output_token_usage: int


class NamespaceChangeInput(BaseModel):
    event: dict
    current_namespace: str | None
    response_message: Any  # Should be replaced with a more specific type if available
    response_text: str
    full_message_text: str
    conversation_id: uuid.UUID
    first_checkpoint_id: str | None
    last_checkpoint_id: str | None
    agents_config: Any  # Should be replaced with a more specific type if available
    current_thought: Any | None  # Replace with specific type if available
    current_thought_idx: int
    input_token_usage: int
    output_token_usage: int
