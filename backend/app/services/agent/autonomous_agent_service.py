import logging
import uuid
from collections.abc import AsyncGenerator
from typing import Any
from uuid import UUID

from langgraph.types import Command

from app.core.langfuse import langfuse_handler
from app.models import (
    Conversation,
    MessageDisplayComponent,
    MessageDisplayComponentType,
)
from app.repositories.notification import NotificationRepository

from .base_service import AgentBaseService
from .schema import NamespaceChangeInput, NamespaceChangeOutput

logger = logging.getLogger(__name__)


class AutonomousAgentService(AgentBaseService):
    """Service for managing and executing autonomous agents."""

    def _prepare_graph_input(
        self,
        user_message_content,
        message_content,
        resume,
        approve,
        checkpoint_id_exists,
    ):
        """Prepare the input for the graph based on mode."""
        if resume:
            return Command(
                resume={"approve": approve, "approve_message": message_content}
            )
        elif not checkpoint_id_exists:
            return {"messages": user_message_content}
        else:
            return None

    async def process_message_stream(
        self,
        conversation_id: uuid.UUID,
        message_content: str | None = None,
        message_action_type: str | None = None,
        resume: bool = False,
        approve: bool = False,
        restore: bool = False,
        restore_message_id: uuid.UUID | None = None,
        graph_name: str = "autonomous_agent",
        config: dict | None = None,
        user_id: uuid.UUID | None = None,
        resource_id: UUID | None = None,
    ) -> AsyncGenerator[dict[str, Any], None]:
        """Process a message using the langgraph API and stream the response.

        Args:
            conversation_id: ID of the conversation
            message_content: Content of the user message (required if not resuming)
            message_action_type: Action type for the message
            resume: Whether to resume a previous interrupted conversation
            approve: Whether to approve a waiting checkpoint
            restore: Whether to restore from a specific message
            restore_message_id: ID of the message to restore from
            graph_name: Name of the graph to use
            config: Optional configuration dictionary

        Yields:
            Dict containing response data for streaming
        """
        graph_name = "networking_agent"
        config = config or {}

        # Validate basic requirements
        if not resume and message_content is None:
            yield {
                "type": "error",
                "content": "Message content must be provided when not resuming",
                "message_id": None,
            }
            return

        # Create or get the message based on inputs
        try:
            user_message, need_title_generation, checkpoint_id = self._prepare_message(
                conversation_id=conversation_id,
                message_content=message_content,
                resume=resume,
                action_type=message_action_type,
                restore=restore,
                message_id=restore_message_id,
            )

            conversation = self.session.get(Conversation, conversation_id)
            if not conversation:
                yield {
                    "type": "error",
                    "content": "Conversation not found",
                    "message_id": None,
                }
                return

            # Setup response message
            response_message = (
                self._get_last_interrupted_message(conversation.id)
                if resume
                else self._create_message(conversation.id, "", "assistant")
            )
            response_message.is_interrupt = False
            self.session.add(response_message)
            self.session.commit()

            if resume and not response_message:
                yield {
                    "type": "error",
                    "content": "No interrupted message found to resume",
                    "message_id": None,
                }
                return
        except ValueError as e:
            yield {"type": "error", "content": str(e), "message_id": None}
            return

        if await self.check_out_of_quota(conversation.agent.workspace_id):
            self._cleanup_on_error(None, user_message)
            yield {"type": "error", "content": "Out of quota", "message_id": None}
            return

        # Prepare conversation config if not provided
        graph, config = await self._prepare_conversation_config(
            conversation,
            checkpoint_id,
            graph_name,
            user_id,
            resource_id,
            message_content,
        )

        # Convert config to dict
        agents_config = config.agents_config
        config = config.to_dict()

        # Process stream variables
        response_text = ""
        full_message_text = ""

        # Tracking agent thought
        current_thought = None
        current_thought_idx = 0

        # Tracking checkpoint ids
        first_checkpoint_id, last_checkpoint_id = None, None

        # Tracking namespace
        current_namespace = None

        # Tracking token usage
        input_token_usage, output_token_usage = 0, 0

        # if interrupted, namespace is last response namespace
        if resume:
            # Yield message ID at the start of the stream
            yield {
                "type": "message_id",
                "message_id": str(response_message.id),
                "namespace": response_message.role,
            }

            current_namespace = response_message.role

            # Must account the cases where the latest response is display component
            if len(response_message.thoughts) > 0:
                thought_position = response_message.thoughts[-1].position
            else:
                thought_position = -1

            if len(response_message.display_components) > 0:
                display_component_position = response_message.display_components[
                    -1
                ].position
            else:
                display_component_position = -1

            if thought_position == -1 and display_component_position == -1:
                current_thought = None
                current_thought_idx = 0
            elif display_component_position > thought_position:
                current_thought = None
                current_thought_idx = display_component_position
            else:
                current_thought = response_message.thoughts[-1]
                current_thought_idx = thought_position

            # Mark notification as read
            repo = NotificationRepository()
            repo.sync_mark_notification_read_given_message_id(
                message_id=response_message.id,
                session=self.session,
            )

        # Prepare graph input
        graph_input = self._prepare_graph_input(
            user_message.content,
            message_content,
            resume,
            approve,
            config["configurable"]["checkpoint_id"] is not None,
        )

        # Add langfuse handler to config
        config["run_name"] = "Autonomous Agent"
        config["callbacks"] = [langfuse_handler]

        try:
            async for event in graph.astream_events(
                input=graph_input,
                config=config,
                stream_mode=["debug", "events", "custom"],
                version="v2",
                subgraphs=True,
            ):
                # Token Usage Tracking
                input_token_usage, output_token_usage = self._handle_token_usage(
                    event, input_token_usage, output_token_usage
                )

                # Namespace Tracking
                if event["event"] == "on_chain_start":
                    namespace_change_output = self._handle_namespace_change(
                        NamespaceChangeInput(
                            event=event,
                            current_namespace=current_namespace,
                            response_message=response_message,
                            response_text=response_text,
                            full_message_text=full_message_text,
                            conversation_id=user_message.conversation_id,
                            first_checkpoint_id=first_checkpoint_id,
                            last_checkpoint_id=last_checkpoint_id,
                            agents_config=agents_config,
                            current_thought=current_thought,
                            current_thought_idx=current_thought_idx,
                            input_token_usage=input_token_usage,
                            output_token_usage=output_token_usage,
                        )
                    )

                    if current_namespace != namespace_change_output.namespace:
                        yield {
                            "type": "message_id",
                            "message_id": str(
                                namespace_change_output.response_message.id
                            ),
                            "namespace": namespace_change_output.namespace,
                        }

                    current_namespace = namespace_change_output.namespace
                    response_message = namespace_change_output.response_message
                    response_text = namespace_change_output.response_text
                    full_message_text = namespace_change_output.full_message_text
                    current_thought = namespace_change_output.current_thought
                    first_checkpoint_id = namespace_change_output.first_checkpoint_id
                    last_checkpoint_id = namespace_change_output.last_checkpoint_id
                    current_thought_idx = namespace_change_output.current_thought_idx
                    input_token_usage = namespace_change_output.input_token_usage
                    output_token_usage = namespace_change_output.output_token_usage

                if event["event"] == "on_chain_stream" and isinstance(
                    event["data"]["chunk"], tuple
                ):
                    event_data = event["data"]["chunk"][2]
                    event_type = event["data"]["chunk"][1]

                    if event_type == "debug":
                        (
                            first_checkpoint_id,
                            last_checkpoint_id,
                            interrupt_result,
                        ) = self._handle_chain_stream_debug(
                            event_data,
                            first_checkpoint_id,
                            last_checkpoint_id,
                            response_message,
                            current_namespace,
                            resume,
                            full_message_text,
                        )

                        if interrupt_result:
                            interrupt_result["namespace"] = current_namespace
                            yield interrupt_result
                            response_message.role = current_namespace
                            self.session.add(response_message)
                            self.session.commit()

                            self.track_token_usage(
                                message_id=str(response_message.id),
                                input_token_count=input_token_usage,
                                output_token_count=output_token_usage,
                                model_id="us.anthropic.claude-3-5-sonnet-20240620-v2:0",
                                workspace_id=str(
                                    response_message.conversation.agent.workspace_id
                                ),
                            )
                            return
                        continue

                    elif event_type == "custom":
                        if (
                            event_data["type"]
                            == "on_recommendation_generation_response"
                        ):
                            # Increment thought index if needed
                            if current_thought:
                                current_thought_idx += 1

                            # Extract metadata for recommendations
                            additional_metadata = {}
                            if (
                                "metadata" in event_data
                                and "resource_id" in event_data["metadata"]
                            ):
                                additional_metadata["resource_id"] = event_data[
                                    "metadata"
                                ]["resource_id"]

                            # Save and stream recommendations in a single operation with metadata
                            components = self._save_recommendations_as_components(
                                response_message,
                                event_data["content"],
                                current_thought_idx,
                                additional_metadata=additional_metadata,
                            )

                            # Reset state for next iteration
                            response_text = ""
                            current_thought = None
                            current_thought_idx += 1

                            # Stream components efficiently
                            for component in components:
                                yield {
                                    "type": "display_component",
                                    "content": component,
                                    # "namespace": current_namespace,
                                }
                        elif (
                            event_type == "custom"
                            and event_data.get("type") == "on_chart_generation_response"
                        ):
                            # Handle custom chart component stream
                            chart_data = event_data.get("content")

                            if not chart_data:
                                logger.warning(
                                    "No chart data received in display component event"
                                )
                                continue

                            try:
                                # Increment current thought for display component
                                if current_thought:
                                    current_thought_idx += 1

                                current_thought_idx = self._process_chart_component(
                                    response_message=response_message,
                                    chart_data=chart_data,
                                    component_position=current_thought_idx,
                                )
                                response_text = ""
                                current_thought = None
                                current_thought_idx += 1

                                # Format the component for streaming
                                if (
                                    isinstance(chart_data, dict)
                                    and "chart_data" in chart_data
                                ):
                                    # Get the most recently created chart component for this message
                                    chart_component = (
                                        self.session.query(MessageDisplayComponent)
                                        .filter(
                                            MessageDisplayComponent.message_id
                                            == response_message.id,
                                            MessageDisplayComponent.type
                                            == MessageDisplayComponentType.CHART,
                                        )
                                        .order_by(
                                            MessageDisplayComponent.created_at.desc()
                                        )
                                        .first()
                                    )

                                    if chart_component:
                                        # Format the component for streaming
                                        component_data = {
                                            "id": str(chart_component.id),
                                            "type": chart_component.type.value,
                                            "chart_type": chart_component.chart_type.value,
                                            "title": chart_component.title,
                                            "description": chart_component.description,
                                            "data": chart_component.data,
                                            "config": chart_component.config,
                                            "position": chart_component.position,
                                            "created_at": int(
                                                chart_component.created_at.timestamp()
                                            ),
                                        }

                                        # Stream the component
                                        yield {
                                            "type": "display_component",
                                            "content": component_data,
                                            # "namespace": current_namespace,
                                        }

                                else:
                                    logger.error("Invalid chart data format received")
                                    logger.error(f"Chart data: {chart_data}")
                            except Exception as e:
                                logger.error(
                                    f"Error processing chart component: {str(e)}"
                                )
                                logger.error(f"Chart data: {chart_data}")
                                continue
                        else:
                            yield event_data

                (
                    response_text,
                    current_thought,
                    current_thought_idx,
                    result,
                ) = self._handle_stream_event(
                    event,
                    current_thought,
                    response_text,
                    response_message,
                    current_thought_idx,
                )

                # Only yield result if it has a type and non-empty content
                if result["type"]:
                    # result["namespace"] = current_namespace
                    if result["type"] == "stream":
                        if result.get("content") and result["content"].strip():
                            full_message_text += result["content"]
                            yield result
                    else:
                        yield result

            # Save final response
            if full_message_text:
                # For scheduling task
                yield {
                    "type": "final",
                    "namespace": current_namespace,
                }

                self._save_final_response(
                    response_message,
                    full_message_text,
                    role=current_namespace if current_namespace else "assistant",
                )
                # Update end checkpoint after saving final response
                self._update_message_checkpoint(
                    response_message, None, last_checkpoint_id
                )
                self.track_token_usage(
                    message_id=str(response_message.id),
                    input_token_count=input_token_usage,
                    output_token_count=output_token_usage,
                    model_id="us.anthropic.claude-3-5-sonnet-20240620-v2:0",
                    workspace_id=str(response_message.conversation.agent.workspace_id),
                )

            # Signal that title generation might be needed
            if need_title_generation:
                yield {
                    "type": "needs_title",
                    "conversation_id": str(conversation.id),
                }

        except Exception as e:
            self._cleanup_on_error(response_message, user_message)
            yield {"type": "error", "content": str(e), "message_id": None}

    async def process_message(
        self,
        conversation_id: uuid.UUID,
        message_content: str | None = None,
        message_action_type: str | None = None,
        resume: bool = False,
        approve: bool = False,
        restore: bool = False,
        restore_message_id: uuid.UUID | None = None,
        user_id: uuid.UUID | None = None,
    ) -> dict:
        """Process a message and return the final response content.
        Returns:
            dict: The final processed message content

        Raises:
            Exception: If processing fails or conversation is invalid
        """
        final_content = {}

        try:
            # Process the message using the stream method
            event_stream = self.process_message_stream(
                conversation_id=conversation_id,
                message_content=message_content,
                message_action_type=message_action_type,
                resume=resume,
                approve=approve,
                restore=restore,
                restore_message_id=restore_message_id,
                user_id=user_id,
            )

            # Process stream events
            async def collect_final():
                async for event in event_stream:
                    if (
                        event.get("type") == "final"
                        or event.get("type") == "interrupt"
                        or event.get("type") == "error"
                    ):
                        final_content = event

                return final_content

            # Run async collection synchronously
            final_content = await collect_final()

            return final_content

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            raise

    def _handle_namespace_change(
        self, input: NamespaceChangeInput
    ) -> NamespaceChangeOutput:
        """Handle namespace changes in the event stream.

        Args:
            input (NamespaceChangeInput): Input schema for namespace change event

        Returns:
            NamespaceChangeOutput: Output schema for namespace change event
        """
        namespace = (
            input.event["metadata"].get("langgraph_checkpoint_ns", "").split(":")[0]
        )

        if namespace not in input.agents_config.agents.keys():
            return NamespaceChangeOutput(
                namespace=input.current_namespace,
                response_message=input.response_message,
                response_text=input.response_text,
                full_message_text=input.full_message_text,
                current_thought=input.current_thought,
                first_checkpoint_id=input.first_checkpoint_id,
                last_checkpoint_id=input.last_checkpoint_id,
                current_thought_idx=input.current_thought_idx,
                input_token_usage=input.input_token_usage,
                output_token_usage=input.output_token_usage,
            )

        if input.current_namespace is not None and namespace != input.current_namespace:
            self._save_final_response(
                input.response_message, input.full_message_text, input.current_namespace
            )
            self._update_message_checkpoint(
                input.response_message, None, input.last_checkpoint_id
            )
            self.track_token_usage(
                message_id=str(input.response_message.id),
                input_token_count=input.input_token_usage,
                output_token_count=input.output_token_usage,
                model_id="us.anthropic.claude-3-5-sonnet-20240620-v2:0",
                workspace_id=str(
                    input.response_message.conversation.agent.workspace_id
                ),
            )
            input_token_usage, output_token_usage = 0, 0

            response_message = self._create_message(
                input.conversation_id, "", namespace
            )
            return NamespaceChangeOutput(
                namespace=namespace,
                response_message=response_message,
                response_text="",
                full_message_text="",
                current_thought=None,
                first_checkpoint_id=None,
                last_checkpoint_id=None,
                current_thought_idx=0,
                input_token_usage=input_token_usage,
                output_token_usage=output_token_usage,
            )

        return NamespaceChangeOutput(
            namespace=namespace,
            response_message=input.response_message,
            response_text=input.response_text,
            full_message_text=input.full_message_text,
            current_thought=input.current_thought,
            first_checkpoint_id=input.first_checkpoint_id,
            last_checkpoint_id=input.last_checkpoint_id,
            current_thought_idx=input.current_thought_idx,
            input_token_usage=input.input_token_usage,
            output_token_usage=input.output_token_usage,
        )

    def get_conversation_history(
        self, conversation_id: uuid.UUID, limit: int = 200
    ) -> dict:
        """Get conversation history with agent thoughts."""
        return self.message_handler.get_conversation_history(conversation_id, limit)
