import json
import time
import uuid
from typing import Any

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import (
    AnyMessage,
    HumanMessage,
    SystemMessage,
    convert_to_openai_messages,
)
from llama_index.core import Document, VectorStoreIndex
from llama_index.embeddings.bedrock import BedrockEmbedding
from llama_index.llms.bedrock import Bedrock
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import models as qdrant_models
from qdrant_client.models import Distance
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.core.langfuse import langfuse_handler
from app.core.qdrant import aclient, client
from app.logger import logger
from app.modules.multi_agents import AgentFactory
from app.modules.multi_agents.core.utils import load_chat_model
from app.repositories.conversation import ConversationRepository
from app.services.memory.prompt import EXTRACTION_DECISION_PROMPT, EXTRACTION_PROMPT
from app.services.memory.schema import ExtractionDecision, MemoryEvolution, MemoryNode
from app.services.memory.template import get_template

MAX_EMBEDDING_RETRIES = 3
RETRY_DELAY = 2  # seconds


class MemoryService:
    def __init__(self, session: AsyncSession | None = None):
        self.session = session
        self.aqdrant_client = aclient
        self.qdrant_client = client
        self.conversation_repo = ConversationRepository(async_session=self.session)
        self.embed_model = BedrockEmbedding(
            model_name=settings.EMBEDDING_MODEL_NAME,
            region_name=settings.BEDROCK_REGION_NAME,
        )

    async def _get_vector_store(self, collection_name: str):
        # Get or create vector store for a collection
        try:
            if not await self.aqdrant_client.collection_exists(collection_name):
                await self.aqdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config={
                        "text-dense": qdrant_models.VectorParams(
                            size=settings.EMBEDDING_MODEL_DIMS,
                            distance=Distance.COSINE,
                        ),
                    },
                    # sparse_vectors_config={
                    #     "text-sparse": qdrant_models.SparseVectorParams(
                    #         index=qdrant_models.SparseIndexParams(),
                    #         modifier=qdrant_models.Modifier.IDF,
                    #     ),
                    # },
                )
            return QdrantVectorStore(
                collection_name=collection_name,
                client=self.qdrant_client,
                aclient=self.aqdrant_client,
                parallel=10,
                # enable_hybrid=True,
                # fastembed_sparse_model="Qdrant/bm25",
                dense_vector_name="text-dense",
            )
        except Exception as e:
            logger.error(
                f"Error with vector store collection '{collection_name}': {str(e)}"
            )
            raise

    async def _generate_embedding_with_retry(self, text: str) -> list[float]:
        # Generate embedding with retry logic for transient failures
        retries = 0
        last_exception = None

        while retries < MAX_EMBEDDING_RETRIES:
            try:
                return await self.embed_model.aget_text_embedding(text)
            except Exception as e:
                retries += 1
                last_exception = e
                logger.warning(
                    f"Embedding generation failed (attempt {retries}/{MAX_EMBEDDING_RETRIES}): {str(e)}"
                )
                if retries < MAX_EMBEDDING_RETRIES:
                    # Exponential backoff
                    delay = RETRY_DELAY * (2 ** (retries - 1))
                    logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)

        # All retries failed
        logger.error(f"All embedding retries failed: {str(last_exception)}")
        raise last_exception

    async def _should_extract_learnings(self, user_message: HumanMessage) -> bool:
        # Determine if conversation is worth extracting learnings from
        try:
            model = load_chat_model(settings.KEY_LEARNING_DECISION_MODEL)
            prompt = [
                SystemMessage(content=EXTRACTION_DECISION_PROMPT),
                HumanMessage(content=user_message.content),
            ]
            response = await model.with_structured_output(ExtractionDecision).ainvoke(
                prompt,
                config={
                    "run_name": "Extraction Decision",
                    "callbacks": [langfuse_handler],
                },
            )
            should_extract = response.should_extract
            logger.debug(f"Extraction decision: {should_extract}")
            return should_extract
        except Exception as e:
            logger.error(f"Error in extraction decision: {str(e)}")
            return False  # Default to not extracting on error

    async def _get_messages_from_checkpoint(
        self, thread_id: str
    ) -> tuple[list[dict], HumanMessage | None]:
        try:
            # Initialize AgentFactory if needed
            if AgentFactory._checkpointer is None:
                logger.debug("AgentFactory not initialized. Initializing now...")
                await AgentFactory.initialize()

            # Get latest assistant message
            latest_assistant_message = (
                await self.conversation_repo.get_last_assistant_message(
                    uuid.UUID(thread_id)
                )
            )

            checkpoint_id = None
            if latest_assistant_message:
                checkpoint = await self.conversation_repo.get_checkpoint_by_message_id(
                    latest_assistant_message.id
                )

                if checkpoint and checkpoint.end_checkpoint_id:
                    checkpoint_id = checkpoint.end_checkpoint_id

            # Configure checkpoint retrieval
            config = {
                "configurable": {
                    "thread_id": thread_id,
                    "checkpoint_id": checkpoint_id,
                }
            }
            checkpoint = await AgentFactory._checkpointer.aget(config)

            # Get agent states from checkpoint
            all_agent_states = checkpoint["channel_values"]["instance_states"]

            # Group messages by agent role
            messages_by_role = {}
            for role, agent_state in all_agent_states.items():
                if hasattr(agent_state, "messages"):
                    messages = agent_state.messages
                    if messages:
                        messages_by_role[role] = messages

            # Get latest user message
            latest_user_message = await self.conversation_repo.get_latest_user_message(
                uuid.UUID(thread_id)
            )

            return messages_by_role, latest_user_message

        except Exception as e:
            logger.error(f"Error in checkpoint retrieval: {e}")
            return [], None

    async def extract_key_learnings_by_role(
        self, conversation_id: uuid.UUID
    ) -> dict[str, Any]:
        # Extract key learnings from conversation for each agent role
        try:
            # Get conversation workspace info
            conversation = await self.conversation_repo.async_get_conversation(
                conversation_id
            )
            workspace_id = conversation.agent.workspace_id

            # Get messages from checkpoint
            (
                messages_by_role,
                latest_user_message,
            ) = await self._get_messages_from_checkpoint(str(conversation_id))

            if not messages_by_role:
                logger.error(
                    f"No messages by role found for conversation {conversation_id}"
                )
                return {"success": False, "error": "No messages found"}

            # Decide if conversation is worth extracting from
            should_extract = False
            if latest_user_message is not None:
                should_extract = await self._should_extract_learnings(
                    latest_user_message
                )
            else:
                # Check if any role has substantive content
                for role, messages in messages_by_role.items():
                    if len(messages) > 3:  # Threshold for sufficient message volume
                        should_extract = True
                        logger.debug(
                            f"Found {len(messages)} messages for {role}, proceeding with extraction"
                        )
                        break

            if not should_extract:
                logger.debug("Skipping extraction - not substantive content")
                return {
                    "success": True,
                    "skipped": True,
                    "reason": "Conversation lacks substantive content",
                }

            # Process each agent role
            for role, messages in messages_by_role.items():
                logger.info(f"Processing role: {role} with {len(messages)} messages")

                if (
                    not messages or len(messages) < 2
                ):  # Skip roles with too few messages
                    logger.debug(f"Skipping role {role} - insufficient content")
                    continue

                collection_name = get_template(
                    workspace_id=str(workspace_id),
                    agent_role=role,
                )
                vector_store = await self._get_vector_store(collection_name)

                if not vector_store:
                    logger.error(
                        f"Failed to create vector store for collection {collection_name}"
                    )
                    continue

                model = load_chat_model(settings.KEY_LEARNING_DECISION_MODEL)

                # Step 1: Extract memory node
                current_memory_node = await self._extract_memory_node(model, messages)
                logger.info(
                    f"Extracted memory node: {current_memory_node.model_dump_json(indent=2)}"
                )

                # Step 2: Prepare for vector store
                current_memory_node_json = current_memory_node.model_dump_json()
                current_vector_store_node = Document(
                    text=current_memory_node_json,
                    metadata={"role": role},
                )
                current_vector_store_node.embedding = (
                    await self._generate_embedding_with_retry(current_memory_node_json)
                )

                # Step 3: Find related memory nodes
                index = VectorStoreIndex.from_vector_store(
                    vector_store=vector_store,
                    embed_model=self.embed_model,
                )
                query_engine = index.as_query_engine(
                    llm=Bedrock(
                        model=settings.SEARCH_MODEL_LLAMAINDEX,
                        region_name=settings.BEDROCK_REGION_NAME,
                    ),
                    similarity_top_k=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
                    similarity_cutoff=settings.MEMORY_RELATEDNESS_THRESHOLD,
                )
                related_memory_nodes = query_engine.retrieve(current_memory_node_json)
                logger.info(f"Found {len(related_memory_nodes)} related memory nodes")

                # Step 4: Analyze memory evolution
                nodes_to_add = []
                nodes_to_delete = []

                new_uuid = str(uuid.uuid4())

                if related_memory_nodes and len(related_memory_nodes) > 0:
                    neighbor_count = len(related_memory_nodes)
                    evolution_inputs = [
                        SystemMessage(
                            content=f"""You are analyzing if a memory node should evolve based on its relationship with {neighbor_count} related memory nodes."""
                        ),
                        HumanMessage(
                            content=f"""Current memory node:
                            id: {new_uuid}
                            {current_memory_node_json}\n\nRelated memory nodes: {[f'{node.node_id} - {node.text}' for node in related_memory_nodes]}"""
                        ),
                    ]
                    memory_evolution = await model.with_structured_output(
                        MemoryEvolution
                    ).ainvoke(
                        evolution_inputs,
                        config={
                            "run_name": "Memory Evolution",
                            "callbacks": [langfuse_handler],
                        },
                    )
                    logger.info(
                        f"Evolution decision: should_evolve={memory_evolution.should_evolve}, type={memory_evolution.evolution_type}"
                    )

                    if memory_evolution.should_evolve:
                        if memory_evolution.evolution_type == "combine":
                            logger.info("Processing combine evolution")
                            # Handle node combination
                            if memory_evolution.combined_node:
                                # Add the combined node
                                combined_node_json = memory_evolution.combined_node.new_node.model_dump_json()
                                combined_vector_store_node = Document(
                                    text=combined_node_json,
                                    metadata={"role": role},
                                )
                                combined_vector_store_node.embedding = (
                                    await self._generate_embedding_with_retry(
                                        combined_node_json
                                    )
                                )
                                nodes_to_add.append(combined_vector_store_node)

                                # Delete the nodes that were combined
                                nodes_to_delete.extend(
                                    memory_evolution.combined_node.combined_from
                                )
                                logger.info(
                                    f"Combined {len(memory_evolution.combined_node.combined_from)} nodes into one"
                                )

                        elif memory_evolution.evolution_type == "connect":
                            logger.info("Processing connect evolution")
                            # Handle node connection
                            if memory_evolution.connections:
                                current_memory_node.links = memory_evolution.connections
                                updated_node_json = (
                                    current_memory_node.model_dump_json()
                                )
                                current_vector_store_node = Document(
                                    doc_id=new_uuid,
                                    text=updated_node_json,
                                    metadata={
                                        "role": role,
                                        "links": memory_evolution.connections,
                                    },
                                )
                                current_vector_store_node.embedding = (
                                    await self._generate_embedding_with_retry(
                                        updated_node_json
                                    )
                                )
                                nodes_to_add.append(current_vector_store_node)

                        else:  # standalone
                            nodes_to_add.append(current_vector_store_node)
                    else:
                        nodes_to_add.append(current_vector_store_node)
                else:
                    nodes_to_add.append(current_vector_store_node)

                # Step 5: Update vector store
                logger.info(
                    f"Updating vector store: adding {len(nodes_to_add)} nodes, deleting {len(nodes_to_delete)} nodes"
                )
                await vector_store.async_add(nodes_to_add)
                await vector_store.adelete_nodes(node_ids=nodes_to_delete)

            return {"success": True}

        except Exception as e:
            logger.error(f"Error extracting key learnings: {str(e)}")
            logger.exception("Extraction error details:")
            return {"success": False, "error": str(e)}

    async def _extract_memory_node(
        self, model: BaseChatModel, messages: list[AnyMessage]
    ) -> MemoryNode:
        try:
            # Parse messages into a string
            oai_messages = convert_to_openai_messages(messages)
            messages_str = "\n".join(
                [f"{msg['role']}: {msg['content']}" for msg in oai_messages]
            )

            extraction_messages = [
                {"role": "system", "content": EXTRACTION_PROMPT},
                {"role": "user", "content": messages_str},
            ]

            model_with_structured_output = model.with_structured_output(MemoryNode)
            memory_node = await model_with_structured_output.ainvoke(
                extraction_messages,
                config={
                    "run_name": "Memory Node Extraction",
                    "callbacks": [langfuse_handler],
                },
            )
            logger.debug(f"Extracted memory node with tags: {memory_node.tags}")
            return memory_node
        except Exception as e:
            logger.error(f"Error extracting learning: {str(e)}")
            raise e

    async def search_memory(self, query: str, role: str, workspace_id: str) -> str:
        collection_name = get_template(
            workspace_id=workspace_id,
            agent_role=role,
        )
        logger.info(
            f"Searching memory collection: {collection_name} with query: {query}"
        )
        vector_store = await self._get_vector_store(collection_name)

        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store,
            embed_model=self.embed_model,
        )

        # 1. Simple query engine
        query_engine = index.as_query_engine(
            llm=Bedrock(
                model=settings.SEARCH_MODEL_LLAMAINDEX,
                region_name=settings.BEDROCK_REGION_NAME,
            ),
            similarity_top_k=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
            similarity_cutoff=settings.MEMORY_RELATEDNESS_THRESHOLD,
        )
        nodes = query_engine.retrieve(query)
        logger.info(f"Found {len(nodes)} related memory nodes")

        # 2. Query fusion retriever
        # retriever = QueryFusionRetriever(
        #     [index.as_retriever()],
        #     llm=Bedrock(
        #         model=settings.SEARCH_MODEL_LLAMAINDEX,
        #         region_name=settings.BEDROCK_REGION_NAME,
        #     ),
        #     similarity_top_k=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
        #     num_queries=3,
        #     verbose=True,
        # )
        # nodes = await retriever.aretrieve(query)

        if len(nodes) == 0:
            return "No related memory nodes found"

        # Format the results
        results = ""
        for index, node in enumerate(nodes):
            # Filter out nodes with score below threshold
            if node.get_score() < settings.MEMORY_RELATEDNESS_THRESHOLD:
                continue
            score = node.get_score()
            node_data = json.loads(node.text)
            result = f"""Memory {index + 1}.\nScore: {score}\nTags: {node_data.get("tags", "No tags")}\n
Task: {node_data.get("task", "No task")}\n
Solution: {node_data.get("solution", "No solution")}\n
"""
            results += result

        if results == "":
            return "No related memory nodes found"

        return results

    @staticmethod
    def get_mfu_memories_prompt(workspace_id: str, agent_role: str) -> str:
        collection_name = get_template(
            workspace_id=workspace_id,
            agent_role=agent_role,
        )
        logger.debug(f"Getting MFU memories for collection: {collection_name}")

        # Check collection exists
        if not client.collection_exists(collection_name):
            return f"{agent_role} has no memories yet, please dont call this tool in this conversation."

        nodes = client.scroll(
            collection_name=collection_name,
            limit=10,
            with_payload=True,
            with_vectors=False,
        )
        mfu_memories = "Here are the most frequent used tasks, you can use the search_memory tool to get more information about the solution:\n"
        for node in nodes[0]:
            node_data = json.loads(node.payload.get("_node_content"))["text_resource"][
                "text"
            ]
            node_data = json.loads(node_data)
            mfu_memories += f"- {node_data.get('task', 'No task')}, Tags: {node_data.get('tags', 'No tags')}\n"

        logger.debug(f"MFU memories prompt: {mfu_memories}")

        return mfu_memories
